<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>波形图测试</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #e8f5e8;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 波形图功能测试</h1>
        
        <div class="status" id="status">
            准备测试波形图功能...
        </div>
        
        <div>
            <button onclick="testBasicChart()">测试基础图表</button>
            <button onclick="testHeartRateChart()">测试心率波形</button>
            <button onclick="testBloodPressureChart()">测试血压波形</button>
            <button onclick="testRealTimeUpdate()">测试实时更新</button>
            <button onclick="clearChart()">清除图表</button>
        </div>
        
        <div class="chart-container">
            <canvas id="testChart"></canvas>
        </div>
        
        <div id="debugInfo" style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 20px;">
            <h3>调试信息</h3>
            <div id="debugContent">等待测试...</div>
        </div>
    </div>

    <script>
        let testChart = null;
        let updateInterval = null;
        
        // 更新状态信息
        function updateStatus(message, isError = false) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.style.background = isError ? '#ffebee' : '#e8f5e8';
            statusEl.style.color = isError ? '#d32f2f' : '#2e7d32';
            console.log(isError ? '❌' : '✅', message);
        }
        
        // 更新调试信息
        function updateDebugInfo(info) {
            document.getElementById('debugContent').innerHTML = info;
        }
        
        // 测试基础图表
        function testBasicChart() {
            updateStatus('测试基础图表...');
            
            try {
                // 检查Chart.js
                if (typeof Chart === 'undefined') {
                    throw new Error('Chart.js未加载');
                }
                
                // 清除旧图表
                if (testChart) {
                    testChart.destroy();
                    testChart = null;
                }
                
                // 获取画布
                const canvas = document.getElementById('testChart');
                if (!canvas) {
                    throw new Error('画布元素不存在');
                }
                
                const ctx = canvas.getContext('2d');
                
                // 创建基础图表
                testChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['1分钟前', '2分钟前', '3分钟前', '4分钟前', '现在'],
                        datasets: [{
                            label: '测试数据',
                            data: [10, 20, 15, 25, 30],
                            borderColor: '#007bff',
                            backgroundColor: '#007bff20',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true
                            }
                        }
                    }
                });
                
                updateStatus('基础图表创建成功！');
                updateDebugInfo(`
                    <strong>基础图表测试结果：</strong><br>
                    Chart.js版本: ${Chart.version}<br>
                    图表类型: line<br>
                    数据点数: 5<br>
                    状态: 成功创建
                `);
                
            } catch (error) {
                updateStatus('基础图表测试失败: ' + error.message, true);
                updateDebugInfo(`<strong>错误:</strong> ${error.message}`);
            }
        }
        
        // 测试心率波形
        function testHeartRateChart() {
            updateStatus('测试心率波形...');
            
            try {
                if (testChart) {
                    testChart.destroy();
                    testChart = null;
                }
                
                // 生成心率数据
                const timeLabels = [];
                const heartRateData = [];
                const now = new Date();
                
                for (let i = 29; i >= 0; i--) {
                    const time = new Date(now.getTime() - i * 60000);
                    timeLabels.push(time.toLocaleTimeString().slice(0, 5));
                    
                    const baseHR = 72 + (Math.random() - 0.5) * 8;
                    heartRateData.push(Math.round(Math.max(65, Math.min(85, baseHR))));
                }
                
                const canvas = document.getElementById('testChart');
                const ctx = canvas.getContext('2d');
                
                testChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: timeLabels,
                        datasets: [{
                            label: '心率 (次/分)',
                            data: heartRateData,
                            borderColor: '#e74c3c',
                            backgroundColor: '#e74c3c15',
                            tension: 0.4,
                            fill: true,
                            pointRadius: 2,
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: '时间'
                                }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: '心率 (次/分)'
                                },
                                min: 60,
                                max: 90
                            }
                        }
                    }
                });
                
                updateStatus('心率波形创建成功！');
                updateDebugInfo(`
                    <strong>心率波形测试结果：</strong><br>
                    数据点数: ${heartRateData.length}<br>
                    时间范围: 过去30分钟<br>
                    数值范围: ${Math.min(...heartRateData)} - ${Math.max(...heartRateData)} 次/分<br>
                    平均心率: ${Math.round(heartRateData.reduce((a,b) => a+b) / heartRateData.length)} 次/分<br>
                    状态: 成功创建
                `);
                
            } catch (error) {
                updateStatus('心率波形测试失败: ' + error.message, true);
                updateDebugInfo(`<strong>错误:</strong> ${error.message}`);
            }
        }
        
        // 测试血压波形
        function testBloodPressureChart() {
            updateStatus('测试血压波形...');
            
            try {
                if (testChart) {
                    testChart.destroy();
                    testChart = null;
                }
                
                // 生成血压数据
                const timeLabels = [];
                const systolicData = [];
                const diastolicData = [];
                const now = new Date();
                
                for (let i = 29; i >= 0; i--) {
                    const time = new Date(now.getTime() - i * 60000);
                    timeLabels.push(time.toLocaleTimeString().slice(0, 5));
                    
                    const baseSys = 120 + (Math.random() - 0.5) * 10;
                    const baseDia = 80 + (Math.random() - 0.5) * 8;
                    
                    systolicData.push(Math.round(Math.max(110, Math.min(135, baseSys))));
                    diastolicData.push(Math.round(Math.max(70, Math.min(90, baseDia))));
                }
                
                const canvas = document.getElementById('testChart');
                const ctx = canvas.getContext('2d');
                
                testChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: timeLabels,
                        datasets: [{
                            label: '收缩压 (mmHg)',
                            data: systolicData,
                            borderColor: '#8e44ad',
                            backgroundColor: '#8e44ad15',
                            tension: 0.4,
                            fill: false,
                            pointRadius: 2,
                            borderWidth: 2
                        }, {
                            label: '舒张压 (mmHg)',
                            data: diastolicData,
                            borderColor: '#9b59b6',
                            backgroundColor: '#9b59b615',
                            tension: 0.4,
                            fill: false,
                            pointRadius: 2,
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: '时间'
                                }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: '血压 (mmHg)'
                                },
                                min: 60,
                                max: 150
                            }
                        }
                    }
                });
                
                updateStatus('血压波形创建成功！');
                updateDebugInfo(`
                    <strong>血压波形测试结果：</strong><br>
                    数据点数: ${systolicData.length}<br>
                    收缩压范围: ${Math.min(...systolicData)} - ${Math.max(...systolicData)} mmHg<br>
                    舒张压范围: ${Math.min(...diastolicData)} - ${Math.max(...diastolicData)} mmHg<br>
                    平均血压: ${Math.round(systolicData.reduce((a,b) => a+b) / systolicData.length)}/${Math.round(diastolicData.reduce((a,b) => a+b) / diastolicData.length)} mmHg<br>
                    状态: 成功创建
                `);
                
            } catch (error) {
                updateStatus('血压波形测试失败: ' + error.message, true);
                updateDebugInfo(`<strong>错误:</strong> ${error.message}`);
            }
        }
        
        // 测试实时更新
        function testRealTimeUpdate() {
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
                updateStatus('实时更新已停止');
                return;
            }
            
            updateStatus('开始实时更新测试...');
            
            if (!testChart) {
                testHeartRateChart();
            }
            
            updateInterval = setInterval(() => {
                if (!testChart) return;
                
                // 添加新数据点
                const now = new Date();
                const newTime = now.toLocaleTimeString().slice(0, 5);
                const newHeartRate = Math.round(72 + (Math.random() - 0.5) * 8);
                
                testChart.data.labels.push(newTime);
                testChart.data.datasets[0].data.push(newHeartRate);
                
                // 限制数据点数量
                if (testChart.data.labels.length > 30) {
                    testChart.data.labels.shift();
                    testChart.data.datasets[0].data.shift();
                }
                
                testChart.update('none');
                
                updateStatus(`实时更新中... 当前心率: ${newHeartRate} 次/分`);
                
            }, 2000);
        }
        
        // 清除图表
        function clearChart() {
            if (testChart) {
                testChart.destroy();
                testChart = null;
            }
            
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }
            
            updateStatus('图表已清除');
            updateDebugInfo('图表已清除，可以重新测试');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('波形图测试系统已就绪');
            updateDebugInfo(`
                <strong>系统信息：</strong><br>
                Chart.js状态: ${typeof Chart !== 'undefined' ? '已加载 (v' + Chart.version + ')' : '未加载'}<br>
                画布元素: ${document.getElementById('testChart') ? '存在' : '不存在'}<br>
                浏览器: ${navigator.userAgent.split(' ').pop()}<br>
                准备状态: 就绪
            `);
        });
    </script>
</body>
</html>
