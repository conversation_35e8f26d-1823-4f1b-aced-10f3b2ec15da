<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强型医学生理监测系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            color: #333;
            font-size: 2.2em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .btn.active {
            background: linear-gradient(45deg, #2e7d32, #4caf50);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .vital-signs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .vital-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            position: relative;
        }

        .vital-card:hover {
            transform: translateY(-3px);
        }

        .vital-card .icon {
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .vital-card .value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .vital-card .label {
            color: #666;
            font-size: 1em;
            margin-bottom: 8px;
        }

        .vital-card .range {
            color: #999;
            font-size: 0.8em;
        }

        .vital-card .status {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
        }

        .vital-card.abnormal .status {
            background: #f44336;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        /* 各生理参数的颜色主题 */
        .heart-rate .icon { color: #e74c3c; }
        .heart-rate .value { color: #e74c3c; }

        .blood-pressure .icon { color: #8e44ad; }
        .blood-pressure .value { color: #8e44ad; }

        .spo2 .icon { color: #3498db; }
        .spo2 .value { color: #3498db; }

        .temperature .icon { color: #f39c12; }
        .temperature .value { color: #f39c12; }

        .respiratory-rate .icon { color: #27ae60; }
        .respiratory-rate .value { color: #27ae60; }

        .glucose .icon { color: #e67e22; }
        .glucose .value { color: #e67e22; }

        .ecg .icon { color: #34495e; }
        .ecg .value { color: #34495e; }

        .stress-index .icon { color: #9b59b6; }
        .stress-index .value { color: #9b59b6; }

        .charts-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .main-chart {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .main-chart h3 {
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
        }

        .stats-panel {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stats-panel h3 {
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
        }

        .stat-value {
            font-weight: bold;
            color: #333;
        }

        .status-bar {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-indicator.active {
            background: #4caf50;
        }

        .status-indicator.inactive {
            background: #ccc;
            animation: none;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pulse-animation {
            animation: heartbeat 1s infinite;
        }

        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .individual-charts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .individual-chart {
            background: white;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .individual-chart h4 {
            margin-bottom: 10px;
            color: #333;
            text-align: center;
            font-size: 1em;
        }

        .individual-chart-wrapper {
            position: relative;
            height: 180px;
        }

        @media (max-width: 768px) {
            .vital-signs-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .charts-container {
                grid-template-columns: 1fr;
            }
            
            .individual-charts {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 增强型医学生理监测系统</h1>
            <p>实时监测多种生理参数，提供专业医学级数据分析</p>
        </div>

        <div class="status-bar">
            <div>
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">系统已就绪</span>
            </div>
            <div>
                <span>数据点: <strong id="dataCount">0</strong></span>
                <span style="margin-left: 20px;">运行时间: <strong id="runTime">00:00</strong></span>
            </div>
        </div>

        <div class="control-panel">
            <button class="btn" id="startBtn" onclick="startSimulation()">开始监测</button>
            <button class="btn" id="stopBtn" onclick="stopSimulation()" disabled>停止监测</button>
            <button class="btn" onclick="resetData()">重置数据</button>
            <button class="btn" onclick="exportData()">导出数据</button>
            <button class="btn" onclick="toggleAlerts()">报警设置</button>
        </div>

        <div class="vital-signs-grid">
            <div class="vital-card heart-rate">
                <div class="status"></div>
                <div class="icon">💓</div>
                <div class="value" id="heartRate">--</div>
                <div class="label">心率 (次/分)</div>
                <div class="range">正常: 60-100</div>
            </div>
            <div class="vital-card blood-pressure">
                <div class="status"></div>
                <div class="icon">🩸</div>
                <div class="value" id="bloodPressure">--/--</div>
                <div class="label">血压 (mmHg)</div>
                <div class="range">正常: 90-140/60-90</div>
            </div>
            <div class="vital-card spo2">
                <div class="status"></div>
                <div class="icon">🫁</div>
                <div class="value" id="spo2">--</div>
                <div class="label">血氧饱和度 (%)</div>
                <div class="range">正常: 95-100</div>
            </div>
            <div class="vital-card temperature">
                <div class="status"></div>
                <div class="icon">🌡️</div>
                <div class="value" id="temperature">--</div>
                <div class="label">体温 (°C)</div>
                <div class="range">正常: 36.1-37.2</div>
            </div>
            <div class="vital-card respiratory-rate">
                <div class="status"></div>
                <div class="icon">🌬️</div>
                <div class="value" id="respiratoryRate">--</div>
                <div class="label">呼吸频率 (次/分)</div>
                <div class="range">正常: 12-20</div>
            </div>
            <div class="vital-card glucose">
                <div class="status"></div>
                <div class="icon">🍯</div>
                <div class="value" id="glucose">--</div>
                <div class="label">血糖 (mmol/L)</div>
                <div class="range">正常: 3.9-6.1</div>
            </div>
            <div class="vital-card ecg">
                <div class="status"></div>
                <div class="icon">📈</div>
                <div class="value" id="ecgQT">--</div>
                <div class="label">QT间期 (ms)</div>
                <div class="range">正常: 350-450</div>
            </div>
            <div class="vital-card stress-index">
                <div class="status"></div>
                <div class="icon">🧠</div>
                <div class="value" id="stressIndex">--</div>
                <div class="label">压力指数</div>
                <div class="range">正常: 0-30</div>
            </div>
        </div>

        <div class="charts-container">
            <div class="main-chart">
                <h3>📈 综合生理参数监测</h3>
                <div class="chart-wrapper">
                    <canvas id="mainChart"></canvas>
                </div>
            </div>

            <div class="stats-panel">
                <h3>📊 实时统计</h3>
                <div class="stat-item">
                    <span class="stat-label">平均心率</span>
                    <span class="stat-value" id="avgHeartRate">--</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">平均血压</span>
                    <span class="stat-value" id="avgBloodPressure">--</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">平均血氧</span>
                    <span class="stat-value" id="avgSpO2">--</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">平均体温</span>
                    <span class="stat-value" id="avgTemperature">--</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">平均呼吸</span>
                    <span class="stat-value" id="avgRespiratory">--</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">平均血糖</span>
                    <span class="stat-value" id="avgGlucose">--</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">当前状态</span>
                    <span class="stat-value" id="currentState">正常</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">异常指标</span>
                    <span class="stat-value" id="abnormalCount">0</span>
                </div>
            </div>
        </div>

        <div class="individual-charts">
            <div class="individual-chart">
                <h4>💓 心率变异性</h4>
                <div class="individual-chart-wrapper">
                    <canvas id="heartRateChart"></canvas>
                </div>
            </div>
            <div class="individual-chart">
                <h4>🩸 血压趋势</h4>
                <div class="individual-chart-wrapper">
                    <canvas id="bloodPressureChart"></canvas>
                </div>
            </div>
            <div class="individual-chart">
                <h4>🫁 血氧监测</h4>
                <div class="individual-chart-wrapper">
                    <canvas id="spo2Chart"></canvas>
                </div>
            </div>
            <div class="individual-chart">
                <h4>🌡️ 体温监测</h4>
                <div class="individual-chart-wrapper">
                    <canvas id="temperatureChart"></canvas>
                </div>
            </div>
            <div class="individual-chart">
                <h4>🌬️ 呼吸模式</h4>
                <div class="individual-chart-wrapper">
                    <canvas id="respiratoryChart"></canvas>
                </div>
            </div>
            <div class="individual-chart">
                <h4>🍯 血糖波动</h4>
                <div class="individual-chart-wrapper">
                    <canvas id="glucoseChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 增强型医学生理数据模拟器
        class EnhancedMedicalSimulator {
            constructor() {
                this.isRunning = false;
                this.interval = null;
                this.startTime = null;
                this.dataCount = 0;
                
                // 基础生理参数
                this.heartRate = 72;
                this.systolicBP = 120;
                this.diastolicBP = 80;
                this.spo2 = 98;
                this.temperature = 36.5;
                this.respiratoryRate = 16;
                this.glucose = 5.0;
                this.qtInterval = 400;
                this.stressIndex = 15;
                
                // 生理状态影响因子
                this.stressLevel = 0;
                this.activityLevel = 0;
                this.fatigueLevel = 0;
                this.timeOfDay = 12; // 24小时制
                this.physiologicalState = 'normal'; // normal, sleep, active, stress
                
                // 数据存储
                this.maxDataPoints = 100;
                this.timeLabels = [];
                this.dataHistory = {
                    heartRate: [],
                    systolicBP: [],
                    diastolicBP: [],
                    spo2: [],
                    temperature: [],
                    respiratoryRate: [],
                    glucose: [],
                    qtInterval: [],
                    stressIndex: []
                };
                
                // 统计数据
                this.stats = {};
                this.resetStats();
                
                // 随机数种子
                this.randomSeed = 12345;
                
                this.initCharts();
            }
        }
        
        // 全局变量
        let simulator = new EnhancedMedicalSimulator();
        let charts = {};
        
            // 简单随机数生成器
            simpleRandom() {
                this.randomSeed = (this.randomSeed * ********** + 12345) & 0x7FFFFFFF;
                return this.randomSeed;
            }

            // 获取范围内随机数
            getRandomInRange(min, max) {
                return min + (this.simpleRandom() % (max - min + 1));
            }

            // 获取正态分布随机数（Box-Muller变换）
            getNormalRandom(mean, stdDev) {
                if (this.spare !== undefined) {
                    const temp = this.spare;
                    delete this.spare;
                    return temp * stdDev + mean;
                }

                const u = Math.random();
                const v = Math.random();
                const mag = stdDev * Math.sqrt(-2.0 * Math.log(u));
                this.spare = mag * Math.cos(2.0 * Math.PI * v);
                return mag * Math.sin(2.0 * Math.PI * v) + mean;
            }

            // 更新生理状态
            updatePhysiologicalState() {
                this.timeOfDay = (this.timeOfDay + 0.0167) % 24; // 每分钟增加0.0167小时

                // 根据时间调整生理状态
                if (this.timeOfDay >= 22 || this.timeOfDay <= 6) {
                    this.physiologicalState = 'sleep';
                    this.stressLevel = Math.max(0, this.stressLevel - 0.1);
                    this.activityLevel = 0;
                    this.fatigueLevel = Math.max(0, this.fatigueLevel - 0.05);
                } else if (this.timeOfDay >= 7 && this.timeOfDay <= 9) {
                    this.physiologicalState = 'active';
                    this.activityLevel = Math.min(10, this.activityLevel + 0.2);
                } else if (this.timeOfDay >= 18 && this.timeOfDay <= 21) {
                    this.physiologicalState = 'normal';
                    this.stressLevel = Math.max(0, this.stressLevel - 0.05);
                    this.activityLevel = Math.max(0, this.activityLevel - 0.1);
                } else {
                    this.physiologicalState = 'normal';
                }

                // 随机压力事件
                if (Math.random() < 0.02) { // 2%概率
                    this.stressLevel = Math.min(10, this.stressLevel + Math.random() * 3);
                    this.physiologicalState = 'stress';
                }
            }

            // 模拟心率（更真实的变异性）
            simulateHeartRate() {
                this.updatePhysiologicalState();

                let baseHR = 72;
                switch (this.physiologicalState) {
                    case 'sleep': baseHR = 55; break;
                    case 'normal': baseHR = 72; break;
                    case 'active': baseHR = 85; break;
                    case 'stress': baseHR = 95; break;
                }

                // 应用影响因子
                baseHR += this.stressLevel * 2;
                baseHR += this.activityLevel * 1.5;
                baseHR -= this.fatigueLevel * 0.5;

                // 添加心率变异性（HRV）- 正常人心率应有小幅波动
                const hrv = this.getNormalRandom(0, 2); // 标准差为2的正态分布
                this.heartRate = Math.round(baseHR + hrv);

                // 限制范围
                this.heartRate = Math.max(45, Math.min(150, this.heartRate));
                return this.heartRate;
            }

            // 模拟血压（考虑昼夜节律）
            simulateBloodPressure() {
                let baseSystolic = 120;
                let baseDiastolic = 80;

                // 昼夜节律影响
                const circadianFactor = Math.sin((this.timeOfDay - 6) * Math.PI / 12) * 5;
                baseSystolic += circadianFactor;
                baseDiastolic += circadianFactor * 0.6;

                // 生理状态影响
                switch (this.physiologicalState) {
                    case 'sleep':
                        baseSystolic -= 10;
                        baseDiastolic -= 5;
                        break;
                    case 'stress':
                        baseSystolic += 15;
                        baseDiastolic += 10;
                        break;
                    case 'active':
                        baseSystolic += 8;
                        baseDiastolic += 5;
                        break;
                }

                // 添加小幅随机波动（血压相对稳定）
                this.systolicBP = Math.round(baseSystolic + this.getNormalRandom(0, 3));
                this.diastolicBP = Math.round(baseDiastolic + this.getNormalRandom(0, 2));

                // 限制范围
                this.systolicBP = Math.max(90, Math.min(180, this.systolicBP));
                this.diastolicBP = Math.max(50, Math.min(110, this.diastolicBP));

                return { systolic: this.systolicBP, diastolic: this.diastolicBP };
            }

            // 模拟血氧饱和度（相对稳定）
            simulateSpO2() {
                let baseSpo2 = 98;

                // 生理状态轻微影响
                switch (this.physiologicalState) {
                    case 'sleep': baseSpo2 = 97; break;
                    case 'active': baseSpo2 = 99; break;
                    case 'stress': baseSpo2 = 97; break;
                }

                // 血氧应该非常稳定，只有小幅波动
                this.spo2 = Math.round(baseSpo2 + this.getNormalRandom(0, 0.5));
                this.spo2 = Math.max(94, Math.min(100, this.spo2));

                return this.spo2;
            }

            // 模拟体温（昼夜节律明显，波动小）
            simulateTemperature() {
                let baseTemp = 36.5;

                // 昼夜节律（体温在凌晨最低，下午最高）
                const circadianTemp = Math.sin((this.timeOfDay - 6) * Math.PI / 12) * 0.4;
                baseTemp += circadianTemp;

                // 生理状态影响
                switch (this.physiologicalState) {
                    case 'sleep': baseTemp -= 0.2; break;
                    case 'active': baseTemp += 0.1; break;
                    case 'stress': baseTemp += 0.2; break;
                }

                // 体温变化非常缓慢和稳定
                const targetTemp = baseTemp;
                this.temperature += (targetTemp - this.temperature) * 0.1; // 缓慢趋向目标
                this.temperature += this.getNormalRandom(0, 0.05); // 极小的随机波动

                // 限制范围
                this.temperature = Math.max(35.5, Math.min(38.5, this.temperature));
                return parseFloat(this.temperature.toFixed(1));
            }

            // 模拟呼吸频率
            simulateRespiratoryRate() {
                let baseRR = 16;

                switch (this.physiologicalState) {
                    case 'sleep': baseRR = 12; break;
                    case 'active': baseRR = 18; break;
                    case 'stress': baseRR = 22; break;
                }

                // 呼吸频率相对稳定
                this.respiratoryRate = Math.round(baseRR + this.getNormalRandom(0, 1.5));
                this.respiratoryRate = Math.max(8, Math.min(30, this.respiratoryRate));

                return this.respiratoryRate;
            }

            // 模拟血糖（餐后波动）
            simulateGlucose() {
                let baseGlucose = 5.0;

                // 模拟餐后血糖波动
                const mealTimes = [7, 12, 18]; // 早中晚餐时间
                let mealEffect = 0;

                for (const mealTime of mealTimes) {
                    const timeSinceMeal = Math.abs(this.timeOfDay - mealTime);
                    if (timeSinceMeal < 2) {
                        mealEffect += (2 - timeSinceMeal) * 1.5; // 餐后2小时内血糖升高
                    }
                }

                baseGlucose += mealEffect;

                // 缓慢变化
                const targetGlucose = baseGlucose;
                this.glucose += (targetGlucose - this.glucose) * 0.05;
                this.glucose += this.getNormalRandom(0, 0.1);

                this.glucose = Math.max(3.5, Math.min(8.0, this.glucose));
                return parseFloat(this.glucose.toFixed(1));
            }

            // 模拟QT间期
            simulateQTInterval() {
                let baseQT = 400;

                // 心率影响QT间期（心率快，QT短）
                baseQT = 400 - (this.heartRate - 72) * 1.5;

                this.qtInterval = Math.round(baseQT + this.getNormalRandom(0, 10));
                this.qtInterval = Math.max(320, Math.min(480, this.qtInterval));

                return this.qtInterval;
            }

            // 模拟压力指数
            simulateStressIndex() {
                let baseStress = 15;

                // 基于多个生理参数计算压力指数
                const hrStress = Math.max(0, (this.heartRate - 80) * 0.5);
                const bpStress = Math.max(0, (this.systolicBP - 130) * 0.3);
                const rrStress = Math.max(0, (this.respiratoryRate - 18) * 0.8);

                baseStress = hrStress + bpStress + rrStress + this.stressLevel * 2;

                this.stressIndex = Math.round(baseStress + this.getNormalRandom(0, 2));
                this.stressIndex = Math.max(0, Math.min(100, this.stressIndex));

                return this.stressIndex;
            }

            // 生成完整的生理数据
            generateVitalSigns() {
                const heartRate = this.simulateHeartRate();
                const bloodPressure = this.simulateBloodPressure();
                const spo2 = this.simulateSpO2();
                const temperature = this.simulateTemperature();
                const respiratoryRate = this.simulateRespiratoryRate();
                const glucose = this.simulateGlucose();
                const qtInterval = this.simulateQTInterval();
                const stressIndex = this.simulateStressIndex();

                const data = {
                    heartRate,
                    systolicBP: bloodPressure.systolic,
                    diastolicBP: bloodPressure.diastolic,
                    spo2,
                    temperature,
                    respiratoryRate,
                    glucose,
                    qtInterval,
                    stressIndex,
                    timestamp: new Date()
                };

                this.updateStats(data);
                return data;
            }

            // 更新统计数据
            updateStats(data) {
                Object.keys(data).forEach(key => {
                    if (key !== 'timestamp') {
                        if (!this.stats[key]) {
                            this.stats[key] = { sum: 0, count: 0, min: Infinity, max: -Infinity };
                        }

                        const stat = this.stats[key];
                        stat.sum += data[key];
                        stat.count++;
                        stat.min = Math.min(stat.min, data[key]);
                        stat.max = Math.max(stat.max, data[key]);
                    }
                });
            }

            // 重置统计数据
            resetStats() {
                this.stats = {};
            }

            // 检查异常值
            checkAbnormalValues(data) {
                const abnormal = [];

                if (data.heartRate < 60 || data.heartRate > 100) abnormal.push('心率');
                if (data.systolicBP < 90 || data.systolicBP > 140) abnormal.push('收缩压');
                if (data.diastolicBP < 60 || data.diastolicBP > 90) abnormal.push('舒张压');
                if (data.spo2 < 95) abnormal.push('血氧');
                if (data.temperature < 36.1 || data.temperature > 37.2) abnormal.push('体温');
                if (data.respiratoryRate < 12 || data.respiratoryRate > 20) abnormal.push('呼吸');
                if (data.glucose < 3.9 || data.glucose > 6.1) abnormal.push('血糖');
                if (data.qtInterval < 350 || data.qtInterval > 450) abnormal.push('QT间期');
                if (data.stressIndex > 30) abnormal.push('压力指数');

                return abnormal;
            }

            // 初始化图表
            initCharts() {
                // 图表配置将在后续添加
            }
        }

        // 初始化所有图表
        function initAllCharts() {
            const chartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    x: { display: true },
                    y: { display: true }
                },
                animation: { duration: 0 }
            };

            // 主图表
            const mainCtx = document.getElementById('mainChart').getContext('2d');
            charts.main = new Chart(mainCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        { label: '心率', data: [], borderColor: '#e74c3c', backgroundColor: 'rgba(231, 76, 60, 0.1)', yAxisID: 'y' },
                        { label: '收缩压', data: [], borderColor: '#8e44ad', backgroundColor: 'rgba(142, 68, 173, 0.1)', yAxisID: 'y1' },
                        { label: '血氧', data: [], borderColor: '#3498db', backgroundColor: 'rgba(52, 152, 219, 0.1)', yAxisID: 'y2' }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: { display: true, title: { display: true, text: '时间' } },
                        y: { type: 'linear', display: true, position: 'left', title: { display: true, text: '心率' }, min: 40, max: 120 },
                        y1: { type: 'linear', display: false, position: 'right', min: 80, max: 160 },
                        y2: { type: 'linear', display: false, position: 'right', min: 90, max: 100 }
                    },
                    plugins: { legend: { display: true, position: 'top' } },
                    animation: { duration: 0 }
                }
            });

            // 单独图表
            const singleChartConfig = (color, min, max) => ({
                type: 'line',
                data: { labels: [], datasets: [{ data: [], borderColor: color, backgroundColor: color + '20', tension: 0.4, fill: true }] },
                options: { ...chartOptions, scales: { ...chartOptions.scales, y: { min, max } } }
            });

            charts.heartRate = new Chart(document.getElementById('heartRateChart').getContext('2d'), singleChartConfig('#e74c3c', 40, 120));
            charts.bloodPressure = new Chart(document.getElementById('bloodPressureChart').getContext('2d'), singleChartConfig('#8e44ad', 80, 160));
            charts.spo2 = new Chart(document.getElementById('spo2Chart').getContext('2d'), singleChartConfig('#3498db', 90, 100));
            charts.temperature = new Chart(document.getElementById('temperatureChart').getContext('2d'), singleChartConfig('#f39c12', 35.5, 38.5));
            charts.respiratory = new Chart(document.getElementById('respiratoryChart').getContext('2d'), singleChartConfig('#27ae60', 8, 30));
            charts.glucose = new Chart(document.getElementById('glucoseChart').getContext('2d'), singleChartConfig('#e67e22', 3, 8));
        }

        // 开始模拟
        function startSimulation() {
            if (simulator.isRunning) return;

            simulator.isRunning = true;
            simulator.startTime = Date.now();
            simulator.dataCount = 0;

            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('stopBtn').classList.add('active');

            updateStatus('监测中', true);

            simulator.interval = setInterval(() => {
                const data = simulator.generateVitalSigns();
                simulator.dataCount++;

                updateDisplay(data);
                updateCharts(data);
                updateStats();
                updateDataCount(simulator.dataCount);
                updateRunTime(Date.now() - simulator.startTime);

                // 检查异常值
                const abnormal = simulator.checkAbnormalValues(data);
                updateAbnormalStatus(abnormal);
            }, 1000);
        }

        // 停止模拟
        function stopSimulation() {
            if (!simulator.isRunning) return;

            simulator.isRunning = false;
            clearInterval(simulator.interval);

            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('stopBtn').classList.remove('active');

            updateStatus('已停止', false);
        }

        // 重置数据
        function resetData() {
            stopSimulation();

            simulator.timeLabels = [];
            Object.keys(simulator.dataHistory).forEach(key => {
                simulator.dataHistory[key] = [];
            });
            simulator.dataCount = 0;
            simulator.resetStats();

            // 重置显示
            const elements = ['heartRate', 'bloodPressure', 'spo2', 'temperature', 'respiratoryRate', 'glucose', 'ecgQT', 'stressIndex'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.textContent = '--';
            });

            // 重置图表
            Object.values(charts).forEach(chart => {
                chart.data.labels = [];
                chart.data.datasets.forEach(dataset => dataset.data = []);
                chart.update();
            });

            updateStats();
            updateDataCount(0);
            updateRunTime(0);
            updateAbnormalStatus([]);
        }

        // 更新状态显示
        function updateStatus(text, isActive) {
            document.getElementById('statusText').textContent = text;
            const indicator = document.getElementById('statusIndicator');
            indicator.className = `status-indicator ${isActive ? 'active' : 'inactive'}`;
        }

        // 更新数据显示
        function updateDisplay(data) {
            document.getElementById('heartRate').textContent = data.heartRate;
            document.getElementById('bloodPressure').textContent = `${data.systolicBP}/${data.diastolicBP}`;
            document.getElementById('spo2').textContent = data.spo2;
            document.getElementById('temperature').textContent = data.temperature;
            document.getElementById('respiratoryRate').textContent = data.respiratoryRate;
            document.getElementById('glucose').textContent = data.glucose;
            document.getElementById('ecgQT').textContent = data.qtInterval;
            document.getElementById('stressIndex').textContent = data.stressIndex;

            // 添加心跳动画
            const heartRateCard = document.querySelector('.heart-rate');
            heartRateCard.classList.add('pulse-animation');
            setTimeout(() => heartRateCard.classList.remove('pulse-animation'), 1000);
        }

        // 更新图表
        function updateCharts(data) {
            const now = new Date().toLocaleTimeString();

            // 添加新数据
            simulator.timeLabels.push(now);
            simulator.dataHistory.heartRate.push(data.heartRate);
            simulator.dataHistory.systolicBP.push(data.systolicBP);
            simulator.dataHistory.spo2.push(data.spo2);
            simulator.dataHistory.temperature.push(data.temperature);
            simulator.dataHistory.respiratoryRate.push(data.respiratoryRate);
            simulator.dataHistory.glucose.push(data.glucose);

            // 限制数据点数量
            if (simulator.timeLabels.length > simulator.maxDataPoints) {
                simulator.timeLabels.shift();
                Object.keys(simulator.dataHistory).forEach(key => {
                    simulator.dataHistory[key].shift();
                });
            }

            // 更新主图表
            charts.main.data.labels = simulator.timeLabels;
            charts.main.data.datasets[0].data = simulator.dataHistory.heartRate;
            charts.main.data.datasets[1].data = simulator.dataHistory.systolicBP;
            charts.main.data.datasets[2].data = simulator.dataHistory.spo2;
            charts.main.update('none');

            // 更新单独图表
            const chartUpdates = [
                { chart: charts.heartRate, data: simulator.dataHistory.heartRate },
                { chart: charts.bloodPressure, data: simulator.dataHistory.systolicBP },
                { chart: charts.spo2, data: simulator.dataHistory.spo2 },
                { chart: charts.temperature, data: simulator.dataHistory.temperature },
                { chart: charts.respiratory, data: simulator.dataHistory.respiratoryRate },
                { chart: charts.glucose, data: simulator.dataHistory.glucose }
            ];

            chartUpdates.forEach(({ chart, data }) => {
                chart.data.labels = simulator.timeLabels;
                chart.data.datasets[0].data = data;
                chart.update('none');
            });
        }

        // 更新统计显示
        function updateStats() {
            const stats = simulator.stats;

            const updateStat = (id, key, formatter = (x) => Math.round(x)) => {
                const element = document.getElementById(id);
                if (element && stats[key] && stats[key].count > 0) {
                    element.textContent = formatter(stats[key].sum / stats[key].count);
                }
            };

            updateStat('avgHeartRate', 'heartRate');
            updateStat('avgBloodPressure', 'systolicBP', (x) => `${Math.round(x)}/${Math.round(stats.diastolicBP?.sum / stats.diastolicBP?.count || 0)}`);
            updateStat('avgSpO2', 'spo2');
            updateStat('avgTemperature', 'temperature', (x) => x.toFixed(1));
            updateStat('avgRespiratory', 'respiratoryRate');
            updateStat('avgGlucose', 'glucose', (x) => x.toFixed(1));

            document.getElementById('currentState').textContent = simulator.physiologicalState === 'normal' ? '正常' :
                simulator.physiologicalState === 'sleep' ? '睡眠' :
                simulator.physiologicalState === 'active' ? '活跃' : '压力';
        }

        // 更新异常状态
        function updateAbnormalStatus(abnormalList) {
            document.getElementById('abnormalCount').textContent = abnormalList.length;

            // 更新各卡片的状态指示器
            const cards = document.querySelectorAll('.vital-card');
            cards.forEach(card => {
                card.classList.remove('abnormal');
            });

            // 标记异常的卡片
            abnormalList.forEach(param => {
                const mapping = {
                    '心率': '.heart-rate',
                    '收缩压': '.blood-pressure',
                    '舒张压': '.blood-pressure',
                    '血氧': '.spo2',
                    '体温': '.temperature',
                    '呼吸': '.respiratory-rate',
                    '血糖': '.glucose',
                    'QT间期': '.ecg',
                    '压力指数': '.stress-index'
                };

                const selector = mapping[param];
                if (selector) {
                    const card = document.querySelector(selector);
                    if (card) card.classList.add('abnormal');
                }
            });
        }

        // 更新数据计数
        function updateDataCount(count) {
            document.getElementById('dataCount').textContent = count;
        }

        // 更新运行时间
        function updateRunTime(milliseconds) {
            const seconds = Math.floor(milliseconds / 1000);
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            document.getElementById('runTime').textContent =
                `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        // 导出数据
        function exportData() {
            if (simulator.timeLabels.length === 0) {
                alert('没有数据可导出');
                return;
            }

            let csvContent = "时间,心率,收缩压,舒张压,血氧,体温,呼吸频率,血糖,QT间期,压力指数\n";
            for (let i = 0; i < simulator.timeLabels.length; i++) {
                const row = [
                    simulator.timeLabels[i],
                    simulator.dataHistory.heartRate[i] || '',
                    simulator.dataHistory.systolicBP[i] || '',
                    simulator.dataHistory.diastolicBP[i] || '',
                    simulator.dataHistory.spo2[i] || '',
                    simulator.dataHistory.temperature[i] || '',
                    simulator.dataHistory.respiratoryRate[i] || '',
                    simulator.dataHistory.glucose[i] || '',
                    simulator.dataHistory.qtInterval?.[i] || '',
                    simulator.dataHistory.stressIndex?.[i] || ''
                ].join(',');
                csvContent += row + '\n';
            }

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `medical_data_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
            link.click();
        }

        // 切换报警设置
        function toggleAlerts() {
            alert('报警设置功能开发中...');
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initAllCharts();
            updateStatus('系统已就绪', false);
            updateDataCount(0);
            updateRunTime(0);
            updateStats();
        });
    </script>
</body>
</html>
