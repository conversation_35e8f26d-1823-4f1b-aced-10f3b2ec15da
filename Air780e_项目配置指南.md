# Air780e + CH32V307 医疗监测项目配置指南

## 🎯 项目概述

本项目实现了基于CH32V307微控制器和Air780e 4G通信模块的医疗数据采集与上传系统。系统能够采集生理监测数据并通过4G网络实时上传到云端服务器。

## 📋 硬件清单

### 主要器件
| 器件名称 | 型号 | 数量 | 用途 |
|----------|------|------|------|
| 微控制器 | CH32V307VCT6 | 1 | 主控芯片 |
| 4G模块 | Air780e M100P | 1 | 4G通信 |
| 4G天线 | 全网通天线 | 1 | 信号收发 |
| SIM卡 | 物联网卡 | 1 | 网络接入 |
| 稳压器 | AMS1117-3.3 | 1 | 电源管理 |

### 辅助器件
| 器件名称 | 规格 | 数量 | 用途 |
|----------|------|------|------|
| 电容 | 100uF/16V | 2 | 电源滤波 |
| 电容 | 10uF/16V | 2 | 电源滤波 |
| 电容 | 1uF/16V | 2 | 高频滤波 |
| 电阻 | 10kΩ | 2 | 上拉电阻 |
| LED | 3mm红色 | 2 | 状态指示 |
| 按键 | 轻触开关 | 1 | 复位按键 |

## 🔌 详细接线图

### 核心连接
```
CH32V307VCT6 引脚分配：
┌─────────────────────────────────────┐
│ 功能     │ CH32引脚 │ Air780e引脚  │
├─────────────────────────────────────┤
│ 串口发送  │ PA9(TX1) │ RXD         │
│ 串口接收  │ PA10(RX1)│ TXD         │
│ 复位控制  │ PC13     │ RST         │
│ 状态监测  │ PC14     │ STATUS      │
│ 电源正    │ 3.3V     │ VCC         │
│ 电源地    │ GND      │ GND         │
└─────────────────────────────────────┘
```

### 电源电路
```
输入电源(5V) ──┬── AMS1117-3.3 ──┬── 100uF ──┬── Air780e VCC
               │                  │           │
               └── 1000uF         └── 10uF    └── CH32V307 VCC
               
GND ───────────┴──────────────────┴───────────┴── 公共地
```

### 指示电路
```
CH32V307 PC15 ──┬── 1kΩ ──┬── LED1(电源) ──┬── GND
                 │         │               │
CH32V307 PC0  ──┴── 1kΩ ──┴── LED2(状态) ──┘
```

## ⚙️ 软件配置

### 开发环境
- **IDE**: MounRiver Studio 或 Keil MDK
- **编译器**: GCC for RISC-V
- **调试器**: WCH-Link
- **串口工具**: 串口助手

### 编译配置
```makefile
# Makefile 配置示例
TARGET = medical_monitor
MCU = CH32V307VCT6

# 源文件
SOURCES = main.c \
          system_ch32v30x.c \
          ch32v30x_gpio.c \
          ch32v30x_usart.c \
          ch32v30x_rcc.c

# 头文件路径
INCLUDES = -I./User \
           -I./Peripheral/inc \
           -I./Core

# 编译选项
CFLAGS = -march=rv32imac -mabi=ilp32 -mcmodel=medany \
         -Wall -Wextra -Os -g

# 链接选项
LDFLAGS = -T Link.ld -nostartfiles -Wl,--gc-sections
```

### 系统配置
```c
// 系统时钟配置
void SystemClock_Config(void) {
    RCC_DeInit();
    RCC_HSEConfig(RCC_HSE_ON);
    
    if(RCC_WaitForHSEStartUp() == SUCCESS) {
        RCC_PLLConfig(RCC_PLLSource_HSE_Div1, RCC_PLLMul_9); // 8MHz * 9 = 72MHz
        RCC_PLLCmd(ENABLE);
        
        while(RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET);
        
        RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
        RCC_HCLKConfig(RCC_SYSCLK_Div1);   // AHB = 72MHz
        RCC_PCLK1Config(RCC_HCLK_Div2);    // APB1 = 36MHz
        RCC_PCLK2Config(RCC_HCLK_Div1);    // APB2 = 72MHz
    }
}
```

## 📡 Air780e配置

### DTU固件配置
```bash
# 1. 查询模块信息
AT+GMI          # 厂商: LUAT
AT+GMM          # 型号: Air780E
AT+GMR          # 版本: 查看固件版本

# 2. SIM卡检查
AT+CPIN?        # 检查SIM卡状态
AT+CCID         # 读取SIM卡ICCID

# 3. 网络配置
AT+CGDCONT=1,"IP","CMNET"     # 移动APN
AT+CGDCONT=1,"IP","3GNET"     # 联通APN  
AT+CGDCONT=1,"IP","CTNET"     # 电信APN

# 4. 网络注册检查
AT+CREG?        # 网络注册状态
AT+CGATT?       # GPRS附着状态
AT+CSQ          # 信号强度

# 5. TCP连接配置
AT+NETOPEN=1,"TCP","服务器IP",8081
AT+ENTM         # 进入透传模式
```

### 透传模式说明
```
透传模式特点：
- 串口数据直接转发到网络
- 网络数据直接转发到串口
- 无需AT命令封装
- 适合实时数据传输

退出透传模式：
1. 发送 "+++" (前后各1秒间隔)
2. 模块返回 "OK"
3. 重新进入AT命令模式
```

## 🌐 服务器部署

### Node.js服务器启动
```bash
# 1. 安装依赖
npm install ws

# 2. 启动服务器
node hardware_websocket_server.js

# 3. 服务器信息
WebSocket端口: 8080 (Web客户端)
TCP端口: 8081 (Air780e连接)
```

### 云服务器配置
```bash
# 阿里云/腾讯云服务器配置
# 1. 开放端口
sudo ufw allow 8080
sudo ufw allow 8081

# 2. 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 3. 部署应用
git clone your-repo
cd medical-server
npm install
npm start

# 4. 使用PM2管理进程
npm install -g pm2
pm2 start hardware_websocket_server.js --name medical-server
pm2 startup
pm2 save
```

## 🔧 调试与测试

### 串口调试
```c
// 调试输出配置
#define DEBUG_UART USART2  // 使用UART2作为调试输出

void Debug_UART_Init(void) {
    // 配置PA2(TX2), PA3(RX2)用于调试
    // 波特率: 115200
}

// 调试宏定义
#define DEBUG_PRINT(fmt, ...) printf("[DEBUG] " fmt "\r\n", ##__VA_ARGS__)
#define ERROR_PRINT(fmt, ...) printf("[ERROR] " fmt "\r\n", ##__VA_ARGS__)
```

### 测试步骤
1. **硬件测试**
   ```c
   // 测试Air780e通信
   if(Air780e_SendATCommand("AT\r\n", "OK", 3000)) {
       DEBUG_PRINT("Air780e通信正常");
   }
   
   // 测试网络连接
   if(Air780e_SendATCommand("AT+CSQ\r\n", "OK", 3000)) {
       DEBUG_PRINT("信号强度检测完成");
   }
   ```

2. **数据传输测试**
   ```c
   // 发送测试数据
   char test_data[] = "{\"test\":\"hello\",\"timestamp\":123456}\r\n";
   Air780e_SendData((uint8_t*)test_data, strlen(test_data));
   ```

3. **服务器接收验证**
   ```bash
   # 查看服务器日志
   tail -f server.log
   
   # 预期输出
   📱 Air780e设备连接: AIR780E_xxx
   📥 Air780e数据: {"test":"hello","timestamp":123456}
   ```

## 📊 数据格式规范

### JSON数据格式
```json
{
  "heartRate": 75,
  "systolicBP": 120,
  "diastolicBP": 80,
  "spo2": 98,
  "temperature": 36.5,
  "bloodGlucose": 5.2,
  "respiratoryRate": 16,
  "qtInterval": 400,
  "cvp": 8,
  "timestamp": 1703123456,
  "deviceId": "CH32V307_001",
  "source": "Air780e"
}
```

### 自定义协议格式
```
HR:75,BP:120/80,SPO2:98,TEMP:36.5,GLUCOSE:5.2,RR:16,QT:400,CVP:8
```

## ⚠️ 注意事项

### 硬件注意事项
1. **电源设计**
   - Air780e峰值电流可达2A，需要足够的电源容量
   - 建议使用低ESR电容进行电源滤波
   - 电源纹波控制在100mV以内

2. **天线安装**
   - 天线远离金属遮挡物
   - 天线接地良好
   - 馈线长度尽量短

3. **PCB设计**
   - 4G模块下方不要走高速信号线
   - 电源和地线加粗处理
   - 注意EMC设计

### 软件注意事项
1. **AT命令时序**
   - AT命令之间需要适当延时
   - 等待模块响应完成再发送下一条命令
   - 处理超时和错误情况

2. **数据缓冲**
   - 合理设置接收缓冲区大小
   - 防止缓冲区溢出
   - 及时处理接收到的数据

3. **错误处理**
   - 网络断开重连机制
   - 数据发送失败重试
   - 模块异常复位恢复

## 🔍 故障排除

### 常见问题
1. **模块无响应**
   - 检查电源电压是否正常
   - 检查串口连接是否正确
   - 尝试硬件复位

2. **网络连接失败**
   - 检查SIM卡是否正常
   - 确认APN设置正确
   - 检查信号强度

3. **数据传输异常**
   - 检查服务器地址和端口
   - 确认网络连接状态
   - 检查数据格式是否正确

### 调试工具
- 串口助手：监控AT命令交互
- 网络抓包：分析数据传输
- 示波器：检查硬件信号
- 万用表：测量电源电压

---

**配置完成后，您的医疗监测设备就可以通过Air780e 4G模块实时上传数据到云端了！**
