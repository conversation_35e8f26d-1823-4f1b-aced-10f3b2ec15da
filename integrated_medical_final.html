<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集成式生理监测管理系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
            position: relative;
            overflow-x: hidden;
            margin: 0;
        }

        /* 全屏模式样式 */
        body.fullscreen {
            padding: 0;
            margin: 0;
        }

        body.fullscreen .container {
            max-width: 100%;
            height: 100vh;
            margin: 0;
            border-radius: 0;
            padding: 20px;
            overflow-y: auto;
        }

        /* 移除了gradientShift动画以提升性能 */

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 95vw;
            width: 100%;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 24px;
            padding: 30px 25px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
            min-height: calc(100vh - 20px);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px 0;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-content {
            flex: 1;
            text-align: center;
        }

        .fullscreen-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            z-index: 10;
        }

        .fullscreen-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .fullscreen-btn:active {
            transform: translateY(0);
        }

        .home-btn {
            position: absolute;
            top: 20px;
            right: 140px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
            z-index: 10;
        }

        .home-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.6);
        }

        .home-btn:active {
            transform: translateY(0);
        }

        .header::before {
            content: '🏥';
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 3em;
            opacity: 0.1;
            z-index: -1;
        }

        .header h1 {
            color: #333;
            font-size: 3.4em;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 移除gradientText动画 */

        .header p {
            color: #666;
            font-size: 1.6em;
            margin-bottom: 8px;
            font-weight: 300;
            letter-spacing: 0.5px;
            opacity: 0.8;
        }

        .status-info {
            background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
            color: #2d5a2d;
            font-weight: 600;
            font-size: 1.1em;
            border: 2px solid rgba(200, 230, 201, 0.6);
            box-shadow:
                0 8px 16px rgba(45, 90, 45, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            position: relative;
            overflow: hidden;
        }

        .status-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            /* 移除shimmer动画 */
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .status-info.error-message {
            background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
            color: #c62828;
            border-color: rgba(244, 67, 54, 0.3);
            box-shadow:
                0 8px 16px rgba(198, 40, 40, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }

        /* 管理界面 */
        .management-view {
            display: block;
        }

        .management-view.hidden {
            display: none;
        }

        .subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .subject-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 35px 30px;
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.08),
                0 1px 8px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            border: 2px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            min-height: 200px;
        }

        .subject-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
            background-size: 200% 100%;
            /* 移除borderGlow动画 */
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .subject-card:hover::before {
            opacity: 1;
        }

        @keyframes borderGlow {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .subject-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.12),
                0 8px 16px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .subject-card.active {
            border-color: rgba(76, 175, 80, 0.4);
            background: linear-gradient(145deg, #f8fff8 0%, #e8f5e8 100%);
            box-shadow:
                0 15px 35px rgba(76, 175, 80, 0.15),
                0 5px 15px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }

        .subject-card.active::before {
            background: linear-gradient(90deg, #4caf50, #66bb6a, #4caf50);
            opacity: 0.8;
        }

        .subject-card.inactive {
            opacity: 0.5;
            cursor: default;
            background: linear-gradient(145deg, #f5f5f5 0%, #e0e0e0 100%);
        }

        .subject-card.inactive:hover {
            transform: none;
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.08),
                0 1px 8px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .subject-card.inactive::before {
            display: none;
        }

        .subject-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(0, 0, 0, 0.05);
        }

        .subject-id {
            font-size: 2.0em;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: 1px;
        }

        .subject-status {
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .subject-status::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            /* 移除statusShimmer动画 */
        }

        @keyframes statusShimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .status-normal {
            background: linear-gradient(135deg, #4caf50, #66bb6a);
            color: white;
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .status-inactive {
            background: linear-gradient(135deg, #9e9e9e, #bdbdbd);
            color: white;
            box-shadow: 0 4px 12px rgba(158, 158, 158, 0.3);
        }

        .subject-info {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.02);
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .subject-info p {
            margin: 8px 0;
            color: #555;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .subject-info strong {
            color: #333;
            margin-right: 8px;
            min-width: 50px;
        }

        .vital-preview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 18px;
            font-size: 1.0em;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .vital-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 1.1em;
        }

        .vital-item:last-child {
            border-bottom: none;
        }

        .vital-label {
            color: #666;
            font-weight: 500;
            font-size: 1.0em;
        }

        .vital-value {
            font-weight: 700;
            color: #333;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.1em;
        }

        /* 卡片操作按钮 */
        .card-actions {
            display: flex;
            gap: 12px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .action-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 16px 20px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-height: 50px;
        }

        .detail-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .detail-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
        }

        .waveform-btn {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            box-shadow: 0 4px 12px rgba(245, 87, 108, 0.3);
        }

        .waveform-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(245, 87, 108, 0.4);
        }

        .disabled-btn {
            background: #95a5a6;
            color: white;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .disabled-btn:hover {
            transform: none;
            box-shadow: none;
        }

        .btn-icon {
            font-size: 20px;
        }

        .btn-text {
            font-size: 16px;
        }

        /* 详细监测界面 */
        .detail-view {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: 100;
            overflow-y: auto;
            padding: 20px;
        }

        .detail-view.active {
            display: block;
            animation: slideInFromRight 0.3s ease;
        }

        @keyframes slideInFromRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 40px 50px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            min-height: 120px;
        }

        .detail-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
            background-size: 200% 100%;
            /* 移除headerGlow动画 */
        }

        @keyframes headerGlow {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .detail-title {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .detail-title .icon {
            font-size: 4em;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .detail-title .info h2 {
            color: #333;
            margin-bottom: 8px;
            font-size: 2.4em;
            font-weight: 700;
            background: linear-gradient(135deg, #333, #555);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .detail-title .info .subject-info-text {
            color: #666;
            font-size: 1.4em;
            font-weight: 500;
            opacity: 0.8;
        }

        .back-btn {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 18px 36px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            font-size: 1.3em;
            font-weight: 600;
            box-shadow: 0 8px 16px rgba(149, 165, 166, 0.3);
            position: relative;
            overflow: hidden;
        }

        .back-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .back-btn:hover::before {
            left: 100%;
        }

        /* 波形图导航 */
        .waveform-nav {
            display: flex;
            gap: 10px;
        }

        .back-btn.secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            font-size: 1.1em;
            padding: 15px 25px;
        }

        .back-btn.secondary:hover {
            background: linear-gradient(135deg, #7f8c8d, #95a5a6);
            transform: translateY(-2px);
        }

        .back-btn:hover {
            transform: translateY(-4px) scale(1.05);
            box-shadow: 0 12px 24px rgba(149, 165, 166, 0.4);
            background: linear-gradient(135deg, #a0b1b2, #8a9b9c);
        }

        .vitals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin: 25px 0;
        }

        .vital-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.08),
                0 5px 15px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.3);
            min-height: 200px;
        }

        .vital-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--card-color, #667eea), transparent, var(--card-color, #667eea));
            background-size: 200% 100%;
            /* 移除cardGlow动画 */
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .vital-card:hover::before {
            opacity: 1;
        }

        @keyframes cardGlow {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .vital-card:hover {
            transform: translateY(-8px) scale(1.03);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.12),
                0 10px 20px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
            border-color: rgba(var(--card-color-rgb, 102, 126, 234), 0.3);
        }

        .vital-card::after {
            content: '点击查看详细波形图 📈';
            position: absolute;
            bottom: 12px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.85em;
            color: #666;
            opacity: 0;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            padding: 4px 12px;
            border-radius: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            font-weight: 500;
        }

        .vital-card:hover::after {
            opacity: 1;
            transform: translateX(-50%) translateY(-2px);
        }

        .vital-card .icon {
            font-size: 4.0em;
            margin-bottom: 15px;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
            transition: all 0.3s ease;
        }

        .vital-card:hover .icon {
            transform: scale(1.1) rotate(5deg);
        }

        .vital-card .value {
            font-size: 3.0em;
            font-weight: 800;
            margin-bottom: 12px;
            background: linear-gradient(135deg, var(--card-color, #667eea), var(--card-color-light, #764ba2));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .vital-card .label {
            color: #555;
            font-size: 1.3em;
            margin-bottom: 8px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .vital-card .range {
            color: #777;
            font-size: 1.0em;
            font-weight: 500;
            background: rgba(0, 0, 0, 0.03);
            padding: 8px 15px;
            border-radius: 12px;
            margin-top: 8px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* 生理参数颜色主题 */
        .heart-rate {
            --card-color: #e74c3c;
            --card-color-light: #ec7063;
            --card-color-rgb: 231, 76, 60;
        }
        .heart-rate .icon { color: #e74c3c; }

        .blood-pressure {
            --card-color: #8e44ad;
            --card-color-light: #a569bd;
            --card-color-rgb: 142, 68, 173;
        }
        .blood-pressure .icon { color: #8e44ad; }

        .spo2 {
            --card-color: #3498db;
            --card-color-light: #5dade2;
            --card-color-rgb: 52, 152, 219;
        }
        .spo2 .icon { color: #3498db; }

        .temperature {
            --card-color: #f39c12;
            --card-color-light: #f5b041;
            --card-color-rgb: 243, 156, 18;
        }
        .temperature .icon { color: #f39c12; }

        .respiratory-rate {
            --card-color: #27ae60;
            --card-color-light: #52c882;
            --card-color-rgb: 39, 174, 96;
        }
        .respiratory-rate .icon { color: #27ae60; }

        .blood-glucose {
            --card-color: #e67e22;
            --card-color-light: #eb984e;
            --card-color-rgb: 230, 126, 34;
        }
        .blood-glucose .icon { color: #e67e22; }

        .ecg-rhythm {
            --card-color: #34495e;
            --card-color-light: #5d6d7e;
            --card-color-rgb: 52, 73, 94;
        }
        .ecg-rhythm .icon { color: #34495e; }

        .cvp {
            --card-color: #9b59b6;
            --card-color-light: #bb8fce;
            --card-color-rgb: 155, 89, 182;
        }
        .cvp .icon { color: #9b59b6; }

        /* 波形视图 */
        .waveform-view {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: 200;
            overflow-y: auto;
            padding: 20px;
        }

        .waveform-view.active {
            display: block;
            animation: slideInFromBottom 0.3s ease;
        }

        @keyframes slideInFromBottom {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .waveform-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 40px 50px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            min-height: 120px;
        }

        .waveform-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--current-color, #667eea), transparent, var(--current-color, #667eea));
            background-size: 200% 100%;
            /* 移除waveformGlow动画 */
        }

        @keyframes waveformGlow {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .waveform-title {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .waveform-title .icon {
            font-size: 4.5em;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
            /* 移除pulse动画 */
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .waveform-title .info h2 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.8em;
            font-weight: 700;
            background: linear-gradient(135deg, #333, #555);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .waveform-title .info .current-value {
            font-size: 2.4em;
            font-weight: 800;
            background: linear-gradient(135deg, var(--current-color, #667eea), var(--current-color-light, #764ba2));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .chart-container {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 50px 40px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.08),
                0 8px 16px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            min-height: 500px;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--current-color, #667eea), var(--current-color-light, #764ba2));
            opacity: 0.6;
        }

        .chart-container h3 {
            margin-bottom: 25px;
            color: #333;
            text-align: center;
            font-size: 2.0em;
            font-weight: 700;
            background: linear-gradient(135deg, #333, #555);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .chart-wrapper {
            position: relative;
            height: 550px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .stats-panel {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 30px;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.08),
                0 5px 15px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .stats-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--current-color, #667eea), var(--current-color-light, #764ba2));
            opacity: 0.8;
        }

        .stats-panel h3 {
            text-align: center;
            margin-bottom: 25px;
            color: #333;
            font-size: 1.4em;
            font-weight: 700;
            background: linear-gradient(135deg, #333, #555);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
        }

        .stat-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 249, 250, 0.8) 100%);
            padding: 25px 20px;
            border-radius: 16px;
            text-align: center;
            box-shadow:
                0 8px 16px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--current-color, #667eea), var(--current-color-light, #764ba2));
            opacity: 0.6;
        }

        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow:
                0 12px 24px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }

        .stat-item .value {
            font-size: 2em;
            font-weight: 800;
            background: linear-gradient(135deg, var(--current-color, #667eea), var(--current-color-light, #764ba2));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-item .label {
            color: #666;
            font-size: 1em;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .error-message {
            background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
            color: #c62828;
            padding: 20px;
            border-radius: 16px;
            margin: 25px 0;
            text-align: center;
            border: 2px solid rgba(244, 67, 54, 0.3);
            box-shadow:
                0 8px 16px rgba(198, 40, 40, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            font-weight: 600;
            font-size: 1.1em;
        }

        /* 大屏幕优化 */
        @media (min-width: 1600px) {
            .container {
                max-width: 98%;
                padding: 40px;
            }

            .header h1 {
                font-size: 4em;
            }

            .header p {
                font-size: 1.8em;
            }

            .subjects-grid {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: 30px;
            }

            .vitals-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 25px;
            }

            .chart-wrapper {
                height: 450px;
            }
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .container {
                max-width: 98%;
                padding: 25px;
            }
        }

        @media (max-width: 1200px) {
            .container {
                max-width: 98%;
                padding: 20px;
            }

            .subjects-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 20px;
            }

            .vitals-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }

            .fullscreen-btn {
                padding: 10px 16px;
                font-size: 13px;
            }

            .home-btn {
                padding: 10px 16px;
                font-size: 13px;
                right: 120px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 20px;
                border-radius: 16px;
            }

            .header h1 {
                font-size: 2.2em;
            }

            .subjects-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .vitals-grid {
                grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
                gap: 15px;
            }

            .detail-header,
            .waveform-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .detail-title,
            .waveform-title {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
                gap: 15px;
            }

            .chart-wrapper {
                height: 300px;
            }

            .fullscreen-btn {
                padding: 8px 12px;
                font-size: 12px;
                top: 15px;
                right: 15px;
            }

            .home-btn {
                padding: 8px 12px;
                font-size: 12px;
                top: 15px;
                right: 100px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8em;
            }

            .header p {
                font-size: 1.1em;
            }

            .fullscreen-btn {
                padding: 6px 10px;
                font-size: 11px;
                top: 10px;
                right: 10px;
            }

            .home-btn {
                padding: 6px 10px;
                font-size: 11px;
                top: 10px;
                right: 80px;
            }

            .vitals-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .vital-card {
                padding: 20px 15px;
            }

            .vital-card .icon {
                font-size: 2.8em;
            }

            .vital-card .value {
                font-size: 2em;
            }
        }

        /* 高级视觉效果 */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .container {
            /* 移除float动画以提升性能 */
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #764ba2, #667eea);
        }

        /* 选择文本样式 */
        ::selection {
            background: rgba(102, 126, 234, 0.3);
            color: #333;
        }

        ::-moz-selection {
            background: rgba(102, 126, 234, 0.3);
            color: #333;
        }

        /* 性能优化 */
        * {
            will-change: auto;
        }

        .subject-card, .vital-card {
            will-change: transform;
            transform: translateZ(0);
        }

        .container {
            contain: layout style paint;
        }

        /* 可编辑字段样式 */
        .editable-field {
            position: relative;
            cursor: pointer;
            padding: 2px 4px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .editable-field:hover {
            background: rgba(102, 126, 234, 0.1);
            box-shadow: 0 0 0 1px rgba(102, 126, 234, 0.3);
        }

        .editable-field::after {
            content: '✏️';
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.8em;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .editable-field:hover::after {
            opacity: 0.6;
        }

        /* 右键菜单样式 */
        .context-menu {
            position: fixed;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            padding: 8px 0;
            z-index: 1000;
            min-width: 120px;
            display: none;
        }

        .context-menu-item {
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            transition: background 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .context-menu-item:hover {
            background: #f5f5f5;
        }

        .context-menu-item .icon {
            font-size: 16px;
        }

        /* 编辑输入框样式 */
        .edit-input {
            background: white;
            border: 2px solid #667eea;
            border-radius: 6px;
            padding: 6px 10px;
            font-size: inherit;
            font-family: inherit;
            color: inherit;
            outline: none;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
            transition: all 0.3s ease;
        }

        .edit-input:focus {
            border-color: #764ba2;
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.3);
        }

        /* 编辑模式提示 */
        .edit-mode-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            z-index: 999;
            display: none;
            animation: slideInRight 0.3s ease;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 异常报警系统样式 */
        .alert-system {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: flex-start;
            padding-top: 10vh;
            backdrop-filter: blur(5px);
        }

        .alert-system.active {
            display: flex;
            animation: alertFadeIn 0.3s ease;
        }

        @keyframes alertFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .alert-container {
            background: linear-gradient(135deg, #ff4757, #ff3838);
            color: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow:
                0 20px 60px rgba(255, 71, 87, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
            position: relative;
            overflow: hidden;
            animation: alertPulse 2s ease-in-out infinite;
        }

        @keyframes alertPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .alert-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: alertShimmer 2s linear infinite;
        }

        @keyframes alertShimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .alert-icon {
            font-size: 4em;
            margin-bottom: 20px;
            animation: alertBounce 1s ease-in-out infinite;
        }

        @keyframes alertBounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .alert-title {
            font-size: 2em;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .alert-message {
            font-size: 1.2em;
            margin-bottom: 25px;
            line-height: 1.5;
            opacity: 0.95;
        }

        .alert-details {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            text-align: left;
        }

        .alert-detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .alert-detail-item:last-child {
            margin-bottom: 0;
        }

        .alert-detail-label {
            font-weight: 600;
        }

        .alert-detail-value {
            font-weight: 700;
            color: #ffeb3b;
        }

        .alert-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .alert-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .alert-btn-primary {
            background: white;
            color: #ff4757;
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
        }

        .alert-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(255, 255, 255, 0.4);
        }

        .alert-btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .alert-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* 异常状态指示器 */
        .abnormal-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(135deg, #ff4757, #ff3838);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(255, 71, 87, 0.4);
            z-index: 1000;
            display: none;
            animation: indicatorBlink 1s ease-in-out infinite;
        }

        .abnormal-indicator.active {
            display: block;
        }

        @keyframes indicatorBlink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* 异常数据高亮 */
        .vital-value.abnormal {
            color: #ff4757 !important;
            font-weight: 700;
            animation: valueAlert 1s ease-in-out infinite;
        }

        @keyframes valueAlert {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .vital-card.abnormal {
            border: 2px solid #ff4757;
            box-shadow: 0 0 20px rgba(255, 71, 87, 0.3);
            animation: cardAlert 2s ease-in-out infinite;
        }

        @keyframes cardAlert {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 71, 87, 0.3); }
            50% { box-shadow: 0 0 30px rgba(255, 71, 87, 0.5); }
        }

        /* 设备连接界面样式 */
        .device-connection-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .device-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .device-header h2 {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .device-header p {
            font-size: 1.2em;
            color: #7f8c8d;
        }

        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .device-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 30px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .device-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .device-card.active {
            border-color: #27ae60;
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.2);
        }

        .device-icon {
            font-size: 3em;
            text-align: center;
            margin-bottom: 15px;
        }

        .device-name {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 5px;
        }

        .device-id {
            font-size: 1em;
            font-weight: 500;
            color: #667eea;
            text-align: center;
            margin-bottom: 10px;
            background: rgba(102, 126, 234, 0.1);
            padding: 4px 12px;
            border-radius: 12px;
            display: inline-block;
        }

        .device-status {
            text-align: center;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .device-status.connected {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }

        .device-status.disconnected {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .device-status.connecting {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            animation: pulse 2s ease-in-out infinite;
        }

        .device-info {
            font-size: 0.9em;
            color: #7f8c8d;
            line-height: 1.6;
        }

        .device-info p {
            margin: 5px 0;
        }

        .device-actions {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .scan-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .refresh-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }

        .settings-btn {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        /* 设备连接界面响应式设计 */
        @media (max-width: 768px) {
            .device-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .device-card {
                padding: 25px;
            }

            .device-header h2 {
                font-size: 2em;
            }

            .device-actions {
                flex-direction: column;
                gap: 15px;
            }

            .action-btn {
                width: 100%;
                max-width: 300px;
            }
        }

        @media (max-width: 480px) {
            .device-connection-container {
                padding: 15px;
            }

            .device-header h2 {
                font-size: 1.8em;
            }

            .device-header p {
                font-size: 1.1em;
            }

            .device-card {
                padding: 20px;
            }

            .device-icon {
                font-size: 2.5em;
            }

            .device-name {
                font-size: 1.2em;
            }
        }

        /* 短期检测数据展示样式 */
        .short-term-data-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 30px;
            max-width: 1000px;
            margin: 0 auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 2px solid #f39c12;
        }

        .short-term-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f39c12;
        }

        .short-term-header h2 {
            font-size: 2.2em;
            color: #2c3e50;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #f39c12, #e67e22);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .patient-info {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .patient-name {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
        }

        .patient-id {
            font-size: 1.1em;
            color: #7f8c8d;
        }

        .patient-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .patient-status.active {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }

        .patient-status.inactive {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .vital-data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .vital-data-group {
            background: linear-gradient(135deg, #ffffff 0%, #f1f2f6 100%);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #e9ecef;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .vital-data-group h3 {
            font-size: 1.3em;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f39c12;
        }

        .data-items {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .data-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border-left: 4px solid #f39c12;
        }

        .data-label {
            font-size: 1em;
            color: #34495e;
            font-weight: 500;
            flex: 1;
        }

        .data-value {
            font-size: 1.4em;
            font-weight: 700;
            color: #2c3e50;
            margin: 0 10px;
        }

        .data-unit {
            font-size: 0.9em;
            color: #7f8c8d;
            min-width: 60px;
            text-align: right;
        }

        .short-term-footer {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 10px;
            border: 1px solid #f39c12;
        }

        .short-term-footer p {
            margin: 0;
            color: #856404;
            font-size: 1em;
            font-weight: 500;
        }

        /* 短期检测响应式设计 */
        @media (max-width: 768px) {
            .vital-data-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .patient-info {
                flex-direction: column;
                gap: 10px;
            }

            .short-term-data-card {
                padding: 20px;
                margin: 10px;
            }
        }

        @media (max-width: 480px) {
            .short-term-header h2 {
                font-size: 1.8em;
            }

            .data-item {
                flex-direction: column;
                text-align: center;
                gap: 8px;
            }

            .data-value {
                font-size: 1.6em;
                margin: 0;
            }
        }

        /* 短期检测"正在检测"界面样式 */
        .short-term-loading-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
            color: white;
            text-align: center;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .short-term-loading-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .loading-header {
            margin-bottom: 40px;
            position: relative;
            z-index: 2;
        }

        .loading-header h2 {
            font-size: 2.5em;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .loading-header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .loading-animation {
            margin-bottom: 40px;
            position: relative;
            z-index: 2;
        }

        .loading-spinner {
            width: 80px;
            height: 80px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            margin: 0 auto 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.5em;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .loading-dots {
            display: flex;
            justify-content: center;
            gap: 8px;
        }

        .dot {
            width: 12px;
            height: 12px;
            background: white;
            border-radius: 50%;
            animation: bounce 1.4s ease-in-out infinite both;
        }

        .dot:nth-child(1) { animation-delay: -0.32s; }
        .dot:nth-child(2) { animation-delay: -0.16s; }
        .dot:nth-child(3) { animation-delay: 0s; }

        @keyframes bounce {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .loading-progress {
            margin-bottom: 40px;
            position: relative;
            z-index: 2;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ffffff, #f1c40f);
            border-radius: 4px;
            width: 0%;
            transition: width 0.3s ease;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .progress-text {
            font-size: 1.1em;
            font-weight: 500;
        }

        .loading-status {
            margin-bottom: 40px;
            position: relative;
            z-index: 2;
        }

        .status-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            opacity: 0.5;
            transition: opacity 0.5s ease;
            gap: 15px;
        }

        .status-item.active {
            opacity: 1;
            animation: statusPulse 0.5s ease-in-out;
        }

        @keyframes statusPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .status-icon {
            font-size: 1.2em;
        }

        .status-text {
            font-size: 1em;
        }

        .loading-footer {
            position: relative;
            z-index: 2;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .loading-footer p {
            margin: 8px 0;
            font-size: 0.95em;
        }

        .loading-footer p:first-child {
            font-weight: 600;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .short-term-loading-card {
                padding: 30px 20px;
                margin: 10px;
            }

            .loading-header h2 {
                font-size: 2em;
            }

            .loading-spinner {
                width: 60px;
                height: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="fullscreen-btn" onclick="toggleFullscreen()" id="fullscreenBtn">
                🔍 全屏模式
            </button>
            <button class="home-btn" onclick="returnToLauncher()" id="homeBtn">
                🏠 返回主页
            </button>
            <div class="header-content">
                <h1 id="systemTitle">🏥 集成式生理监测管理系统</h1>
                <p id="systemSubtitle">多对象集成监测，统一管理，精准分析</p>
            </div>
        </div>

        <div class="status-info" id="statusInfo">
            系统运行正常 - 当前监测12个对象，1个对象数据有效
        </div>

        <!-- 管理界面 -->
        <div class="management-view" id="managementView">
            <div class="subjects-grid" id="subjectsGrid">
                <!-- 检测对象卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 详细监测界面 -->
        <div class="detail-view" id="detailView">
            <div class="detail-header">
                <div class="detail-title">
                    <div class="icon">👤</div>
                    <div class="info">
                        <h2 id="detailSubjectName">患者详细监测</h2>
                        <div class="subject-info-text" id="detailSubjectInfo">正在加载患者信息...</div>
                    </div>
                </div>
                <button class="back-btn" onclick="console.log('按钮被点击'); backToManagement();">← 返回管理界面</button>
            </div>

            <div class="vitals-grid" id="vitalsGrid">
                <!-- 生理参数卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 波形视图 -->
        <div class="waveform-view" id="waveformView">
            <div class="waveform-header">
                <div class="waveform-title">
                    <div class="icon" id="waveformIcon">💓</div>
                    <div class="info">
                        <h2 id="waveformTitle">心率分析</h2>
                        <div class="current-value" id="waveformCurrentValue">72 次/分</div>
                    </div>
                </div>
                <div class="waveform-nav">
                    <button class="back-btn" onclick="backToDetail()">← 返回上级</button>
                    <button class="back-btn secondary" onclick="backToManagementFromWaveform()">🏠 返回主页</button>
                </div>
            </div>

            <div class="chart-container">
                <h3 id="chartTitle">心率变化趋势</h3>
                <div class="chart-wrapper">
                    <canvas id="waveformChart"></canvas>
                </div>
            </div>

            <div class="stats-panel">
                <h3>数据统计</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="value" id="currentStat">72</div>
                        <div class="label">当前值</div>
                    </div>
                    <div class="stat-item">
                        <div class="value" id="averageStat">74</div>
                        <div class="label">平均值</div>
                    </div>
                    <div class="stat-item">
                        <div class="value" id="maxStat">85</div>
                        <div class="label">最高值</div>
                    </div>
                    <div class="stat-item">
                        <div class="value" id="minStat">65</div>
                        <div class="label">最低值</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右键菜单 -->
        <div class="context-menu" id="contextMenu">
            <div class="context-menu-item" id="editMenuItem">
                <span class="icon">✏️</span>
                <span>编辑</span>
            </div>
        </div>

        <!-- 编辑模式指示器 -->
        <div class="edit-mode-indicator" id="editModeIndicator">
            📝 编辑模式 - 点击其他地方保存
        </div>

        <!-- 异常报警系统 -->
        <div class="alert-system" id="alertSystem">
            <div class="alert-container">
                <div class="alert-icon" id="alertIcon">🚨</div>
                <div class="alert-title" id="alertTitle">数据异常警报</div>
                <div class="alert-message" id="alertMessage">检测到生理数据异常变化，请立即关注！</div>
                <div class="alert-details" id="alertDetails">
                    <!-- 异常详情将动态生成 -->
                </div>
                <div class="alert-actions">
                    <button class="alert-btn alert-btn-primary" onclick="acknowledgeAlert()">确认查看</button>
                    <button class="alert-btn alert-btn-secondary" onclick="dismissAlert()">暂时忽略</button>
                </div>
            </div>
        </div>

        <!-- 异常状态指示器 -->
        <div class="abnormal-indicator" id="abnormalIndicator">
            🚨 检测到数据异常
        </div>
    </div>

    <script>
        // 全局变量
        let currentSubjectId = null;
        let currentParameter = null;
        let waveformChart = null;
        let updateInterval = null;
        let previousView = null; // 记录进入波形图前的界面状态

        // 系统配置
        const systemConfig = {
            enableAbnormalData: false,  // 是否启用异常数据生成（正常情况下关闭）
            abnormalDataProbability: 0.005,  // 异常数据生成概率（0.5%）
            demoMode: false,  // 演示模式（开启时会有更多异常数据用于展示）
            currentMode: 'long'  // 当前运行模式：short(短期检测), long(长期监测), device(设备连接)
        };

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 初始化运行模式
        function initializeMode() {
            const mode = getUrlParameter('mode') || 'long';
            systemConfig.currentMode = mode;

            console.log(`🎯 系统运行模式: ${mode}`);

            // 根据模式调整系统配置
            switch (mode) {
                case 'short':
                    console.log('⚡ 短期检测模式 - 快速监测配置');
                    systemConfig.enableAbnormalData = false;
                    break;
                case 'long':
                    console.log('📊 长期监测模式 - 完整功能配置');
                    systemConfig.enableAbnormalData = false;
                    break;
                case 'device':
                    console.log('🔗 设备连接模式 - 设备管理配置');
                    systemConfig.enableAbnormalData = false;
                    break;
            }
        }

        // 编辑功能变量
        let currentEditElement = null;
        let currentEditField = null;
        let currentEditSubjectId = null;
        let originalValue = null;

        // 缓存DOM元素以提升性能
        let cachedElements = {
            subjectsGrid: null,
            statusInfo: null,
            detailView: null,
            waveformView: null,
            contextMenu: null
        };

        // 报警系统变量
        let alertSystem = {
            isActive: false,
            currentAlerts: [],
            alertHistory: [],
            lastAlertTime: null,
            alertCooldown: 10000, // 10秒冷却时间
            isAcknowledged: false
        };

        // 异常阈值配置
        const abnormalThresholds = {
            heartRate: {
                min: 50,
                max: 120,
                criticalMin: 40,
                criticalMax: 150,
                name: '心率',
                unit: '次/分'
            },
            systolicBP: {
                min: 90,
                max: 140,
                criticalMin: 70,
                criticalMax: 180,
                name: '收缩压',
                unit: 'mmHg'
            },
            diastolicBP: {
                min: 60,
                max: 90,
                criticalMin: 40,
                criticalMax: 110,
                name: '舒张压',
                unit: 'mmHg'
            },
            spo2: {
                min: 95,
                max: 100,
                criticalMin: 90,
                criticalMax: 100,
                name: '血氧饱和度',
                unit: '%'
            },
            temperature: {
                min: 36.1,
                max: 37.2,
                criticalMin: 35.0,
                criticalMax: 39.0,
                name: '体温',
                unit: '°C'
            },
            respiratoryRate: {
                min: 12,
                max: 20,
                criticalMin: 8,
                criticalMax: 30,
                name: '呼吸频率',
                unit: '次/分'
            },
            bloodGlucose: {
                min: 3.9,
                max: 6.1,
                criticalMin: 2.8,
                criticalMax: 11.1,
                name: '血糖',
                unit: 'mmol/L'
            },
            qtInterval: {
                min: 350,
                max: 450,
                criticalMin: 300,
                criticalMax: 500,
                name: 'QT间期',
                unit: 'ms'
            },
            cvp: {
                min: 2,
                max: 8,
                criticalMin: 0,
                criticalMax: 15,
                name: '中心静脉压',
                unit: 'mmHg'
            }
        };
        
        // 历史数据
        let historyData = {
            heartRate: [],
            bloodPressure: [],
            spo2: [],
            temperature: [],
            respiratoryRate: [],
            bloodGlucose: [],
            ecgRhythm: [],
            cvp: [],
            timeLabels: []
        };
        
        // 模拟检测对象数据
        let subjects = {
            'P001': {
                id: 'P001',
                name: '张某某',
                age: 45,
                gender: '男',
                room: '4F012',
                status: 'normal',
                isActive: true,
                vitalSigns: {
                    heartRate: 72,
                    systolicBP: 120,
                    diastolicBP: 80,
                    spo2: 98,
                    temperature: 36.5,
                    respiratoryRate: 16,
                    bloodGlucose: 5.2,
                    qtInterval: 400,
                    cvp: 8
                }
            }
        };
        
        // 创建其他11个无效对象
        for (let i = 2; i <= 12; i++) {
            const id = `P${i.toString().padStart(3, '0')}`;
            subjects[id] = {
                id: id,
                name: `患者${i}`,
                age: 30 + Math.floor(Math.random() * 40),
                gender: Math.random() > 0.5 ? '男' : '女',
                room: `4F${(i + 10).toString().padStart(3, '0')}`,
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            };
        }

        // 参数配置
        const parameterConfigs = {
            heartRate: {
                icon: '💓',
                title: '心率分析',
                unit: '次/分',
                color: '#e74c3c',
                getValue: (subject) => Math.round(subject.vitalSigns.heartRate),
                getDisplayValue: (subject) => Math.round(subject.vitalSigns.heartRate) + ' 次/分',
                range: '参考范围: 60-100'
            },
            bloodPressure: {
                icon: '🩸',
                title: '血压分析',
                unit: 'mmHg',
                color: '#8e44ad',
                getValue: (subject) => Math.round(subject.vitalSigns.systolicBP),
                getDisplayValue: (subject) => `${Math.round(subject.vitalSigns.systolicBP)}/${Math.round(subject.vitalSigns.diastolicBP)} mmHg`,
                range: '参考范围: 90-140/60-90'
            },
            spo2: {
                icon: '🫁',
                title: '血氧分析',
                unit: '%',
                color: '#3498db',
                getValue: (subject) => Math.round(subject.vitalSigns.spo2),
                getDisplayValue: (subject) => Math.round(subject.vitalSigns.spo2) + '%',
                range: '参考范围: 95-100'
            },
            temperature: {
                icon: '🌡️',
                title: '体温分析',
                unit: '°C',
                color: '#f39c12',
                getValue: (subject) => subject.vitalSigns.temperature.toFixed(1),
                getDisplayValue: (subject) => subject.vitalSigns.temperature.toFixed(1) + '°C',
                range: '参考范围: 36.1-37.2'
            },
            respiratoryRate: {
                icon: '🌬️',
                title: '呼吸分析',
                unit: '次/分',
                color: '#27ae60',
                getValue: (subject) => Math.round(subject.vitalSigns.respiratoryRate),
                getDisplayValue: (subject) => Math.round(subject.vitalSigns.respiratoryRate) + ' 次/分',
                range: '参考范围: 12-20'
            },
            bloodGlucose: {
                icon: '🍯',
                title: '血糖分析',
                unit: 'mmol/L',
                color: '#e67e22',
                getValue: (subject) => subject.vitalSigns.bloodGlucose.toFixed(1),
                getDisplayValue: (subject) => subject.vitalSigns.bloodGlucose.toFixed(1) + ' mmol/L',
                range: '参考范围: 3.9-6.1'
            },
            ecgRhythm: {
                icon: '📈',
                title: '心电分析',
                unit: 'ms',
                color: '#34495e',
                getValue: (subject) => Math.round(subject.vitalSigns.qtInterval),
                getDisplayValue: (subject) => Math.round(subject.vitalSigns.qtInterval) + ' ms',
                range: '参考范围: 350-450'
            },
            cvp: {
                icon: '🔄',
                title: '静脉压分析',
                unit: 'mmHg',
                color: '#9b59b6',
                getValue: (subject) => Math.round(subject.vitalSigns.cvp),
                getDisplayValue: (subject) => Math.round(subject.vitalSigns.cvp) + ' mmHg',
                range: '参考范围: 2-8'
            }
        };

        // 缓存DOM元素
        function cacheElements() {
            console.log('📦 缓存DOM元素');
            cachedElements.subjectsGrid = document.getElementById('subjectsGrid');
            cachedElements.statusInfo = document.getElementById('statusInfo');
            cachedElements.detailView = document.getElementById('detailView');
            cachedElements.waveformView = document.getElementById('waveformView');
            cachedElements.contextMenu = document.getElementById('contextMenu');
        }

        // 初始化系统
        function initializeSystem() {
            console.log('🚀 系统初始化开始');

            try {
                // 初始化运行模式
                initializeMode();

                // 更新页面标题和描述
                updatePageTitle();

                cacheElements();
                renderSubjectsGrid();
                updateStatusInfo();
                generateHistoryData();
                startDataUpdate();

                console.log('✅ 系统初始化完成');
            } catch (error) {
                console.error('❌ 系统初始化失败:', error);
                showError('系统初始化失败: ' + error.message);
            }
        }

        // 更新页面标题
        function updatePageTitle() {
            const titleElement = document.getElementById('systemTitle');
            const subtitleElement = document.getElementById('systemSubtitle');

            if (!titleElement || !subtitleElement) return;

            switch (systemConfig.currentMode) {
                case 'short':
                    titleElement.textContent = '⚡ 短期检测系统';
                    subtitleElement.textContent = '快速健康状态评估，实时监测分析';
                    break;
                case 'long':
                    titleElement.textContent = '📊 长期监测系统';
                    subtitleElement.textContent = '多对象集成监测，统一管理，精准分析';
                    break;
                case 'device':
                    titleElement.textContent = '🔗 设备连接管理';
                    subtitleElement.textContent = '医疗设备连接配置，数据传输管理';
                    break;
            }
        }

        // 返回启动界面
        function returnToLauncher() {
            console.log('🏠 返回启动界面');

            // 清理当前系统状态
            if (updateInterval) {
                clearInterval(updateInterval);
            }
            if (waveformChart) {
                waveformChart.destroy();
            }

            // 跳转回启动界面
            window.location.href = 'medical_system_launcher.html';
        }

        // 渲染检测对象网格
        function renderSubjectsGrid() {
            console.log('📊 渲染检测对象网格');

            const subjectsGrid = cachedElements.subjectsGrid || document.getElementById('subjectsGrid');
            if (!subjectsGrid) {
                throw new Error('subjectsGrid元素不存在');
            }

            subjectsGrid.innerHTML = '';

            // 根据不同模式显示不同内容
            switch (systemConfig.currentMode) {
                case 'short':
                    renderShortTermView(subjectsGrid);
                    break;
                case 'device':
                    renderDeviceConnectionView(subjectsGrid);
                    break;
                case 'long':
                default:
                    renderLongTermView(subjectsGrid);
                    break;
            }

            console.log(`✅ ${systemConfig.currentMode} 模式界面渲染完成`);
        }

        // 渲染短期检测视图（先显示正在检测界面，5秒后显示生理数据）
        function renderShortTermView(container) {
            console.log('⚡ 渲染短期检测视图');

            // 只显示P001对象的生理数据
            const subject = subjects['P001'];
            if (subject) {
                // 首先显示"正在检测"界面
                const loadingCard = createShortTermLoadingCard();
                container.appendChild(loadingCard);

                console.log('⏳ 显示正在检测界面，5秒后显示生理数据');

                // 5秒后显示生理数据
                setTimeout(() => {
                    container.innerHTML = '';
                    const dataCard = createShortTermDataCard(subject);
                    container.appendChild(dataCard);

                    // 绑定编辑字段的右键事件
                    bindEditableFieldEvents();

                    console.log('✅ 短期检测界面渲染完成，显示生理数据');
                }, 5000);
            }
        }

        // 创建短期检测专用数据展示卡片
        function createShortTermDataCard(subject) {
            const card = document.createElement('div');
            card.className = 'short-term-data-card';
            card.id = `short-term-${subject.id}`;

            const statusClass = subject.isActive ? 'active' : 'inactive';
            const statusText = subject.isActive ? '数据正常' : '设备未连接';
            const statusIcon = subject.isActive ? '🟢' : '🔴';

            card.innerHTML = `
                <div class="short-term-header">
                    <h2>⚡ 短期检测 - 生理数据监测</h2>
                    <div class="patient-info">
                        <span class="patient-name">${subject.name}</span>
                        <span class="patient-id">ID: ${subject.id}</span>
                        <span class="patient-status ${statusClass}">
                            ${statusIcon} ${statusText}
                        </span>
                    </div>
                </div>

                <div class="vital-data-grid">
                    <div class="vital-data-group">
                        <h3>💓 心血管系统</h3>
                        <div class="data-items">
                            <div class="data-item">
                                <span class="data-label">心率</span>
                                <span class="data-value">${Math.round(subject.vitalSigns.heartRate)}</span>
                                <span class="data-unit">次/分</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">收缩压</span>
                                <span class="data-value">${Math.round(subject.vitalSigns.systolicBP)}</span>
                                <span class="data-unit">mmHg</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">舒张压</span>
                                <span class="data-value">${Math.round(subject.vitalSigns.diastolicBP)}</span>
                                <span class="data-unit">mmHg</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">中心静脉压</span>
                                <span class="data-value">${Math.round(subject.vitalSigns.cvp)}</span>
                                <span class="data-unit">mmHg</span>
                            </div>
                        </div>
                    </div>

                    <div class="vital-data-group">
                        <h3>🫁 呼吸系统</h3>
                        <div class="data-items">
                            <div class="data-item">
                                <span class="data-label">血氧饱和度</span>
                                <span class="data-value">${Math.round(subject.vitalSigns.spo2)}</span>
                                <span class="data-unit">%</span>
                            </div>
                            <div class="data-item">
                                <span class="data-label">呼吸频率</span>
                                <span class="data-value">${Math.round(subject.vitalSigns.respiratoryRate)}</span>
                                <span class="data-unit">次/分</span>
                            </div>
                        </div>
                    </div>

                    <div class="vital-data-group">
                        <h3>🌡️ 体温调节</h3>
                        <div class="data-items">
                            <div class="data-item">
                                <span class="data-label">体温</span>
                                <span class="data-value">${subject.vitalSigns.temperature.toFixed(1)}</span>
                                <span class="data-unit">°C</span>
                            </div>
                        </div>
                    </div>

                    <div class="vital-data-group">
                        <h3>🍯 代谢系统</h3>
                        <div class="data-items">
                            <div class="data-item">
                                <span class="data-label">血糖</span>
                                <span class="data-value">${subject.vitalSigns.bloodGlucose.toFixed(1)}</span>
                                <span class="data-unit">mmol/L</span>
                            </div>
                        </div>
                    </div>

                    <div class="vital-data-group">
                        <h3>📈 心电系统</h3>
                        <div class="data-items">
                            <div class="data-item">
                                <span class="data-label">QT间期</span>
                                <span class="data-value">${Math.round(subject.vitalSigns.qtInterval)}</span>
                                <span class="data-unit">ms</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="short-term-footer">
                    <p>📊 实时生理数据监测 | 数据更新频率: 每3秒</p>
                </div>
            `;

            return card;
        }

        // 创建短期检测"正在检测"界面
        function createShortTermLoadingCard() {
            const card = document.createElement('div');
            card.className = 'short-term-loading-card';
            card.id = 'short-term-loading';

            card.innerHTML = `
                <div class="loading-header">
                    <h2>⚡ 短期检测系统</h2>
                    <p>正在初始化生理监测设备...</p>
                </div>

                <div class="loading-animation">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">正在检测</div>
                    <div class="loading-dots">
                        <span class="dot"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                    </div>
                </div>

                <div class="loading-progress">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-text">检测进度: <span id="progressPercent">0</span>%</div>
                </div>

                <div class="loading-status">
                    <div class="status-item">
                        <span class="status-icon">🔍</span>
                        <span class="status-text">扫描生理参数传感器...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-icon">📡</span>
                        <span class="status-text">建立数据连接...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-icon">⚙️</span>
                        <span class="status-text">校准监测设备...</span>
                    </div>
                    <div class="status-item">
                        <span class="status-icon">📊</span>
                        <span class="status-text">准备数据显示...</span>
                    </div>
                </div>

                <div class="loading-footer">
                    <p>🔔 请保持患者静止状态，确保检测准确性</p>
                    <p>⏱️ 预计检测时间：5秒</p>
                </div>
            `;

            // 启动进度条动画
            setTimeout(() => {
                startLoadingAnimation();
            }, 100);

            return card;
        }

        // 启动加载动画
        function startLoadingAnimation() {
            const progressFill = document.querySelector('.progress-fill');
            const progressPercent = document.getElementById('progressPercent');
            const statusItems = document.querySelectorAll('.status-item');

            if (!progressFill || !progressPercent) return;

            let progress = 0;
            const interval = setInterval(() => {
                progress += 2;
                progressFill.style.width = progress + '%';
                progressPercent.textContent = progress;

                // 激活状态项
                if (progress >= 25 && statusItems[0]) {
                    statusItems[0].classList.add('active');
                }
                if (progress >= 50 && statusItems[1]) {
                    statusItems[1].classList.add('active');
                }
                if (progress >= 75 && statusItems[2]) {
                    statusItems[2].classList.add('active');
                }
                if (progress >= 95 && statusItems[3]) {
                    statusItems[3].classList.add('active');
                }

                if (progress >= 100) {
                    clearInterval(interval);
                }
            }, 100); // 每100ms增加2%，总共5秒
        }

        // 渲染长期监测视图（显示所有对象）
        function renderLongTermView(container) {
            console.log('📊 渲染长期监测视图');

            Object.values(subjects).forEach(subject => {
                const subjectCard = createSubjectCard(subject);
                container.appendChild(subjectCard);
            });

            // 绑定编辑字段的右键事件
            bindEditableFieldEvents();
        }

        // 渲染设备连接视图
        function renderDeviceConnectionView(container) {
            console.log('🔗 渲染设备连接视图');

            let deviceCardsHTML = '';

            Object.keys(deviceStates).forEach(roomNumber => {
                const device = deviceStates[roomNumber];
                const icon = '🏥'; // 统一使用医院图标

                let statusClass = 'disconnected';
                let statusText = '未连接';
                let cardClass = 'device-card';
                let deviceInfo = '';

                if (device.connecting) {
                    statusClass = 'connecting';
                    statusText = '连接中';
                    deviceInfo = `
                        <p>房间号: ${roomNumber}</p>
                        <p>位置: ${device.room}</p>
                        <p>型号: ${device.model}</p>
                        <p>状态: 正在连接</p>
                        <p>进度: ${Math.floor(Math.random() * 40) + 60}%</p>
                    `;
                } else if (device.connected) {
                    statusClass = 'connected';
                    statusText = '已连接';
                    cardClass = 'device-card active';
                    deviceInfo = `
                        <p>房间号: ${roomNumber}</p>
                        <p>位置: ${device.room}</p>
                        <p>型号: ${device.model}</p>
                        <p>信号强度: ${Math.floor(Math.random() * 20) + 80}%</p>
                        <p>最后同步: 刚刚</p>
                    `;
                } else {
                    deviceInfo = `
                        <p>房间号: ${roomNumber}</p>
                        <p>位置: ${device.room}</p>
                        <p>型号: ${device.model}</p>
                        <p>状态: 待连接</p>
                        <p>最后连接: ${getLastConnectionTime(roomNumber)}</p>
                    `;
                }

                deviceCardsHTML += `
                    <div class="${cardClass}" onclick="connectDevice('${roomNumber}')">
                        <div class="device-icon">${icon}</div>
                        <div class="device-name">${device.name}</div>
                        <div class="device-id">房间 ${roomNumber}</div>
                        <div class="device-status ${statusClass}">${statusText}</div>
                        <div class="device-info">
                            ${deviceInfo}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = `
                <div class="device-connection-container">
                    <div class="device-header">
                        <h2>🔗 生理监测设备连接管理</h2>
                        <p>选择房间号连接对应的生理监测仪</p>
                    </div>

                    <div class="device-grid">
                        ${deviceCardsHTML}
                    </div>

                    <div class="device-actions">
                        <button class="action-btn scan-btn" onclick="scanDevices()">
                            <span class="btn-icon">🔍</span>
                            <span class="btn-text">扫描设备</span>
                        </button>
                        <button class="action-btn refresh-btn" onclick="refreshDevices()">
                            <span class="btn-icon">🔄</span>
                            <span class="btn-text">刷新状态</span>
                        </button>
                        <button class="action-btn settings-btn" onclick="deviceSettings()">
                            <span class="btn-icon">⚙️</span>
                            <span class="btn-text">连接设置</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // 获取设备最后连接时间
        function getLastConnectionTime(roomNumber) {
            const times = {
                '101': '刚刚',
                '102': '2小时前',
                '103': '1天前',
                '104': '30分钟前',
                '105': '从未连接',
                '106': '3小时前',
                '201': '1天前',
                '202': '5小时前',
                '203': '未连接',
                '204': '2天前',
                '301': '1周前',
                '302': '未连接'
            };
            return times[roomNumber] || '未知';
        }

        // 创建检测对象卡片
        function createSubjectCard(subject) {
            const card = document.createElement('div');
            card.className = `subject-card ${subject.isActive ? 'active' : 'inactive'}`;

            const statusClass = subject.isActive ? 'status-normal' : 'status-inactive';
            const statusText = subject.isActive ? '正常' : '未连接';

            card.innerHTML = `
                <div class="subject-header">
                    <div class="subject-id">${subject.id}</div>
                    <div class="subject-status ${statusClass}">${statusText}</div>
                </div>
                <div class="subject-info">
                    <p><strong>姓名:</strong> <span class="editable-field" data-field="name" data-subject="${subject.id}">${subject.name}</span></p>
                    <p><strong>年龄:</strong> <span class="editable-field" data-field="age" data-subject="${subject.id}">${subject.age}</span>岁</p>
                    <p><strong>性别:</strong> <span class="editable-field" data-field="gender" data-subject="${subject.id}">${subject.gender}</span></p>
                    <p><strong>房间:</strong> <span class="editable-field" data-field="room" data-subject="${subject.id}">${subject.room}</span></p>
                </div>
                ${subject.isActive && subject.vitalSigns ? `
                <div class="vital-preview">
                    <div class="vital-item">
                        <span class="vital-label">心率:</span>
                        <span class="vital-value">${subject.vitalSigns.heartRate} 次/分</span>
                    </div>
                    <div class="vital-item">
                        <span class="vital-label">血压:</span>
                        <span class="vital-value">${subject.vitalSigns.systolicBP}/${subject.vitalSigns.diastolicBP}</span>
                    </div>
                    <div class="vital-item">
                        <span class="vital-label">血氧:</span>
                        <span class="vital-value">${subject.vitalSigns.spo2}%</span>
                    </div>
                    <div class="vital-item">
                        <span class="vital-label">体温:</span>
                        <span class="vital-value">${subject.vitalSigns.temperature}°C</span>
                    </div>
                </div>
                <div class="card-actions">
                    <button class="action-btn detail-btn" onclick="enterDetailMonitoring('${subject.id}')">
                        <span class="btn-icon">📊</span>
                        <span class="btn-text">详细监测</span>
                    </button>
                    <button class="action-btn waveform-btn" onclick="enterWaveformView('${subject.id}', 'heartRate')">
                        <span class="btn-icon">📈</span>
                        <span class="btn-text">波形图</span>
                    </button>
                </div>
                ` : `
                <div style="text-align: center; color: #999; padding: 20px;">暂无数据</div>
                <div class="card-actions">
                    <button class="action-btn disabled-btn" disabled>
                        <span class="btn-icon">⚠️</span>
                        <span class="btn-text">设备未连接</span>
                    </button>
                </div>
                `}
            `;

            return card;
        }

        // 更新状态信息
        function updateStatusInfo() {
            const statusInfo = cachedElements.statusInfo || document.getElementById('statusInfo');
            if (!statusInfo) return;

            statusInfo.className = 'status-info';

            // 根据不同模式显示不同的状态信息
            switch (systemConfig.currentMode) {
                case 'short':
                    const subject = subjects['P001'];
                    const status = subject && subject.isActive ? '数据正常' : '设备未连接';
                    statusInfo.textContent = `⚡ 短期检测模式 - 监测对象: ${subject?.name || 'P001'} | 状态: ${status}`;
                    break;

                case 'device':
                    const connectedDevices = Object.values(deviceStates).filter(d => d.connected).length;
                    const totalDevices = Object.keys(deviceStates).length;
                    const connectingDevices = Object.values(deviceStates).filter(d => d.connecting).length;

                    let deviceStatus = `${connectedDevices}/${totalDevices} 设备已连接`;
                    if (connectingDevices > 0) {
                        deviceStatus += ` | ${connectingDevices} 设备连接中`;
                    }
                    statusInfo.textContent = `🔗 设备连接模式 - ${deviceStatus}`;
                    break;

                case 'long':
                default:
                    const totalSubjects = Object.keys(subjects).length;
                    const activeSubjects = Object.values(subjects).filter(s => s.isActive).length;
                    statusInfo.textContent = `📊 长期监测模式 - 当前监测${totalSubjects}个对象，${activeSubjects}个对象数据有效`;
                    break;
            }

            console.log(`📊 状态更新: ${systemConfig.currentMode} 模式`);
        }

        // 生成历史数据
        function generateHistoryData() {
            console.log('📊 生成历史数据');

            const now = new Date();
            historyData.timeLabels = [];

            // 为每个参数初始化数组
            Object.keys(parameterConfigs).forEach(param => {
                historyData[param] = [];
            });

            // 生成过去30分钟的数据
            for (let i = 29; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 60000);
                historyData.timeLabels.push(time.toLocaleTimeString().slice(0, 5));

                const subject = subjects['P001'];
                if (subject && subject.vitalSigns) {
                    // 心率数据（稳定范围）
                    const baseHR = subject.vitalSigns.heartRate + (Math.random() - 0.5) * 4;
                    historyData.heartRate.push(Math.round(Math.max(68, Math.min(78, baseHR))));

                    // 血压数据（收缩压，稳定范围）
                    const baseBP = subject.vitalSigns.systolicBP + (Math.random() - 0.5) * 6;
                    historyData.bloodPressure.push(Math.round(Math.max(115, Math.min(125, baseBP))));

                    // 血氧数据（高稳定性）
                    const baseSpo2 = subject.vitalSigns.spo2 + (Math.random() - 0.5) * 1;
                    historyData.spo2.push(Math.round(Math.max(97, Math.min(99, baseSpo2))));

                    // 体温数据（微小波动）
                    const baseTemp = subject.vitalSigns.temperature + (Math.random() - 0.5) * 0.2;
                    historyData.temperature.push(parseFloat(Math.max(36.3, Math.min(36.7, baseTemp)).toFixed(1)));

                    // 呼吸频率数据（稳定范围）
                    const baseResp = subject.vitalSigns.respiratoryRate + (Math.random() - 0.5) * 2;
                    historyData.respiratoryRate.push(Math.round(Math.max(15, Math.min(17, baseResp))));

                    // 血糖数据（稳定范围）
                    const baseGlucose = subject.vitalSigns.bloodGlucose + (Math.random() - 0.5) * 0.4;
                    historyData.bloodGlucose.push(parseFloat(Math.max(5.0, Math.min(5.4, baseGlucose)).toFixed(1)));

                    // 心电数据（稳定范围）
                    const baseECG = subject.vitalSigns.qtInterval + (Math.random() - 0.5) * 15;
                    historyData.ecgRhythm.push(Math.round(Math.max(390, Math.min(410, baseECG))));

                    // 中心静脉压数据（稳定范围）
                    const baseCVP = subject.vitalSigns.cvp + (Math.random() - 0.5) * 2;
                    historyData.cvp.push(Math.round(Math.max(7, Math.min(9, baseCVP))));
                }
            }

            console.log('✅ 历史数据生成完成');
        }

        // 开始数据更新
        function startDataUpdate() {
            updateInterval = setInterval(() => {
                updateVitalSigns();
                updateHistoryData();

                // 如果在波形视图中，更新波形
                if (currentParameter && waveformChart) {
                    updateWaveformChart();
                }
            }, 3000);
        }

        // 更新生理数据
        function updateVitalSigns() {
            const subject = subjects['P001'];
            if (!subject || !subject.vitalSigns) return;

            // 根据系统配置决定是否生成异常数据
            const shouldGenerateAbnormal = systemConfig.enableAbnormalData &&
                                         Math.random() < systemConfig.abnormalDataProbability;

            if (shouldGenerateAbnormal) {
                console.log('🎭 生成异常数据用于演示报警系统');
                generateAbnormalData(subject);
            } else {
                // 正常的数据变化（稳定状态）
                updateNormalVitalSigns(subject);
            }

            // 检测数据异常
            const abnormalities = checkDataAbnormalities(subject);

            if (abnormalities.length > 0) {
                console.log('🚨 检测到数据异常:', abnormalities);
                triggerAlert(abnormalities, subject);
            } else {
                // 如果没有异常，清除之前的异常状态
                if (alertSystem.isAcknowledged) {
                    hideAbnormalIndicator();
                    clearAbnormalHighlights();
                }
            }
        }

        // 正常的生理数据更新（稳定状态，小幅波动）
        function updateNormalVitalSigns(subject) {
            // 心率变化（减少波动幅度）
            subject.vitalSigns.heartRate += (Math.random() - 0.5) * 2;
            subject.vitalSigns.heartRate = Math.max(70, Math.min(78, subject.vitalSigns.heartRate));

            // 血压变化（更稳定的范围）
            subject.vitalSigns.systolicBP += (Math.random() - 0.5) * 3;
            subject.vitalSigns.systolicBP = Math.max(118, Math.min(125, subject.vitalSigns.systolicBP));
            subject.vitalSigns.diastolicBP += (Math.random() - 0.5) * 2;
            subject.vitalSigns.diastolicBP = Math.max(78, Math.min(82, subject.vitalSigns.diastolicBP));

            // 血氧变化（保持高稳定性）
            subject.vitalSigns.spo2 += (Math.random() - 0.5) * 0.3;
            subject.vitalSigns.spo2 = Math.max(97.5, Math.min(98.5, subject.vitalSigns.spo2));

            // 体温变化（非常小的波动）
            subject.vitalSigns.temperature += (Math.random() - 0.5) * 0.05;
            subject.vitalSigns.temperature = Math.max(36.4, Math.min(36.6, subject.vitalSigns.temperature));

            // 其他参数的微小变化
            subject.vitalSigns.respiratoryRate += (Math.random() - 0.5) * 0.5;
            subject.vitalSigns.respiratoryRate = Math.max(15.5, Math.min(16.5, subject.vitalSigns.respiratoryRate));

            subject.vitalSigns.bloodGlucose += (Math.random() - 0.5) * 0.1;
            subject.vitalSigns.bloodGlucose = Math.max(5.1, Math.min(5.3, subject.vitalSigns.bloodGlucose));

            subject.vitalSigns.qtInterval += (Math.random() - 0.5) * 5;
            subject.vitalSigns.qtInterval = Math.max(395, Math.min(405, subject.vitalSigns.qtInterval));

            subject.vitalSigns.cvp += (Math.random() - 0.5) * 0.5;
            subject.vitalSigns.cvp = Math.max(7.5, Math.min(8.5, subject.vitalSigns.cvp));
        }

        // 生成异常数据（仅限血糖轻微异常）
        function generateAbnormalData(subject) {
            // 只允许血糖异常，其他参数保持正常
            const abnormalTypes = ['mildHighGlucose', 'mildLowGlucose'];

            const selectedAbnormal = abnormalTypes[Math.floor(Math.random() * abnormalTypes.length)];

            // 首先确保其他所有参数都在正常范围内
            updateNormalVitalSigns(subject);

            // 只对血糖进行轻微的异常调整
            switch (selectedAbnormal) {
                case 'mildHighGlucose':
                    // 血糖稍微超过正常上限（6.1），但不会太高
                    subject.vitalSigns.bloodGlucose = 6.2 + Math.random() * 0.8; // 6.2-7.0 mmol/L
                    console.log(`🍯 血糖轻微偏高: ${subject.vitalSigns.bloodGlucose.toFixed(1)} mmol/L`);
                    break;
                case 'mildLowGlucose':
                    // 血糖稍微低于正常下限（3.9），但不会太低
                    subject.vitalSigns.bloodGlucose = 3.5 + Math.random() * 0.3; // 3.5-3.8 mmol/L
                    console.log(`🍯 血糖轻微偏低: ${subject.vitalSigns.bloodGlucose.toFixed(1)} mmol/L`);
                    break;
            }

            console.log(`🎭 生成轻微血糖异常: ${selectedAbnormal}`);
        }

        // 更新历史数据
        function updateHistoryData() {
            const now = new Date().toLocaleTimeString().slice(0, 5);
            const subject = subjects['P001'];

            if (!subject || !subject.vitalSigns) return;

            historyData.timeLabels.push(now);
            historyData.heartRate.push(Math.round(subject.vitalSigns.heartRate));
            historyData.bloodPressure.push(Math.round(subject.vitalSigns.systolicBP));
            historyData.spo2.push(Math.round(subject.vitalSigns.spo2));
            historyData.temperature.push(parseFloat(subject.vitalSigns.temperature.toFixed(1)));
            historyData.respiratoryRate.push(Math.round(subject.vitalSigns.respiratoryRate));
            historyData.bloodGlucose.push(parseFloat(subject.vitalSigns.bloodGlucose.toFixed(1)));
            historyData.ecgRhythm.push(Math.round(subject.vitalSigns.qtInterval));
            historyData.cvp.push(Math.round(subject.vitalSigns.cvp));

            // 限制数据点数量
            const maxPoints = 30;
            if (historyData.timeLabels.length > maxPoints) {
                historyData.timeLabels.shift();
                Object.keys(parameterConfigs).forEach(param => {
                    if (historyData[param].length > maxPoints) {
                        historyData[param].shift();
                    }
                });
            }
        }

        // 显示错误信息
        function showError(message) {
            const statusInfo = document.getElementById('statusInfo');
            statusInfo.className = 'error-message';
            statusInfo.textContent = message;
        }

        // 进入详细监测
        function enterDetailMonitoring(subjectId) {
            console.log('🔍 进入详细监测:', subjectId);

            currentSubjectId = subjectId;
            const subject = subjects[subjectId];

            if (!subject || !subject.isActive) {
                console.error('❌ 对象无效或未激活:', subjectId);
                alert('该对象未连接或数据无效');
                return;
            }

            // 切换界面
            const managementView = document.getElementById('managementView');
            const detailView = document.getElementById('detailView');

            console.log('🔄 界面切换前状态:', {
                managementViewClasses: managementView?.className,
                detailViewClasses: detailView?.className
            });

            managementView.classList.add('hidden');
            detailView.classList.add('active');

            console.log('🔄 界面切换后状态:', {
                managementViewClasses: managementView?.className,
                detailViewClasses: detailView?.className
            });

            // 更新详细信息
            document.getElementById('detailSubjectName').textContent = `${subject.name} - 详细监测`;
            document.getElementById('detailSubjectInfo').textContent =
                `${subject.id} | ${subject.age}岁 ${subject.gender} | 房间: ${subject.room}`;

            // 渲染生理参数
            renderVitalSigns(subject);

            console.log('✅ 详细监测界面已显示');
        }

        // 直接进入波形图界面
        function enterWaveformView(subjectId, parameterType = 'heartRate') {
            console.log('📈 直接进入波形图:', subjectId, parameterType);

            currentSubjectId = subjectId;
            currentParameter = parameterType;
            const subject = subjects[subjectId];

            if (!subject || !subject.isActive) {
                console.error('❌ 对象无效或未激活:', subjectId);
                alert('该对象未连接或数据无效');
                return;
            }

            // 隐藏其他界面
            document.getElementById('managementView').classList.add('hidden');
            document.getElementById('detailView').classList.remove('active');

            // 显示波形界面
            document.getElementById('waveformView').classList.add('active');

            // 更新波形界面信息
            const config = parameterConfigs[parameterType];
            if (config) {
                const elements = {
                    icon: document.getElementById('waveformIcon'),
                    title: document.getElementById('waveformTitle'),
                    value: document.getElementById('waveformCurrentValue'),
                    chartTitle: document.getElementById('chartTitle')
                };

                if (elements.icon) elements.icon.textContent = config.icon;
                if (elements.title) elements.title.textContent = config.title;
                if (elements.value) {
                    elements.value.textContent = config.getDisplayValue(subject);
                    elements.value.style.color = config.color;
                }
                if (elements.chartTitle) elements.chartTitle.textContent = `${config.title.replace('分析', '')}变化趋势`;

                // 延迟初始化图表，确保界面已显示
                setTimeout(() => {
                    console.log('⏰ 开始初始化波形图表');
                    initializeWaveformChart(parameterType, config);
                    updateStatistics(parameterType, config, subject);
                }, 150);
            }

            console.log('✅ 波形图界面已显示');
        }

        // 渲染生理参数
        function renderVitalSigns(subject) {
            const vitalsGrid = document.getElementById('vitalsGrid');
            vitalsGrid.innerHTML = '';

            const vitalCards = [
                { id: 'heartRate', class: 'heart-rate', label: '心率 (次/分)' },
                { id: 'bloodPressure', class: 'blood-pressure', label: '血压 (mmHg)' },
                { id: 'spo2', class: 'spo2', label: '血氧饱和度 (%)' },
                { id: 'temperature', class: 'temperature', label: '体温 (°C)' },
                { id: 'respiratoryRate', class: 'respiratory-rate', label: '呼吸频率 (次/分)' },
                { id: 'bloodGlucose', class: 'blood-glucose', label: '血糖 (mmol/L)' },
                { id: 'ecgRhythm', class: 'ecg-rhythm', label: 'QT间期 (ms)' },
                { id: 'cvp', class: 'cvp', label: '中心静脉压 (mmHg)' }
            ];

            vitalCards.forEach(card => {
                const config = parameterConfigs[card.id];
                if (!config) return;

                const cardElement = document.createElement('div');
                cardElement.className = `vital-card ${card.class}`;
                cardElement.onclick = () => showWaveform(card.id);
                cardElement.innerHTML = `
                    <div class="icon">${config.icon}</div>
                    <div class="value">${config.getValue(subject)}</div>
                    <div class="label">${card.label}</div>
                    <div class="range">${config.range}</div>
                `;
                vitalsGrid.appendChild(cardElement);
            });

            console.log(`✅ 生理参数渲染完成，共 ${vitalCards.length} 个参数`);
        }

        // 显示波形图
        function showWaveform(parameterType) {
            console.log('📈 显示波形图:', parameterType);
            console.log('🔍 当前对象ID:', currentSubjectId);
            console.log('🔍 参数配置:', parameterConfigs[parameterType]);

            currentParameter = parameterType;
            const config = parameterConfigs[parameterType];
            const subject = subjects[currentSubjectId];

            if (!config || !subject) {
                console.error('❌ 参数配置或对象不存在', { config, subject });
                return;
            }

            // 检查DOM元素
            const detailView = document.getElementById('detailView');
            const waveformView = document.getElementById('waveformView');
            const managementView = document.getElementById('managementView');

            console.log('🔍 DOM元素检查:', {
                detailView: !!detailView,
                waveformView: !!waveformView,
                detailViewClasses: detailView?.className,
                waveformViewClasses: waveformView?.className
            });

            // 记录当前界面状态
            if (detailView && detailView.classList.contains('active')) {
                previousView = 'detail';
                console.log('📝 记录上一级界面: 详细监测');
            } else if (managementView && !managementView.classList.contains('hidden')) {
                previousView = 'management';
                console.log('📝 记录上一级界面: 主界面');
            } else {
                previousView = 'detail'; // 默认返回详细监测
                console.log('📝 默认上一级界面: 详细监测');
            }

            // 切换界面 - 保持详细监测界面的active状态以便返回
            if (waveformView) {
                waveformView.classList.add('active');
                // 强制显示波形视图
                waveformView.style.display = 'block';
                waveformView.style.opacity = '1';
                waveformView.style.visibility = 'visible';
                console.log('✅ 波形视图已激活，类名:', waveformView.className);
                console.log('✅ 波形视图样式:', waveformView.style.display);
            }

            // 隐藏详细监测界面但保持其active状态
            if (detailView) {
                detailView.style.display = 'none';
                console.log('✅ 详细监测界面已隐藏但保持active状态');
            }

            // 更新波形视图标题
            const elements = {
                icon: document.getElementById('waveformIcon'),
                title: document.getElementById('waveformTitle'),
                value: document.getElementById('waveformCurrentValue'),
                chartTitle: document.getElementById('chartTitle')
            };

            console.log('🔍 标题元素检查:', Object.keys(elements).reduce((acc, key) => {
                acc[key] = !!elements[key];
                return acc;
            }, {}));

            if (elements.icon) {
                elements.icon.textContent = config.icon;
                elements.icon.style.color = config.color;
            }
            if (elements.title) elements.title.textContent = config.title;
            if (elements.value) {
                elements.value.textContent = config.getDisplayValue(subject);
                elements.value.style.color = config.color;
            }
            if (elements.chartTitle) elements.chartTitle.textContent = `${config.title.replace('分析', '')}变化趋势`;

            // 延迟初始化图表，确保界面已显示
            setTimeout(() => {
                console.log('⏰ 开始初始化图表');
                console.log('🔍 波形视图状态:', {
                    display: waveformView?.style.display,
                    visibility: waveformView?.style.visibility,
                    opacity: waveformView?.style.opacity,
                    classes: waveformView?.className
                });
                initializeWaveformChart(parameterType, config);
                updateStatistics(parameterType, config, subject);
            }, 200);
        }

        // 初始化波形图表
        function initializeWaveformChart(parameterType, config) {
            console.log('🎨 初始化波形图表:', parameterType);
            console.log('🔍 配置信息:', config);

            // 销毁旧图表
            if (waveformChart) {
                console.log('🗑️ 销毁旧图表');
                waveformChart.destroy();
                waveformChart = null;
            }

            const canvas = document.getElementById('waveformChart');
            console.log('🔍 画布元素:', !!canvas);

            if (!canvas) {
                console.error('❌ 未找到图表画布');
                return;
            }

            // 检查画布是否可见
            const rect = canvas.getBoundingClientRect();
            console.log('📐 画布尺寸:', rect);

            if (rect.width === 0 || rect.height === 0) {
                console.warn('⚠️ 画布不可见，延迟重试');
                setTimeout(() => initializeWaveformChart(parameterType, config), 200);
                return;
            }

            const ctx = canvas.getContext('2d');
            const data = historyData[parameterType] || [];

            console.log('📊 图表数据详情:', {
                parameterType,
                labels: historyData.timeLabels?.length || 0,
                data: data.length,
                sample: data.slice(-5),
                allHistoryKeys: Object.keys(historyData),
                timeLabels: historyData.timeLabels?.slice(-3) || []
            });

            console.log('🔍 Chart.js可用性:', typeof Chart !== 'undefined');

            try {
                waveformChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: historyData.timeLabels,
                        datasets: [{
                            label: config.title.replace('分析', ''),
                            data: data,
                            borderColor: config.color,
                            backgroundColor: config.color + '20',
                            tension: 0.4,
                            fill: true,
                            pointRadius: 3,
                            pointHoverRadius: 6,
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    font: { size: 14 },
                                    usePointStyle: true
                                }
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: '时间',
                                    font: { size: 14 }
                                },
                                grid: { color: 'rgba(0,0,0,0.1)' }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: `${config.title.replace('分析', '')} (${config.unit})`,
                                    font: { size: 14 }
                                },
                                grid: { color: 'rgba(0,0,0,0.1)' }
                            }
                        },
                        animation: {
                            duration: 200,
                            easing: 'linear'
                        }
                    }
                });

                console.log('✅ 波形图表创建成功');
                console.log('📊 图表实例:', waveformChart);

                // 强制重新渲染
                setTimeout(() => {
                    if (waveformChart) {
                        waveformChart.resize();
                        waveformChart.update();
                        console.log('🔄 图表已重新渲染');
                    }
                }, 100);

            } catch (error) {
                console.error('❌ 图表创建失败:', error);
                console.error('❌ 错误详情:', {
                    message: error.message,
                    stack: error.stack,
                    canvas: !!canvas,
                    ctx: !!ctx,
                    Chart: typeof Chart,
                    data: data?.length,
                    labels: historyData.timeLabels?.length
                });

                // 显示用户友好的错误信息
                const chartContainer = document.querySelector('.chart-wrapper');
                if (chartContainer) {
                    chartContainer.innerHTML = `
                        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; color: #666;">
                            <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                            <div style="font-size: 18px; margin-bottom: 10px;">波形图加载失败</div>
                            <div style="font-size: 14px; color: #999;">请刷新页面重试</div>
                        </div>
                    `;
                }
            }
        }

        // 更新波形图表
        function updateWaveformChart() {
            if (!waveformChart || !currentParameter || !currentSubjectId) return;

            const config = parameterConfigs[currentParameter];
            const subject = subjects[currentSubjectId];
            const data = historyData[currentParameter] || [];

            // 更新当前值显示
            document.getElementById('waveformCurrentValue').textContent = config.getDisplayValue(subject);

            // 更新图表数据
            waveformChart.data.labels = historyData.timeLabels;
            waveformChart.data.datasets[0].data = data;
            waveformChart.update('none');

            // 更新统计信息
            updateStatistics(currentParameter, config, subject);
        }

        // 更新统计信息
        function updateStatistics(parameterType, config, subject) {
            const data = historyData[parameterType] || [];

            if (data.length === 0) return;

            const current = config.getValue(subject);
            const sum = data.reduce((a, b) => a + b, 0);
            const average = sum / data.length;
            const max = Math.max(...data);
            const min = Math.min(...data);

            document.getElementById('currentStat').textContent = current;
            document.getElementById('averageStat').textContent = average.toFixed(1);
            document.getElementById('maxStat').textContent = max.toFixed(1);
            document.getElementById('minStat').textContent = min.toFixed(1);
        }

        // 返回管理界面
        function backToManagement() {
            console.log('🔙 返回管理界面');

            // 调试当前状态
            debugInterfaceState();

            const detailView = document.getElementById('detailView');
            const managementView = document.getElementById('managementView');

            // 检查DOM元素是否存在
            if (!detailView || !managementView) {
                console.error('❌ 界面元素未找到:', {
                    detailView: !!detailView,
                    managementView: !!managementView
                });
                return;
            }

            console.log('🔍 当前界面状态:', {
                detailViewClasses: detailView.className,
                managementViewClasses: managementView.className
            });

            // 隐藏详细监测界面
            detailView.classList.remove('active');
            detailView.style.display = 'none';

            // 显示管理界面
            managementView.classList.remove('hidden');
            managementView.style.display = 'block';

            // 清理状态
            currentSubjectId = null;
            previousView = null;

            // 清理任何可能存在的波形图资源
            if (waveformChart) {
                waveformChart.destroy();
                waveformChart = null;
                console.log('🗑️ 波形图表已销毁');
            }

            console.log('✅ 已返回管理界面');
        }

        // 返回详细监测或主界面
        function backToDetail() {
            console.log('🔙 返回上一级界面');
            console.log('🔍 当前对象ID:', currentSubjectId);
            console.log('🔍 当前参数:', currentParameter);

            const waveformView = document.getElementById('waveformView');
            const detailView = document.getElementById('detailView');
            const managementView = document.getElementById('managementView');

            // 关闭波形图界面
            if (waveformView) {
                waveformView.classList.remove('active');
                waveformView.style.display = 'none';
                console.log('✅ 波形视图已隐藏');
            }

            // 根据记录的上一级界面状态返回
            if (previousView === 'detail' && currentSubjectId && detailView) {
                // 返回到详细监测界面
                console.log('🔙 返回详细监测界面');
                detailView.classList.add('active');
                detailView.style.display = 'block';

                // 确保主界面隐藏
                if (managementView) {
                    managementView.classList.add('hidden');
                }
            } else {
                // 返回到主界面
                console.log('🔙 返回主界面');
                if (detailView) {
                    detailView.classList.remove('active');
                    detailView.style.display = 'none';
                }
                if (managementView) {
                    managementView.classList.remove('hidden');
                }
                currentSubjectId = null;
            }

            // 清理上一级界面记录
            previousView = null;

            // 清理波形图相关资源
            currentParameter = null;
            if (waveformChart) {
                waveformChart.destroy();
                waveformChart = null;
                console.log('🗑️ 波形图表已销毁');
            }

            console.log('✅ 返回操作完成');
        }

        // 新增：从波形图直接返回主界面
        function backToManagementFromWaveform() {
            console.log('🔙 从波形图返回主界面');

            const waveformView = document.getElementById('waveformView');
            const detailView = document.getElementById('detailView');
            const managementView = document.getElementById('managementView');

            // 关闭所有子界面
            if (waveformView) {
                waveformView.classList.remove('active');
                waveformView.style.display = 'none';
            }
            if (detailView) {
                detailView.classList.remove('active');
                detailView.style.display = 'none';
            }

            // 显示主界面
            if (managementView) {
                managementView.classList.remove('hidden');
            }

            // 清理状态
            currentSubjectId = null;
            currentParameter = null;
            previousView = null;

            if (waveformChart) {
                waveformChart.destroy();
                waveformChart = null;
                console.log('🗑️ 波形图表已销毁');
            }

            console.log('✅ 已返回主界面');
        }

        // ==================== 设备连接管理功能 ====================

        // 设备连接状态（按房间号管理生理监测仪）- 只有一个设备连接
        const deviceStates = {
            '101': { connected: true, name: '生理监测仪', type: 'physiological', model: 'PM-2024', room: '101房间' },
            '102': { connected: false, name: '生理监测仪', type: 'physiological', model: 'PM-2024', room: '102房间' },
            '103': { connected: false, name: '生理监测仪', type: 'physiological', model: 'PM-2024', room: '103房间' },
            '104': { connected: false, name: '生理监测仪', type: 'physiological', model: 'PM-2024', room: '104房间' },
            '105': { connected: false, name: '生理监测仪', type: 'physiological', model: 'PM-2024', room: '105房间' },
            '106': { connected: false, name: '生理监测仪', type: 'physiological', model: 'PM-2024', room: '106房间' },
            '201': { connected: false, name: '生理监测仪', type: 'physiological', model: 'PM-2024', room: '201房间' },
            '202': { connected: false, name: '生理监测仪', type: 'physiological', model: 'PM-2024', room: '202房间' },
            '203': { connected: false, name: '生理监测仪', type: 'physiological', model: 'PM-2024', room: '203房间' },
            '204': { connected: false, name: '生理监测仪', type: 'physiological', model: 'PM-2024', room: '204房间' },
            '301': { connected: false, name: '生理监测仪', type: 'physiological', model: 'PM-2024', room: '301房间' },
            '302': { connected: false, name: '生理监测仪', type: 'physiological', model: 'PM-2024', room: '302房间' }
        };

        // 连接设备
        function connectDevice(roomNumber) {
            console.log(`🔗 尝试连接房间 ${roomNumber} 的生理监测仪`);

            const device = deviceStates[roomNumber];
            if (!device) {
                console.error('❌ 未知房间号:', roomNumber);
                return;
            }

            if (device.connected) {
                // 如果已连接，显示设备详情
                showDeviceDetails(roomNumber);
            } else {
                // 开始连接过程
                startDeviceConnection(roomNumber);
            }
        }

        // 开始设备连接
        function startDeviceConnection(roomNumber) {
            const device = deviceStates[roomNumber];
            device.connecting = true;

            console.log(`⏳ 正在连接房间 ${roomNumber} 的${device.name}...`);

            // 重新渲染界面以显示连接状态
            renderSubjectsGrid();

            // 模拟连接过程
            setTimeout(() => {
                const success = Math.random() > 0.3; // 70%成功率

                if (success) {
                    device.connected = true;
                    device.connecting = false;
                    console.log(`✅ 房间 ${roomNumber} 的${device.name}连接成功`);
                    showSuccessMessage(`房间 ${roomNumber} 的${device.name}连接成功！`);
                } else {
                    device.connecting = false;
                    console.log(`❌ 房间 ${roomNumber} 的${device.name}连接失败`);
                    showErrorMessage(`房间 ${roomNumber} 的${device.name}连接失败，请检查设备状态`);
                }

                // 重新渲染界面
                renderSubjectsGrid();
            }, 2000 + Math.random() * 3000); // 2-5秒连接时间
        }

        // 显示设备详情
        function showDeviceDetails(roomNumber) {
            const device = deviceStates[roomNumber];
            alert(`设备详情：\n\n房间号: ${roomNumber}\n位置: ${device.room}\n设备名称: ${device.name}\n设备型号: ${device.model}\n设备类型: ${device.type}\n连接状态: ${device.connected ? '已连接' : '未连接'}\n\n功能：查看设备参数、断开连接、设备设置`);
        }

        // 扫描设备
        function scanDevices() {
            console.log('🔍 开始扫描设备...');
            showInfoMessage('正在扫描附近的医疗设备...');

            setTimeout(() => {
                const foundDevices = Math.floor(Math.random() * 3) + 1;
                showSuccessMessage(`扫描完成！发现 ${foundDevices} 个可连接设备`);
                console.log(`✅ 扫描完成，发现 ${foundDevices} 个设备`);
            }, 3000);
        }

        // 刷新设备状态
        function refreshDevices() {
            console.log('🔄 刷新设备状态...');
            showInfoMessage('正在刷新生理监测设备连接状态...');

            setTimeout(() => {
                // 随机更新一些设备状态
                Object.keys(deviceStates).forEach(roomNumber => {
                    if (Math.random() > 0.8) {
                        deviceStates[roomNumber].connected = !deviceStates[roomNumber].connected;
                        console.log(`🔄 房间 ${roomNumber} 设备状态已更新: ${deviceStates[roomNumber].connected ? '已连接' : '未连接'}`);
                    }
                });

                renderSubjectsGrid();
                showSuccessMessage('设备状态已刷新');
                console.log('✅ 设备状态刷新完成');
            }, 1500);
        }

        // 设备设置
        function deviceSettings() {
            console.log('⚙️ 打开设备设置');
            alert('设备连接设置：\n\n• 自动重连: 开启\n• 连接超时: 30秒\n• 数据同步频率: 每3秒\n• 异常断线提醒: 开启\n• 设备发现模式: 蓝牙+WiFi\n\n点击确定保存设置');
        }

        // 消息提示函数
        function showSuccessMessage(message) {
            const statusInfo = document.getElementById('statusInfo');
            statusInfo.className = 'status-info';
            statusInfo.style.background = 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)';
            statusInfo.style.color = '#155724';
            statusInfo.textContent = '✅ ' + message;

            setTimeout(() => {
                updateStatusInfo();
            }, 3000);
        }

        function showErrorMessage(message) {
            const statusInfo = document.getElementById('statusInfo');
            statusInfo.className = 'status-info error-message';
            statusInfo.textContent = '❌ ' + message;

            setTimeout(() => {
                updateStatusInfo();
            }, 3000);
        }

        function showInfoMessage(message) {
            const statusInfo = document.getElementById('statusInfo');
            statusInfo.className = 'status-info';
            statusInfo.style.background = 'linear-gradient(135deg, #cce7ff 0%, #b3d9ff 100%)';
            statusInfo.style.color = '#004085';
            statusInfo.textContent = 'ℹ️ ' + message;
        }

        // 测试函数：检查界面状态
        function debugInterfaceState() {
            const managementView = document.getElementById('managementView');
            const detailView = document.getElementById('detailView');
            const waveformView = document.getElementById('waveformView');

            console.log('🔍 当前界面状态调试:', {
                managementView: {
                    exists: !!managementView,
                    classes: managementView?.className,
                    display: managementView?.style.display,
                    visible: managementView && !managementView.classList.contains('hidden')
                },
                detailView: {
                    exists: !!detailView,
                    classes: detailView?.className,
                    display: detailView?.style.display,
                    active: detailView && detailView.classList.contains('active')
                },
                waveformView: {
                    exists: !!waveformView,
                    classes: waveformView?.className,
                    display: waveformView?.style.display,
                    active: waveformView && waveformView.classList.contains('active')
                },
                currentSubjectId: currentSubjectId,
                previousView: previousView
            });
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
            if (waveformChart) {
                waveformChart.destroy();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌟 页面加载完成');

            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('❌ Chart.js未加载');
                showError('系统加载失败 - Chart.js库未加载，请检查网络连接');
                return;
            }

            console.log('✅ Chart.js已加载，版本:', Chart.version);

            // 绑定返回按钮事件监听器（备用方案）
            const backBtn = document.querySelector('.detail-view .back-btn');
            if (backBtn) {
                backBtn.addEventListener('click', function(e) {
                    console.log('🔙 返回按钮被点击（事件监听器）');
                    e.preventDefault();
                    backToManagement();
                });
                console.log('✅ 返回按钮事件监听器已绑定');
            } else {
                console.warn('⚠️ 未找到返回按钮元素');
            }

            try {
                initializeSystem();
                console.log('🎉 系统启动成功');

                // 显示系统配置状态
                console.log('⚙️ 系统配置状态:');
                console.log(`📊 正常模式: ${!systemConfig.enableAbnormalData ? '✅ 启用' : '❌ 禁用'}`);
                console.log(`🎭 演示模式: ${systemConfig.demoMode ? '✅ 启用' : '❌ 禁用'}`);
                console.log(`🍯 异常数据类型: 仅限血糖轻微异常`);
                console.log(`⚠️ 异常数据概率: ${(systemConfig.abnormalDataProbability * 100).toFixed(1)}%`);
                console.log('💡 提示: 使用 enableNormalMode() 或 enableDemoMode() 切换模式');
                console.log('🧪 测试命令: testGlucoseAbnormal("high") 或 testGlucoseAbnormal("low")');

            } catch (error) {
                console.error('💥 系统启动失败:', error);
                showError('系统启动失败: ' + error.message);
            }
        });

        // ==================== 异常报警系统 ====================

        // 检测数据异常（已禁用）
        function checkDataAbnormalities(subject) {
            // 异常报警功能已暂时禁用
            console.log('⚠️ 异常报警功能已禁用');
            return [];
        }

        // 触发异常报警
        function triggerAlert(abnormalities, subject) {
            if (abnormalities.length === 0) return;

            // 检查冷却时间
            const now = Date.now();
            if (alertSystem.lastAlertTime && (now - alertSystem.lastAlertTime) < alertSystem.alertCooldown) {
                console.log('⏰ 报警冷却中，跳过此次报警');
                return;
            }

            console.log('🚨 触发异常报警:', abnormalities);

            // 更新报警系统状态
            alertSystem.isActive = true;
            alertSystem.currentAlerts = abnormalities;
            alertSystem.lastAlertTime = now;
            alertSystem.isAcknowledged = false;

            // 添加到历史记录
            alertSystem.alertHistory.push({
                timestamp: new Date(),
                subject: subject.id,
                abnormalities: [...abnormalities]
            });

            // 显示报警界面
            showAlertDialog(abnormalities, subject);

            // 显示异常指示器
            showAbnormalIndicator();

            // 高亮异常数据
            highlightAbnormalData(abnormalities);

            // 播放报警音效（如果需要）
            playAlertSound();
        }

        // 显示报警对话框
        function showAlertDialog(abnormalities, subject) {
            const alertSystem = document.getElementById('alertSystem');
            const alertTitle = document.getElementById('alertTitle');
            const alertMessage = document.getElementById('alertMessage');
            const alertDetails = document.getElementById('alertDetails');
            const alertIcon = document.getElementById('alertIcon');

            // 设置报警图标和标题
            const criticalCount = abnormalities.filter(a => a.severity === 'critical').length;
            if (criticalCount > 0) {
                alertIcon.textContent = '🚨';
                alertTitle.textContent = '严重异常警报';
                alertMessage.textContent = `患者 ${subject.name} (${subject.id}) 出现严重生理数据异常，需要立即处理！`;
            } else {
                alertIcon.textContent = '⚠️';
                alertTitle.textContent = '数据异常提醒';
                alertMessage.textContent = `患者 ${subject.name} (${subject.id}) 生理数据超出正常范围，请关注！`;
            }

            // 生成异常详情
            let detailsHTML = '';
            abnormalities.forEach(abnormality => {
                const severityClass = abnormality.severity === 'critical' ? 'critical' : 'warning';
                detailsHTML += `
                    <div class="alert-detail-item ${severityClass}">
                        <span class="alert-detail-label">${abnormality.name}:</span>
                        <span class="alert-detail-value">${abnormality.value}${abnormality.unit}</span>
                    </div>
                    <div class="alert-detail-item">
                        <span class="alert-detail-label">正常范围:</span>
                        <span class="alert-detail-value">${abnormality.normalRange}</span>
                    </div>
                `;
            });

            alertDetails.innerHTML = detailsHTML;

            // 显示报警对话框
            alertSystem.classList.add('active');
        }

        // 显示异常指示器
        function showAbnormalIndicator() {
            const indicator = document.getElementById('abnormalIndicator');
            indicator.classList.add('active');
        }

        // 高亮异常数据
        function highlightAbnormalData(abnormalities) {
            // 移除之前的高亮
            document.querySelectorAll('.vital-value.abnormal, .vital-card.abnormal').forEach(el => {
                el.classList.remove('abnormal');
            });

            // 添加新的高亮
            abnormalities.forEach(abnormality => {
                // 高亮卡片预览中的数值
                const vitalValues = document.querySelectorAll('.vital-value');
                vitalValues.forEach(valueEl => {
                    const text = valueEl.textContent;
                    if (text.includes(abnormality.value.toString())) {
                        valueEl.classList.add('abnormal');
                    }
                });

                // 高亮详细监测中的卡片
                const vitalCards = document.querySelectorAll('.vital-card');
                vitalCards.forEach(card => {
                    const cardText = card.textContent;
                    if (cardText.includes(abnormality.name) && cardText.includes(abnormality.value.toString())) {
                        card.classList.add('abnormal');
                    }
                });
            });
        }

        // 播放报警音效
        function playAlertSound() {
            try {
                // 创建音频上下文（如果浏览器支持）
                if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
                    const audioContext = new (AudioContext || webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);

                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.2);

                    // 播放三次短促的警报声
                    setTimeout(() => {
                        const osc2 = audioContext.createOscillator();
                        const gain2 = audioContext.createGain();
                        osc2.connect(gain2);
                        gain2.connect(audioContext.destination);
                        osc2.frequency.setValueAtTime(800, audioContext.currentTime);
                        gain2.gain.setValueAtTime(0.1, audioContext.currentTime);
                        osc2.start();
                        osc2.stop(audioContext.currentTime + 0.2);
                    }, 300);

                    setTimeout(() => {
                        const osc3 = audioContext.createOscillator();
                        const gain3 = audioContext.createGain();
                        osc3.connect(gain3);
                        gain3.connect(audioContext.destination);
                        osc3.frequency.setValueAtTime(800, audioContext.currentTime);
                        gain3.gain.setValueAtTime(0.1, audioContext.currentTime);
                        osc3.start();
                        osc3.stop(audioContext.currentTime + 0.2);
                    }, 600);
                }
            } catch (error) {
                console.log('🔇 无法播放报警音效:', error);
            }
        }

        // 确认报警
        function acknowledgeAlert() {
            console.log('✅ 用户确认报警');
            alertSystem.isAcknowledged = true;
            hideAlertDialog();

            // 如果当前在管理界面，自动跳转到异常患者的详细监测
            if (alertSystem.currentAlerts.length > 0) {
                const subject = subjects['P001']; // 假设异常来自P001
                if (subject) {
                    enterDetailMonitoring(subject.id);
                }
            }
        }

        // 忽略报警
        function dismissAlert() {
            console.log('⏭️ 用户忽略报警');
            hideAlertDialog();
        }

        // 隐藏报警对话框
        function hideAlertDialog() {
            const alertSystemEl = document.getElementById('alertSystem');
            alertSystemEl.classList.remove('active');

            // 延迟重置状态
            setTimeout(() => {
                alertSystem.isActive = false;
                alertSystem.currentAlerts = [];
            }, 300);
        }

        // 隐藏异常指示器
        function hideAbnormalIndicator() {
            const indicator = document.getElementById('abnormalIndicator');
            indicator.classList.remove('active');
        }

        // 清除异常高亮
        function clearAbnormalHighlights() {
            document.querySelectorAll('.vital-value.abnormal, .vital-card.abnormal').forEach(el => {
                el.classList.remove('abnormal');
            });
        }

        // ==================== 编辑功能 ====================

        // 绑定可编辑字段事件
        function bindEditableFieldEvents() {
            console.log('🔗 绑定可编辑字段事件');

            const editableFields = document.querySelectorAll('.editable-field');
            console.log('🔍 找到可编辑字段数量:', editableFields.length);

            editableFields.forEach(field => {
                // 移除旧的事件监听器（如果有）
                field.removeEventListener('contextmenu', handleContextMenu);

                // 添加新的事件监听器
                field.addEventListener('contextmenu', handleContextMenu);
            });

            console.log('✅ 可编辑字段事件绑定完成');
        }

        // 处理右键菜单事件
        function handleContextMenu(event) {
            showContextMenu(event, this);
        }

        // 显示右键菜单
        function showContextMenu(event, element) {
            event.preventDefault();
            event.stopPropagation();

            console.log('🖱️ 右键点击编辑字段:', element.dataset.field);
            console.log('🔍 元素信息:', {
                field: element.dataset.field,
                subject: element.dataset.subject,
                text: element.textContent.trim(),
                element: element
            });

            currentEditElement = element;
            currentEditField = element.dataset.field;
            currentEditSubjectId = element.dataset.subject;
            originalValue = element.textContent.trim();

            const contextMenu = document.getElementById('contextMenu');
            if (!contextMenu) {
                console.error('❌ 未找到右键菜单元素');
                return;
            }

            contextMenu.style.display = 'block';
            contextMenu.style.left = event.pageX + 'px';
            contextMenu.style.top = event.pageY + 'px';

            console.log('✅ 右键菜单已显示');

            // 点击其他地方隐藏菜单
            setTimeout(() => {
                document.addEventListener('click', hideContextMenu);
            }, 10);
        }

        // 隐藏右键菜单
        function hideContextMenu(event) {
            // 如果点击的是菜单项，不隐藏菜单
            if (event && event.target.closest('.context-menu')) {
                return;
            }

            const contextMenu = document.getElementById('contextMenu');
            if (contextMenu) {
                contextMenu.style.display = 'none';
            }
            document.removeEventListener('click', hideContextMenu);
        }

        // 开始编辑字段
        function editField() {
            console.log('🎯 editField函数被调用');
            console.log('🔍 当前编辑状态:', {
                currentEditElement: !!currentEditElement,
                currentEditField: currentEditField,
                currentEditSubjectId: currentEditSubjectId,
                originalValue: originalValue
            });

            if (!currentEditElement || !currentEditField || !currentEditSubjectId) {
                console.error('❌ 编辑信息不完整', {
                    element: currentEditElement,
                    field: currentEditField,
                    subject: currentEditSubjectId
                });
                return;
            }

            console.log('✏️ 开始编辑字段:', {
                field: currentEditField,
                subject: currentEditSubjectId,
                value: originalValue
            });

            hideContextMenu();

            // 显示编辑模式指示器
            const indicator = document.getElementById('editModeIndicator');
            indicator.style.display = 'block';

            // 创建输入框
            const input = document.createElement('input');
            input.type = getInputType(currentEditField);
            input.className = 'edit-input';
            input.value = originalValue;

            // 设置输入框属性
            if (currentEditField === 'age') {
                input.min = '1';
                input.max = '120';
            } else if (currentEditField === 'gender') {
                // 为性别创建下拉选择
                const select = document.createElement('select');
                select.className = 'edit-input';
                select.innerHTML = `
                    <option value="男" ${originalValue === '男' ? 'selected' : ''}>男</option>
                    <option value="女" ${originalValue === '女' ? 'selected' : ''}>女</option>
                `;
                input = select;
            }

            // 替换原始元素
            currentEditElement.style.display = 'none';
            currentEditElement.parentNode.insertBefore(input, currentEditElement.nextSibling);

            // 聚焦并选中文本
            input.focus();
            if (input.select) input.select();

            // 绑定事件
            input.addEventListener('blur', saveEdit);
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    saveEdit();
                } else if (e.key === 'Escape') {
                    cancelEdit();
                }
            });
        }

        // 获取输入框类型
        function getInputType(field) {
            switch (field) {
                case 'age':
                    return 'number';
                case 'name':
                case 'room':
                default:
                    return 'text';
            }
        }

        // 保存编辑
        function saveEdit() {
            const input = document.querySelector('.edit-input');
            if (!input || !currentEditElement) return;

            const newValue = input.value.trim();

            console.log('💾 保存编辑:', {
                field: currentEditField,
                oldValue: originalValue,
                newValue: newValue
            });

            // 验证输入
            if (!validateInput(currentEditField, newValue)) {
                alert('输入值无效，请重新输入');
                input.focus();
                return;
            }

            // 更新数据
            if (updateSubjectData(currentEditSubjectId, currentEditField, newValue)) {
                // 更新显示
                currentEditElement.textContent = newValue;
                currentEditElement.style.display = '';

                // 移除输入框
                input.remove();

                // 隐藏编辑指示器
                document.getElementById('editModeIndicator').style.display = 'none';

                // 重新渲染相关界面
                refreshDisplays();

                console.log('✅ 编辑保存成功');
            } else {
                alert('保存失败，请重试');
                input.focus();
            }

            // 清理编辑状态
            clearEditState();
        }

        // 取消编辑
        function cancelEdit() {
            const input = document.querySelector('.edit-input');
            if (input && currentEditElement) {
                currentEditElement.style.display = '';
                input.remove();
                document.getElementById('editModeIndicator').style.display = 'none';
            }
            clearEditState();
            console.log('❌ 编辑已取消');
        }

        // 清理编辑状态
        function clearEditState() {
            currentEditElement = null;
            currentEditField = null;
            currentEditSubjectId = null;
            originalValue = null;
        }

        // 验证输入
        function validateInput(field, value) {
            if (!value) return false;

            switch (field) {
                case 'name':
                    return value.length >= 1 && value.length <= 20;
                case 'age':
                    const age = parseInt(value);
                    return age >= 1 && age <= 120;
                case 'gender':
                    return ['男', '女'].includes(value);
                case 'room':
                    return value.length >= 1 && value.length <= 10;
                default:
                    return true;
            }
        }

        // 更新对象数据
        function updateSubjectData(subjectId, field, value) {
            try {
                const subject = subjects[subjectId];
                if (!subject) {
                    console.error('❌ 未找到对象:', subjectId);
                    return false;
                }

                // 转换数据类型
                let convertedValue = value;
                if (field === 'age') {
                    convertedValue = parseInt(value);
                }

                // 更新数据
                subject[field] = convertedValue;

                console.log('📝 数据已更新:', {
                    subject: subjectId,
                    field: field,
                    value: convertedValue
                });

                return true;
            } catch (error) {
                console.error('❌ 更新数据失败:', error);
                return false;
            }
        }

        // 刷新显示
        function refreshDisplays() {
            try {
                // 重新渲染对象网格
                renderSubjectsGrid();

                // 如果当前在详细监测界面，更新详细信息
                if (currentSubjectId && document.getElementById('detailView').classList.contains('active')) {
                    const subject = subjects[currentSubjectId];
                    if (subject) {
                        document.getElementById('detailSubjectName').textContent = `${subject.name} - 详细监测`;
                        document.getElementById('detailSubjectInfo').textContent =
                            `${subject.id} | ${subject.age}岁 ${subject.gender} | 房间: ${subject.room}`;
                    }
                }

                console.log('🔄 界面已刷新');
            } catch (error) {
                console.error('❌ 刷新界面失败:', error);
            }
        }

        // 阻止右键菜单在其他地方显示
        document.addEventListener('contextmenu', function(e) {
            if (!e.target.classList.contains('editable-field')) {
                // 允许在非编辑字段上显示默认右键菜单
                return true;
            }
        });

        // 点击空白处取消编辑
        document.addEventListener('click', function(e) {
            if (currentEditElement && !e.target.classList.contains('edit-input')) {
                const input = document.querySelector('.edit-input');
                if (input) {
                    saveEdit();
                }
            }
        });

        // 绑定编辑菜单项点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const editMenuItem = document.getElementById('editMenuItem');
            if (editMenuItem) {
                editMenuItem.addEventListener('click', function(e) {
                    e.stopPropagation();
                    console.log('📝 编辑菜单项被点击');
                    editField();
                });
            }
        });

        // 调试函数 - 检查编辑功能状态
        window.checkEditFunction = function() {
            console.log('🔍 编辑功能状态检查:');
            console.log('- 可编辑字段数量:', document.querySelectorAll('.editable-field').length);
            console.log('- 右键菜单元素:', !!document.getElementById('contextMenu'));
            console.log('- 编辑菜单项:', !!document.getElementById('editMenuItem'));
            console.log('- 当前编辑状态:', {
                element: !!currentEditElement,
                field: currentEditField,
                subject: currentEditSubjectId,
                value: originalValue
            });

            // 测试右键菜单显示
            const firstField = document.querySelector('.editable-field');
            if (firstField) {
                console.log('- 第一个可编辑字段:', firstField.textContent.trim());
                console.log('- 字段数据:', {
                    field: firstField.dataset.field,
                    subject: firstField.dataset.subject
                });
            }
        };

        // 手动测试异常报警功能
        window.testAlert = function(type = 'random') {
            console.log('🧪 手动测试异常报警:', type);
            const subject = subjects['P001'];
            if (!subject || !subject.vitalSigns) {
                console.error('❌ 无法测试：患者数据不存在');
                return;
            }

            // 备份原始数据
            const originalData = { ...subject.vitalSigns };

            // 根据类型生成异常数据（仅限血糖轻微异常）
            switch (type) {
                case 'bloodGlucose':
                case 'glucose':
                    // 血糖轻微偏高
                    subject.vitalSigns.bloodGlucose = 6.5; // 轻微超过正常上限
                    console.log('🍯 测试血糖轻微异常');
                    break;
                case 'lowGlucose':
                    // 血糖轻微偏低
                    subject.vitalSigns.bloodGlucose = 3.7; // 轻微低于正常下限
                    console.log('🍯 测试血糖轻微偏低');
                    break;
                case 'heartRate':
                case 'bloodPressure':
                case 'spo2':
                case 'temperature':
                case 'multiple':
                case 'critical':
                    // 这些测试类型现在只会生成血糖轻微异常
                    console.log(`⚠️ 测试类型 "${type}" 已调整为血糖轻微异常`);
                    subject.vitalSigns.bloodGlucose = 6.3 + Math.random() * 0.4; // 6.3-6.7
                    console.log('🍯 仅生成血糖轻微异常用于测试');
                    break;
                default:
                    // 默认生成血糖轻微异常
                    generateAbnormalData(subject);
            }

            // 立即检测异常
            const abnormalities = checkDataAbnormalities(subject);
            if (abnormalities.length > 0) {
                triggerAlert(abnormalities, subject);
            }

            // 5秒后恢复正常数据
            setTimeout(() => {
                Object.assign(subject.vitalSigns, originalData);
                console.log('🔄 数据已恢复正常');

                // 重新渲染界面
                if (currentSubjectId === subject.id) {
                    renderVitalSigns(subject);
                }
                renderSubjectsGrid();
            }, 5000);
        };

        // 清除所有报警状态
        window.clearAlerts = function() {
            console.log('🧹 清除所有报警状态');
            alertSystem.isActive = false;
            alertSystem.currentAlerts = [];
            alertSystem.isAcknowledged = true;

            hideAlertDialog();
            hideAbnormalIndicator();
            clearAbnormalHighlights();
        };

        // 查看报警历史
        window.getAlertHistory = function() {
            console.log('📋 报警历史记录:');
            console.table(alertSystem.alertHistory);
            return alertSystem.alertHistory;
        };

        // 系统配置控制函数
        window.enableNormalMode = function() {
            systemConfig.enableAbnormalData = false;
            systemConfig.demoMode = false;
            console.log('✅ 已切换到正常模式 - 异常数据生成已禁用');
            console.log('📊 系统将保持稳定的生理数据，符合正常监测状态');
        };

        window.enableDemoMode = function() {
            systemConfig.enableAbnormalData = true;
            systemConfig.abnormalDataProbability = 0.05; // 5%概率用于演示
            systemConfig.demoMode = true;
            console.log('🎭 已切换到演示模式 - 异常数据生成已启用');
            console.log('🍯 系统将偶尔生成血糖轻微异常用于演示报警功能');
            console.log('✅ 其他生理参数将保持正常范围');
        };

        window.getSystemConfig = function() {
            console.log('⚙️ 当前系统配置:');
            console.table(systemConfig);
            return systemConfig;
        };

        window.setAbnormalProbability = function(probability) {
            if (probability >= 0 && probability <= 1) {
                systemConfig.abnormalDataProbability = probability;
                console.log(`📊 异常数据生成概率已设置为: ${(probability * 100).toFixed(1)}%`);
            } else {
                console.error('❌ 概率值必须在0-1之间');
            }
        };

        // 专门的血糖异常测试函数
        window.testGlucoseAbnormal = function(type = 'high') {
            console.log('🍯 测试血糖异常功能');
            const subject = subjects['P001'];
            if (!subject || !subject.vitalSigns) {
                console.error('❌ 无法测试：患者数据不存在');
                return;
            }

            const originalGlucose = subject.vitalSigns.bloodGlucose;

            if (type === 'high') {
                subject.vitalSigns.bloodGlucose = 6.5; // 轻微偏高
                console.log('📈 血糖设置为轻微偏高: 6.5 mmol/L');
            } else if (type === 'low') {
                subject.vitalSigns.bloodGlucose = 3.7; // 轻微偏低
                console.log('📉 血糖设置为轻微偏低: 3.7 mmol/L');
            }

            // 立即检测异常
            const abnormalities = checkDataAbnormalities(subject);
            if (abnormalities.length > 0) {
                triggerAlert(abnormalities, subject);
            }

            // 5秒后恢复正常
            setTimeout(() => {
                subject.vitalSigns.bloodGlucose = originalGlucose;
                console.log('🔄 血糖已恢复正常');

                if (currentSubjectId === subject.id) {
                    renderVitalSigns(subject);
                }
                renderSubjectsGrid();
            }, 5000);
        };

        console.log('✅ 编辑功能已初始化');
        console.log('🚨 异常报警系统已初始化');
        console.log('💡 测试命令:');
        console.log('  - testAlert() - 随机异常测试');
        console.log('  - testAlert("heartRate") - 心率异常测试');
        console.log('  - testAlert("bloodPressure") - 血压异常测试');
        console.log('  - testAlert("spo2") - 血氧异常测试');
        console.log('  - testAlert("temperature") - 体温异常测试');
        console.log('  - testAlert("multiple") - 多项异常测试');
        console.log('  - testAlert("critical") - 危险异常测试');
        console.log('  - clearAlerts() - 清除所有报警');

        // 全屏功能
        let isFullscreen = false;

        function toggleFullscreen() {
            const body = document.body;
            const btn = document.getElementById('fullscreenBtn');

            if (!isFullscreen) {
                // 进入全屏模式
                body.classList.add('fullscreen');
                btn.innerHTML = '🔍 退出全屏';
                btn.title = '退出全屏模式';
                isFullscreen = true;

                // 尝试浏览器全屏API
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen().catch(err => {
                        console.log('全屏API不可用:', err);
                    });
                } else if (document.documentElement.webkitRequestFullscreen) {
                    document.documentElement.webkitRequestFullscreen();
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                }
            } else {
                // 退出全屏模式
                body.classList.remove('fullscreen');
                btn.innerHTML = '🔍 全屏模式';
                btn.title = '进入全屏模式';
                isFullscreen = false;

                // 退出浏览器全屏
                if (document.exitFullscreen) {
                    document.exitFullscreen().catch(err => {
                        console.log('退出全屏API不可用:', err);
                    });
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            }

            // 重新调整图表大小
            setTimeout(() => {
                if (window.heartRateChart) window.heartRateChart.resize();
                if (window.bloodPressureChart) window.bloodPressureChart.resize();
                if (window.spo2Chart) window.spo2Chart.resize();
                if (window.temperatureChart) window.temperatureChart.resize();
            }, 300);
        }

        // 监听浏览器全屏状态变化
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('msfullscreenchange', handleFullscreenChange);

        function handleFullscreenChange() {
            const isInFullscreen = !!(document.fullscreenElement ||
                                    document.webkitFullscreenElement ||
                                    document.msFullscreenElement);

            if (!isInFullscreen && isFullscreen) {
                // 用户通过ESC键退出了浏览器全屏，同步我们的状态
                document.body.classList.remove('fullscreen');
                document.getElementById('fullscreenBtn').innerHTML = '🔍 全屏模式';
                isFullscreen = false;
            }
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F11') {
                e.preventDefault();
                toggleFullscreen();
            }
        });

        console.log('🔍 全屏功能已初始化 - 按F11或点击按钮切换全屏');
        console.log('  - getAlertHistory() - 查看报警历史');
    </script>
</body>
</html>
