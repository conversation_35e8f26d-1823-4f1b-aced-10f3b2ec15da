<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .editable-field {
            position: relative;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.3s ease;
            display: inline-block;
            background: #f8f9fa;
            margin: 2px;
        }

        .editable-field:hover {
            background: rgba(102, 126, 234, 0.1);
            box-shadow: 0 0 0 1px rgba(102, 126, 234, 0.3);
        }

        .context-menu {
            position: fixed;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            padding: 8px 0;
            z-index: 1000;
            min-width: 120px;
            display: none;
        }

        .context-menu-item {
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            transition: background 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .context-menu-item:hover {
            background: #f5f5f5;
        }

        .edit-input {
            background: white;
            border: 2px solid #667eea;
            border-radius: 6px;
            padding: 6px 10px;
            font-size: inherit;
            font-family: inherit;
            color: inherit;
            outline: none;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        }

        .debug {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 编辑功能测试</h1>
        
        <div class="patient-info">
            <h3>患者信息</h3>
            <p><strong>姓名:</strong> <span class="editable-field" data-field="name" data-subject="P001">张三</span></p>
            <p><strong>年龄:</strong> <span class="editable-field" data-field="age" data-subject="P001">25</span>岁</p>
            <p><strong>性别:</strong> <span class="editable-field" data-field="gender" data-subject="P001">男</span></p>
            <p><strong>房间:</strong> <span class="editable-field" data-field="room" data-subject="P001">4F012</span></p>
        </div>
        
        <div class="debug" id="debugLog">等待操作...</div>
    </div>

    <!-- 右键菜单 -->
    <div class="context-menu" id="contextMenu">
        <div class="context-menu-item" id="editMenuItem">
            <span class="icon">✏️</span>
            <span>编辑</span>
        </div>
    </div>

    <script>
        // 全局变量
        let currentEditElement = null;
        let currentEditField = null;
        let currentEditSubjectId = null;
        let originalValue = null;
        
        // 测试数据
        let subjects = {
            'P001': {
                name: '张三',
                age: 25,
                gender: '男',
                room: '4F012'
            }
        };
        
        function log(message) {
            console.log(message);
            const debugEl = document.getElementById('debugLog');
            if (debugEl) {
                debugEl.innerHTML += '<br>' + new Date().toLocaleTimeString() + ': ' + message;
                debugEl.scrollTop = debugEl.scrollHeight;
            }
        }
        
        // 显示右键菜单
        function showContextMenu(event, element) {
            event.preventDefault();
            event.stopPropagation();
            
            log('🖱️ 右键点击: ' + element.dataset.field + ' = ' + element.textContent.trim());
            
            currentEditElement = element;
            currentEditField = element.dataset.field;
            currentEditSubjectId = element.dataset.subject;
            originalValue = element.textContent.trim();
            
            const contextMenu = document.getElementById('contextMenu');
            if (!contextMenu) {
                log('❌ 未找到右键菜单');
                return;
            }
            
            contextMenu.style.display = 'block';
            contextMenu.style.left = event.pageX + 'px';
            contextMenu.style.top = event.pageY + 'px';
            
            log('✅ 右键菜单已显示');
        }
        
        // 隐藏右键菜单
        function hideContextMenu(event) {
            if (event && event.target.closest('.context-menu')) {
                return;
            }
            
            const contextMenu = document.getElementById('contextMenu');
            if (contextMenu) {
                contextMenu.style.display = 'none';
            }
            document.removeEventListener('click', hideContextMenu);
            log('🔒 右键菜单已隐藏');
        }
        
        // 开始编辑
        function editField() {
            log('🎯 开始编辑字段: ' + currentEditField);
            
            if (!currentEditElement || !currentEditField) {
                log('❌ 编辑信息不完整');
                return;
            }
            
            hideContextMenu();
            
            // 创建输入框
            let input;
            if (currentEditField === 'gender') {
                input = document.createElement('select');
                input.innerHTML = `
                    <option value="男" ${originalValue === '男' ? 'selected' : ''}>男</option>
                    <option value="女" ${originalValue === '女' ? 'selected' : ''}>女</option>
                `;
            } else {
                input = document.createElement('input');
                input.type = currentEditField === 'age' ? 'number' : 'text';
                input.value = originalValue;
                if (currentEditField === 'age') {
                    input.min = '1';
                    input.max = '120';
                }
            }
            
            input.className = 'edit-input';
            
            // 替换原始元素
            currentEditElement.style.display = 'none';
            currentEditElement.parentNode.insertBefore(input, currentEditElement.nextSibling);
            
            // 聚焦
            input.focus();
            if (input.select) input.select();
            
            log('✅ 输入框已创建');
            
            // 绑定事件
            input.addEventListener('blur', saveEdit);
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    saveEdit();
                } else if (e.key === 'Escape') {
                    cancelEdit();
                }
            });
        }
        
        // 保存编辑
        function saveEdit() {
            const input = document.querySelector('.edit-input');
            if (!input || !currentEditElement) return;
            
            const newValue = input.value.trim();
            log('💾 保存编辑: ' + currentEditField + ' = ' + newValue);
            
            if (!newValue) {
                log('❌ 输入值为空');
                input.focus();
                return;
            }
            
            // 更新数据
            subjects[currentEditSubjectId][currentEditField] = currentEditField === 'age' ? parseInt(newValue) : newValue;
            
            // 更新显示
            currentEditElement.textContent = newValue;
            currentEditElement.style.display = '';
            
            // 移除输入框
            input.remove();
            
            log('✅ 编辑保存成功');
            clearEditState();
        }
        
        // 取消编辑
        function cancelEdit() {
            const input = document.querySelector('.edit-input');
            if (input && currentEditElement) {
                currentEditElement.style.display = '';
                input.remove();
            }
            log('❌ 编辑已取消');
            clearEditState();
        }
        
        // 清理编辑状态
        function clearEditState() {
            currentEditElement = null;
            currentEditField = null;
            currentEditSubjectId = null;
            originalValue = null;
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('🌟 页面加载完成');
            
            // 绑定右键事件
            document.querySelectorAll('.editable-field').forEach(field => {
                field.addEventListener('contextmenu', function(e) {
                    showContextMenu(e, this);
                });
            });
            
            // 绑定编辑菜单点击事件
            const editMenuItem = document.getElementById('editMenuItem');
            if (editMenuItem) {
                editMenuItem.addEventListener('click', function(e) {
                    e.stopPropagation();
                    log('📝 编辑菜单被点击');
                    editField();
                });
            }
            
            // 点击其他地方隐藏菜单
            document.addEventListener('click', hideContextMenu);
            
            log('✅ 事件绑定完成');
        });
    </script>
</body>
</html>
