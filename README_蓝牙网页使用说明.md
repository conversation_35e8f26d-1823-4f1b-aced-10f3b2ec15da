# 蓝牙健康监测网页使用说明

## 📋 概述

本项目提供了六个网页版本来处理CH32V307VCT6蓝牙健康监测系统的数据：

1. **bluetooth_health_monitor.html** - 使用Web Bluetooth API（适用于支持BLE的设备）
2. **serial_bluetooth_monitor.html** - 使用Web Serial API（适用于串口蓝牙模块如HC-05/HC-06）
3. **health_data_simulator.html** - 基础生理数据模拟器（无需蓝牙连接，直接生成数据并可视化）
4. **enhanced_medical_monitor.html** - 增强型医学生理监测系统（多参数监测，医学级数据模拟）
5. **interactive_medical_monitor.html** - 交互式医学生理监测系统（点击生理数据查看详细波形）
6. **clickable_vitals_demo.html** - 可点击生理数据演示（简化版交互式界面演示）

## 🔧 硬件连接

### HC-05/HC-06蓝牙模块连接
```
HC-05/HC-06        CH32V307VCT6
VCC     ←→         3.3V
GND     ←→         GND
TXD     ←→         PA10 (UART1_RX)
RXD     ←→         PA9  (UART1_TX)
```

### AS608指纹模块连接
```
AS608             CH32V307VCT6
VCC     ←→        3.3V
GND     ←→        GND
TX      ←→        PA3 (UART2_RX)
RX      ←→        PA2 (UART2_TX)
```

## 💻 浏览器要求

### Web Bluetooth API版本
- **Chrome 56+**
- **Edge 79+**
- **Opera 43+**
- ❌ Firefox（不支持）
- ❌ Safari（不支持）

### Web Serial API版本
- **Chrome 89+**
- **Edge 89+**
- ❌ Firefox（不支持）
- ❌ Safari（不支持）

## 🚀 使用步骤

### 方法一：Web Bluetooth API（推荐用于BLE设备）

1. **打开网页**
   ```
   双击 bluetooth_health_monitor.html
   ```

2. **连接设备**
   - 点击"连接蓝牙设备"按钮
   - 在弹出的设备列表中选择你的蓝牙设备
   - 等待连接成功

3. **开始监测**
   - 连接成功后，将手指放在AS608指纹模块上
   - 网页将实时显示健康数据

### 方法二：Web Serial API（推荐用于HC-05/HC-06）

1. **硬件准备**
   - 将HC-05/HC-06连接到电脑的USB转串口适配器
   - 确保驱动程序已正确安装

2. **打开网页**
   ```
   双击 serial_bluetooth_monitor.html
   ```

3. **连接串口**
   - 点击"连接串口"按钮
   - 选择对应的COM端口
   - 等待连接成功

4. **开始监测**
   - 连接成功后，系统将自动接收数据
   - 将手指放在AS608指纹模块上开始监测

### 方法三：生理数据模拟器（无需蓝牙连接）

1. **打开网页**
   ```
   双击 health_data_simulator.html
   ```

2. **开始模拟**
   - 点击"开始模拟"按钮
   - 系统将自动生成与CH32V307VCT6相同的生理数据
   - 实时显示心率、血氧、体温数据和图表

3. **功能特点**
   - **无需硬件连接** - 完全基于软件模拟
   - **相同数据逻辑** - 使用与CH32V307VCT6完全相同的数据生成算法
   - **实时图表** - 提供心率、血氧、体温的实时可视化图表
   - **数据导出** - 生成与蓝牙传输相同格式的数据日志

### 方法四：增强型医学生理监测系统（医学级多参数监测）

1. **打开网页**
   ```
   双击 enhanced_medical_monitor.html
   ```

2. **开始监测**
   - 点击"开始监测"按钮
   - 系统将实时生成8种医学生理参数
   - 提供专业的异常检测和报警功能

3. **功能特点**
   - **多参数监测** - 心率、血压、血氧、体温、呼吸频率、血糖、QT间期、压力指数
   - **医学级精度** - 基于真实生理学原理的数据模拟
   - **昼夜节律** - 模拟人体24小时生理周期变化
   - **异常检测** - 自动识别异常值并提供视觉报警
   - **统计分析** - 提供平均值、趋势分析等统计功能
   - **数据导出** - 支持完整的医学数据CSV导出

### 方法五：交互式医学生理监测系统（点击查看详细波形）

1. **打开网页**
   ```
   双击 interactive_medical_monitor.html
   ```

2. **开始监测**
   - 点击"开始监测"按钮开始数据生成
   - 点击任意生理数据卡片进入详细波形界面
   - 在详细界面可查看实时波形图和统计分析
   - 点击"返回主界面"按钮回到总览界面

3. **功能特点**
   - **点击交互** - 点击生理数据卡片查看详细波形图
   - **双界面设计** - 主界面总览 + 详细界面深入分析
   - **实时波形** - 高质量的实时波形图显示
   - **统计分析** - 平均值、最大值、最小值、趋势分析
   - **状态监测** - 自动判断正常/异常状态
   - **无缝切换** - 主界面和详细界面无缝切换

### 方法六：可点击生理数据演示（简化版交互演示）

1. **打开网页**
   ```
   双击 clickable_vitals_demo.html
   ```

2. **演示操作**
   - 点击"开始演示"按钮
   - 点击任意生理数据卡片查看详细波形
   - 观察实时数据变化和波形图更新
   - 体验流畅的界面交互效果

3. **功能特点**
   - **简化界面** - 专注于交互功能演示
   - **流畅动画** - 优美的悬停和点击效果
   - **实时更新** - 详细界面实时数据更新
   - **响应式设计** - 适配各种屏幕尺寸

## 📊 数据格式与范围

### 基础数据范围
- **心率**: 55-105次/分
- **血氧**: 80-100%（默认94%）
- **体温**: 35.0-40.0°C（默认36.5°C）

### 增强型医学数据范围
- **心率**: 45-150次/分（正常60-100）
- **血压**: 90-180/50-110 mmHg（正常90-140/60-90）
- **血氧**: 94-100%（正常95-100）
- **体温**: 35.5-38.5°C（正常36.1-37.2）
- **呼吸频率**: 8-30次/分（正常12-20）
- **血糖**: 3.5-8.0 mmol/L（正常3.9-6.1）
- **QT间期**: 320-480ms（正常350-450）
- **压力指数**: 0-100（正常0-30）

### 健康数据格式
```
健康数据:心率=75次/分,血氧=94%,体温=36.5°C
```

### 增强型医学数据格式
```
心率=72次/分,血压=120/80mmHg,血氧=98%,体温=36.5°C,呼吸=16次/分,血糖=5.0mmol/L,QT=400ms,压力指数=15
```

### 指纹检测状态
```
手指检测到 - 开始生理数据监测
手指移开 - 停止生理数据监测
```

### 报警信息格式
```
病人编号001，身体情况出现异常反应，请立即前往4F012查看
```

## 🎨 网页功能特点

### 实时数据显示
- **心率监测** - 实时显示心率数值（次/分）
- **血氧监测** - 实时显示血氧饱和度（%）
- **体温监测** - 实时显示体温数值（°C）

### 指纹检测状态
- **等待状态** - 显示"等待手指接触..."
- **检测状态** - 显示"手指已检测到，正在监测..."

### 报警功能
- **视觉报警** - 页面背景变红闪烁
- **浏览器通知** - 弹出系统通知
- **声音提示** - 浏览器通知声音

### 数据日志
- **实时日志** - 显示所有接收到的数据
- **时间戳** - 每条记录都有时间标记
- **分类显示** - 不同类型的数据用不同颜色显示

## 🔧 故障排除

### 连接问题

**问题：无法找到蓝牙设备**
- 确保蓝牙设备已开启并处于可发现状态
- 检查设备名称是否正确（HC-05, HC-06等）
- 尝试重启蓝牙设备

**问题：连接后无数据**
- 检查硬件连接是否正确
- 确认波特率设置（默认9600）
- 检查串口是否被其他程序占用

### 浏览器问题

**问题：提示不支持Web Bluetooth/Serial API**
- 使用Chrome 89+或Edge 89+浏览器
- 确保浏览器已更新到最新版本
- 检查浏览器实验性功能是否启用

**问题：无法访问串口**
- 确保网页通过HTTPS访问（本地文件除外）
- 检查浏览器权限设置
- 尝试重新授权串口访问

### 数据解析问题

**问题：数据显示异常**
- 检查数据格式是否正确
- 确认字符编码设置
- 查看浏览器控制台错误信息

## 📱 移动设备支持

### Android设备
- **Chrome Mobile** - 支持Web Bluetooth API
- **Edge Mobile** - 支持Web Bluetooth API
- ❌ **Web Serial API** - 移动设备不支持

### iOS设备
- ❌ **Safari** - 不支持Web Bluetooth API
- ❌ **Chrome iOS** - 不支持Web Bluetooth API

## 🔒 安全注意事项

### 数据隐私
- 所有数据仅在本地处理，不会上传到服务器
- 建议在安全的网络环境中使用
- 定期清理浏览器缓存和数据

### 设备安全
- 确保蓝牙设备使用安全的配对密码
- 避免在公共场所使用敏感的健康监测功能
- 定期更新设备固件

## 📞 技术支持

### 常见问题
1. **数据更新延迟** - 正常现象，数据每秒更新一次
2. **连接断开** - 检查设备电源和信号强度
3. **报警误触发** - 检查传感器连接和阈值设置

### 调试信息
- 打开浏览器开发者工具（F12）
- 查看Console标签页的错误信息
- 检查Network标签页的连接状态

### 联系方式
- 技术问题请查看浏览器控制台日志
- 硬件问题请检查连接和电源
- 软件问题请更新浏览器版本

## 🎯 演示模式

### 自动演示
系统内置5秒循环演示模式：
- 0-5秒：等待手指接触
- 5-10秒：模拟手指检测，显示生理数据
- 10-15秒：模拟手指移开，停止显示
- 循环重复...

### 手动测试
可以通过串口终端发送测试命令：
```
健康数据:心率=75次/分,血氧=94%,体温=36.5°C
手指检测到 - 开始生理数据监测
病人编号001，身体情况出现异常反应，请立即前往4F012查看
```

## 📈 扩展功能

### 数据导出
- 可以扩展添加数据导出功能
- 支持CSV、JSON等格式
- 可以添加历史数据查看

### 多设备支持
- 可以同时连接多个蓝牙设备
- 支持设备管理和切换
- 可以添加设备配置保存

### 云端同步
- 可以扩展添加云端数据同步
- 支持多设备数据共享
- 可以添加远程监控功能

## 🎯 生理数据模拟器特点

### 数据生成算法
- **完全相同的逻辑** - 使用与CH32V307VCT6相同的心率模拟算法
- **真实的数据范围** - 心率55-105次/分，血氧80-100%，体温35.0-40.0°C
- **自然变化模式** - 模拟睡眠、休息、正常、活跃、运动等不同状态
- **影响因子模拟** - 包含压力水平、活动强度、疲劳水平等因子

### 可视化功能
- **实时图表** - 使用Chart.js提供专业的数据可视化
- **多参数显示** - 同时显示心率、血氧、体温三个参数
- **动态更新** - 每秒更新一次数据，与实际设备同步
- **历史数据** - 保存最近50个数据点的历史记录

### 用户界面
- **现代化设计** - 响应式布局，支持各种屏幕尺寸
- **直观操作** - 简单的开始/停止/重置按钮
- **状态指示** - 清晰的运行状态指示器
- **数据日志** - 完整的数据记录，格式与蓝牙传输一致

### 应用场景
- **开发测试** - 无需硬件即可测试网页功能
- **演示展示** - 用于产品演示和功能展示
- **数据分析** - 研究生理数据的变化规律
- **教学培训** - 用于医疗设备相关的教学和培训
