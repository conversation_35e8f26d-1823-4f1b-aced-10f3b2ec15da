<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试版医学监测系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 15px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 25px;
        }

        .header h1 {
            color: #333;
            font-size: 2.3em;
            margin-bottom: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .status-info {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.1em;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        .debug-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }

        .debug-info h3 {
            color: #007bff;
            margin-bottom: 10px;
        }

        .subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .subject-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .subject-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .subject-card.active {
            border-color: #4caf50;
            background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
        }

        .subject-card.inactive {
            opacity: 0.6;
            cursor: default;
        }

        .subject-card.inactive:hover {
            transform: none;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: transparent;
        }

        .subject-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .subject-id {
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
        }

        .subject-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: bold;
        }

        .status-normal {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-inactive {
            background: #f5f5f5;
            color: #666;
        }

        .subject-info {
            margin-bottom: 15px;
        }

        .subject-info p {
            margin: 5px 0;
            color: #666;
        }

        .vital-preview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9em;
        }

        .vital-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }

        .vital-label {
            color: #666;
        }

        .vital-value {
            font-weight: bold;
            color: #333;
        }

        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .hidden {
            display: none !important;
        }

        .detail-view {
            display: none;
        }

        .detail-view.active {
            display: block;
        }

        .vitals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .vital-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .vital-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        }

        .vital-card .icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .vital-card .value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .vital-card .label {
            color: #666;
            font-size: 1em;
        }

        /* 生理参数颜色主题 */
        .heart-rate .icon { color: #e74c3c; }
        .heart-rate .value { color: #e74c3c; }
        .blood-pressure .icon { color: #8e44ad; }
        .blood-pressure .value { color: #8e44ad; }
        .spo2 .icon { color: #3498db; }
        .spo2 .value { color: #3498db; }
        .temperature .icon { color: #f39c12; }
        .temperature .value { color: #f39c12; }
        .respiratory-rate .icon { color: #27ae60; }
        .respiratory-rate .value { color: #27ae60; }
        .blood-glucose .icon { color: #e67e22; }
        .blood-glucose .value { color: #e67e22; }
        .ecg-rhythm .icon { color: #34495e; }
        .ecg-rhythm .value { color: #34495e; }
        .cvp .icon { color: #9b59b6; }
        .cvp .value { color: #9b59b6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 集成式生理监测管理系统</h1>
            <p>多对象集成监测，统一管理，精准分析</p>
        </div>

        <div class="debug-info">
            <h3>🔧 系统调试信息</h3>
            <div id="debugContent">正在初始化系统...</div>
        </div>

        <div class="status-info" id="statusInfo">
            系统运行正常 - 当前监测12个对象，1个对象数据有效
        </div>

        <!-- 管理界面 -->
        <div class="management-view" id="managementView">
            <div class="subjects-grid" id="subjectsGrid">
                <!-- 检测对象卡片将通过JavaScript动态生成 -->
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <button onclick="testSystem()">🧪 测试系统</button>
                <button onclick="forceRender()">🔄 强制重新渲染</button>
                <button onclick="showDebugInfo()">📊 显示调试信息</button>
            </div>
        </div>

        <!-- 详细监测界面 -->
        <div class="detail-view" id="detailView">
            <div style="margin-bottom: 20px;">
                <button onclick="backToManagement()">← 返回管理界面</button>
                <span id="currentSubjectInfo" style="margin-left: 20px; font-weight: bold;"></span>
            </div>
            
            <div class="vitals-grid" id="vitalsGrid">
                <!-- 生理参数卡片将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSubjectId = null;
        
        // 模拟检测对象数据
        let subjects = {
            'P001': {
                id: 'P001',
                name: '张某某',
                age: 45,
                gender: '男',
                room: '4F012',
                status: 'normal',
                isActive: true,
                vitalSigns: {
                    heartRate: 72,
                    systolicBP: 120,
                    diastolicBP: 80,
                    spo2: 98,
                    temperature: 36.5,
                    respiratoryRate: 16,
                    bloodGlucose: 5.2,
                    qtInterval: 400,
                    cvp: 8
                }
            },
            'P002': {
                id: 'P002',
                name: '李某某',
                age: 32,
                gender: '女',
                room: '4F015',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            },
            'P003': {
                id: 'P003',
                name: '王某某',
                age: 58,
                gender: '男',
                room: '4F018',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            }
        };
        
        // 为了测试，创建更多对象
        for (let i = 4; i <= 12; i++) {
            const id = `P${i.toString().padStart(3, '0')}`;
            subjects[id] = {
                id: id,
                name: `患者${i}`,
                age: 30 + Math.floor(Math.random() * 40),
                gender: Math.random() > 0.5 ? '男' : '女',
                room: `4F${(i + 10).toString().padStart(3, '0')}`,
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            };
        }
        
        // 更新调试信息
        function updateDebugInfo(message) {
            const debugContent = document.getElementById('debugContent');
            const timestamp = new Date().toLocaleTimeString();
            debugContent.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            console.log(`[${timestamp}] ${message}`);
        }
        
        // 初始化系统
        function initializeSystem() {
            updateDebugInfo('🚀 开始初始化系统');
            
            try {
                renderSubjectsGrid();
                updateDebugInfo('✅ 对象网格渲染完成');
                
                updateStatusInfo();
                updateDebugInfo('✅ 状态信息更新完成');
                
                updateDebugInfo('✅ 系统初始化完成');
            } catch (error) {
                updateDebugInfo('❌ 系统初始化失败: ' + error.message);
                console.error('系统初始化失败:', error);
            }
        }
        
        // 渲染检测对象网格
        function renderSubjectsGrid() {
            updateDebugInfo('📊 开始渲染对象网格');
            
            const subjectsGrid = document.getElementById('subjectsGrid');
            if (!subjectsGrid) {
                throw new Error('未找到subjectsGrid元素');
            }
            
            subjectsGrid.innerHTML = '';
            
            const subjectCount = Object.keys(subjects).length;
            updateDebugInfo(`📋 找到 ${subjectCount} 个对象`);
            
            Object.values(subjects).forEach((subject, index) => {
                try {
                    const subjectCard = createSubjectCard(subject);
                    subjectsGrid.appendChild(subjectCard);
                    updateDebugInfo(`✅ 创建对象卡片: ${subject.id}`);
                } catch (error) {
                    updateDebugInfo(`❌ 创建对象卡片失败 ${subject.id}: ${error.message}`);
                }
            });
            
            updateDebugInfo(`✅ 对象网格渲染完成，共 ${subjectCount} 个卡片`);
        }
        
        // 创建检测对象卡片
        function createSubjectCard(subject) {
            const card = document.createElement('div');
            card.className = `subject-card ${subject.isActive ? 'active' : 'inactive'}`;
            
            if (subject.isActive) {
                card.onclick = () => enterDetailMonitoring(subject.id);
            }
            
            const statusClass = subject.isActive ? 'status-normal' : 'status-inactive';
            const statusText = subject.isActive ? '正常' : '未连接';
            
            card.innerHTML = `
                <div class="subject-header">
                    <div class="subject-id">${subject.id}</div>
                    <div class="subject-status ${statusClass}">${statusText}</div>
                </div>
                <div class="subject-info">
                    <p><strong>姓名:</strong> ${subject.name}</p>
                    <p><strong>年龄:</strong> ${subject.age}岁</p>
                    <p><strong>性别:</strong> ${subject.gender}</p>
                    <p><strong>房间:</strong> ${subject.room}</p>
                </div>
                ${subject.isActive && subject.vitalSigns ? `
                <div class="vital-preview">
                    <div class="vital-item">
                        <span class="vital-label">心率:</span>
                        <span class="vital-value">${subject.vitalSigns.heartRate} 次/分</span>
                    </div>
                    <div class="vital-item">
                        <span class="vital-label">血压:</span>
                        <span class="vital-value">${subject.vitalSigns.systolicBP}/${subject.vitalSigns.diastolicBP}</span>
                    </div>
                    <div class="vital-item">
                        <span class="vital-label">血氧:</span>
                        <span class="vital-value">${subject.vitalSigns.spo2}%</span>
                    </div>
                    <div class="vital-item">
                        <span class="vital-label">体温:</span>
                        <span class="vital-value">${subject.vitalSigns.temperature}°C</span>
                    </div>
                </div>
                ` : '<div style="text-align: center; color: #999; padding: 20px;">暂无数据</div>'}
            `;
            
            return card;
        }
        
        // 更新状态信息
        function updateStatusInfo() {
            const totalSubjects = Object.keys(subjects).length;
            const activeCount = Object.values(subjects).filter(s => s.isActive).length;
            
            const statusInfo = document.getElementById('statusInfo');
            statusInfo.textContent = `系统运行正常 - 当前监测${totalSubjects}个对象，${activeCount}个对象数据有效`;
            
            updateDebugInfo(`📊 状态更新: 总计${totalSubjects}个对象，${activeCount}个有效`);
        }
        
        // 进入详细监测
        function enterDetailMonitoring(subjectId) {
            updateDebugInfo(`🔍 进入详细监测: ${subjectId}`);
            
            currentSubjectId = subjectId;
            const subject = subjects[subjectId];
            
            if (!subject || !subject.isActive) {
                updateDebugInfo(`❌ 对象无效或未激活: ${subjectId}`);
                alert('该对象未连接或数据无效');
                return;
            }
            
            // 切换界面
            document.getElementById('managementView').classList.add('hidden');
            document.getElementById('detailView').classList.add('active');
            
            // 更新当前对象信息
            document.getElementById('currentSubjectInfo').textContent = 
                `当前监测: ${subject.id} - ${subject.name} (${subject.room})`;
            
            // 渲染生理参数
            renderVitalSigns(subject);
            
            updateDebugInfo(`✅ 详细监测界面已显示`);
        }
        
        // 渲染生理参数
        function renderVitalSigns(subject) {
            const vitalsGrid = document.getElementById('vitalsGrid');
            vitalsGrid.innerHTML = '';
            
            const vitalCards = [
                {
                    id: 'heartRate',
                    class: 'heart-rate',
                    icon: '💓',
                    label: '心率 (次/分)',
                    value: Math.round(subject.vitalSigns.heartRate),
                    range: '参考范围: 60-100'
                },
                {
                    id: 'bloodPressure',
                    class: 'blood-pressure',
                    icon: '🩸',
                    label: '血压 (mmHg)',
                    value: `${Math.round(subject.vitalSigns.systolicBP)}/${Math.round(subject.vitalSigns.diastolicBP)}`,
                    range: '参考范围: 90-140/60-90'
                },
                {
                    id: 'spo2',
                    class: 'spo2',
                    icon: '🫁',
                    label: '血氧饱和度 (%)',
                    value: Math.round(subject.vitalSigns.spo2),
                    range: '参考范围: 95-100'
                },
                {
                    id: 'temperature',
                    class: 'temperature',
                    icon: '🌡️',
                    label: '体温 (°C)',
                    value: subject.vitalSigns.temperature.toFixed(1),
                    range: '参考范围: 36.1-37.2'
                },
                {
                    id: 'respiratoryRate',
                    class: 'respiratory-rate',
                    icon: '🌬️',
                    label: '呼吸频率 (次/分)',
                    value: Math.round(subject.vitalSigns.respiratoryRate),
                    range: '参考范围: 12-20'
                },
                {
                    id: 'bloodGlucose',
                    class: 'blood-glucose',
                    icon: '🍯',
                    label: '血糖 (mmol/L)',
                    value: subject.vitalSigns.bloodGlucose.toFixed(1),
                    range: '参考范围: 3.9-6.1'
                },
                {
                    id: 'ecgRhythm',
                    class: 'ecg-rhythm',
                    icon: '📈',
                    label: 'QT间期 (ms)',
                    value: Math.round(subject.vitalSigns.qtInterval),
                    range: '参考范围: 350-450'
                },
                {
                    id: 'cvp',
                    class: 'cvp',
                    icon: '🔄',
                    label: '中心静脉压 (mmHg)',
                    value: Math.round(subject.vitalSigns.cvp),
                    range: '参考范围: 2-8'
                }
            ];
            
            vitalCards.forEach(card => {
                const cardElement = document.createElement('div');
                cardElement.className = `vital-card ${card.class}`;
                cardElement.onclick = () => showParameterDetail(card.id);
                cardElement.innerHTML = `
                    <div class="icon">${card.icon}</div>
                    <div class="value">${card.value}</div>
                    <div class="label">${card.label}</div>
                    <div style="font-size: 0.8em; color: #999; margin-top: 5px;">${card.range}</div>
                `;
                vitalsGrid.appendChild(cardElement);
            });
            
            updateDebugInfo(`✅ 生理参数渲染完成，共 ${vitalCards.length} 个参数`);
        }
        
        // 显示参数详细信息
        function showParameterDetail(parameterId) {
            updateDebugInfo(`📈 点击参数: ${parameterId}`);
            alert(`参数详细分析功能开发中...\n参数: ${parameterId}\n对象: ${currentSubjectId}`);
        }
        
        // 返回管理界面
        function backToManagement() {
            updateDebugInfo(`🔙 返回管理界面`);
            
            document.getElementById('detailView').classList.remove('active');
            document.getElementById('managementView').classList.remove('hidden');
            
            currentSubjectId = null;
        }
        
        // 测试系统
        function testSystem() {
            updateDebugInfo('🧪 开始系统测试');
            
            // 测试数据
            updateDebugInfo(`📊 对象数据测试: ${Object.keys(subjects).length} 个对象`);
            
            // 测试DOM元素
            const elements = ['subjectsGrid', 'statusInfo', 'debugContent'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                updateDebugInfo(`🔍 DOM元素 ${id}: ${element ? '存在' : '不存在'}`);
            });
            
            // 测试Chart.js
            updateDebugInfo(`📈 Chart.js: ${typeof Chart !== 'undefined' ? '已加载' : '未加载'}`);
            
            updateDebugInfo('✅ 系统测试完成');
        }
        
        // 强制重新渲染
        function forceRender() {
            updateDebugInfo('🔄 强制重新渲染');
            initializeSystem();
        }
        
        // 显示调试信息
        function showDebugInfo() {
            const info = {
                subjects: Object.keys(subjects).length,
                activeSubjects: Object.values(subjects).filter(s => s.isActive).length,
                currentSubject: currentSubjectId,
                chartJS: typeof Chart !== 'undefined',
                timestamp: new Date().toLocaleString()
            };
            
            alert(`系统调试信息:\n${JSON.stringify(info, null, 2)}`);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('🌟 页面加载完成，开始初始化');
            
            try {
                initializeSystem();
                updateDebugInfo('🎉 系统启动成功');
            } catch (error) {
                updateDebugInfo('💥 系统启动失败: ' + error.message);
                console.error('系统启动失败:', error);
            }
        });
    </script>
</body>
</html>
