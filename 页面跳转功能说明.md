# 智能医疗监测系统 - 页面跳转功能说明

## 🎯 功能概述

系统现已支持页面跳转功能，为短期检测和长期监测创建了专门的独立页面，提供更专业和专注的用户体验。

## 📄 页面结构

### 🏠 **主页面** (`完善版智能医疗监测系统.html`)
- **功能**: 系统总控制台和导航中心
- **包含模块**: 病人管理、历史记录、设备连接、数据分析
- **作用**: 作为系统入口，提供完整的管理功能

### ⚡ **短期检测页面** (`短期检测页面.html`)
- **功能**: 专门的5秒快速检测界面
- **特色**: 倒计时器、进度条、实时数据采集
- **适用场景**: 快速体检、初步筛查、门诊检测

### 📊 **长期监测页面** (`长期监测页面.html`)
- **功能**: 24小时连续监测界面
- **特色**: 实时趋势图、ECG波形、监测设置
- **适用场景**: 重症监护、慢病管理、住院监测

## 🔄 页面跳转逻辑

### 从主页跳转
```javascript
// 点击短期检测按钮
switchMode('short') → window.location.href = '短期检测页面.html'

// 点击长期监测按钮  
switchMode('long') → window.location.href = '长期监测页面.html'
```

### 返回主页
```javascript
// 在子页面点击返回按钮
goBack() → window.location.href = '完善版智能医疗监测系统.html'
```

## 🎨 页面设计特色

### 短期检测页面特色
- **🔴 红色主题**: 突出紧急和快速的特性
- **⏱️ 倒计时器**: 大字体显示剩余时间
- **📊 进度条**: 直观显示检测进度
- **🎯 专注设计**: 简化界面，专注检测过程
- **📱 移动优化**: 完美适配手机和平板

### 长期监测页面特色
- **🟢 绿色主题**: 体现稳定和持续的特性
- **📈 实时图表**: 显示最近1小时的数据趋势
- **⚙️ 监测设置**: 可调节采样频率、监测时长等
- **🚨 智能预警**: 多级敏感度的异常检测
- **💾 数据存储**: 支持本地和云端存储选项

## 🛠️ 使用方法

### 基本操作流程

#### 1. **启动系统**
```bash
# 双击主页面文件
完善版智能医疗监测系统.html
```

#### 2. **选择检测模式**
- 点击 **"⚡ 短期检测"** → 跳转到快速检测页面
- 点击 **"📊 长期监测"** → 跳转到连续监测页面

#### 3. **在专门页面进行操作**
- **短期检测**: 点击"开始检测" → 等待5秒 → 查看结果
- **长期监测**: 配置设置 → 点击"开始监测" → 持续观察

#### 4. **返回主页**
- 点击页面左上角的 **"←"** 返回按钮
- 或按 **Esc** 键快速返回

## ⌨️ 快捷键支持

### 主页面快捷键
| 快捷键 | 功能 |
|--------|------|
| Ctrl+1 | 跳转到短期检测页面 |
| Ctrl+2 | 跳转到长期监测页面 |
| Ctrl+3 | 切换到设备连接模式 |
| Ctrl+4 | 切换到数据分析模式 |
| Ctrl+5 | 切换到病人管理模式 |
| Ctrl+6 | 切换到历史记录模式 |

### 短期检测页面快捷键
| 快捷键 | 功能 |
|--------|------|
| 空格键 | 开始/暂停检测 |
| Enter | 开始检测 |
| Esc | 返回主页 |

### 长期监测页面快捷键
| 快捷键 | 功能 |
|--------|------|
| 空格键 | 开始/暂停监测 |
| Ctrl+S | 导出数据 |
| Esc | 返回主页 |

## 📊 数据同步机制

### 数据存储位置
- **本地存储**: 使用浏览器localStorage保存数据
- **跨页面共享**: 通过localStorage实现页面间数据共享
- **自动备份**: 重要数据自动保存到本地

### 数据传递流程
```
主页面 ←→ localStorage ←→ 子页面
   ↓                        ↓
病人信息                  检测结果
历史记录                  监测数据
系统设置                  报告数据
```

## 🔧 技术实现

### 页面跳转实现
```javascript
// 主页面跳转逻辑
function switchMode(mode) {
    switch(mode) {
        case 'short':
            window.location.href = '短期检测页面.html';
            break;
        case 'long':
            window.location.href = '长期监测页面.html';
            break;
        // 其他模式在当前页面切换
    }
}

// 子页面返回逻辑
function goBack() {
    window.location.href = '完善版智能医疗监测系统.html';
}
```

### 数据持久化
```javascript
// 保存检测结果
function saveResults() {
    const data = {
        ...testResults,
        testType: '短期检测',
        timestamp: new Date().toISOString()
    };
    
    const savedResults = JSON.parse(localStorage.getItem('medicalResults') || '[]');
    savedResults.push(data);
    localStorage.setItem('medicalResults', JSON.stringify(savedResults));
}
```

## 🎯 使用场景

### 短期检测适用场景
- **🏥 门诊快速体检**: 5秒完成基础生理参数检测
- **🚑 急诊初步筛查**: 快速评估患者基本状况
- **👨‍⚕️ 医生查房**: 快速获取患者当前状态
- **🏃‍♂️ 运动前检测**: 确认身体状况适合运动

### 长期监测适用场景
- **🏥 ICU重症监护**: 24小时连续生命体征监测
- **🏠 慢病管理**: 高血压、糖尿病等慢性病监测
- **🛏️ 住院患者**: 术后恢复期连续监测
- **👴 老年护理**: 居家养老健康监测

## 📱 移动端优化

### 响应式设计
- **自适应布局**: 根据屏幕尺寸自动调整
- **触摸优化**: 按钮大小适合手指操作
- **字体缩放**: 在小屏幕上保持可读性
- **手势支持**: 支持滑动和缩放操作

### 移动端特色功能
- **震动反馈**: 检测完成时提供震动提示
- **语音播报**: 可选的语音结果播报
- **离线模式**: 无网络时仍可正常使用
- **省电模式**: 优化电池使用

## 🔒 安全考虑

### 数据安全
- **本地存储**: 敏感数据不上传到服务器
- **加密存储**: 重要数据进行本地加密
- **访问控制**: 防止未授权访问
- **数据清理**: 定期清理过期数据

### 隐私保护
- **匿名化**: 可选的数据匿名化处理
- **权限控制**: 严格的数据访问权限
- **审计日志**: 记录数据访问和修改
- **合规性**: 符合医疗数据保护法规

## 🚀 未来扩展

### 计划功能
- **更多专门页面**: 为其他功能创建专门页面
- **页面间通信**: 实现更复杂的数据交互
- **离线同步**: 支持离线数据同步
- **多语言支持**: 国际化界面支持

### 技术升级
- **PWA支持**: 渐进式Web应用功能
- **WebRTC**: 实时音视频通信
- **WebAssembly**: 高性能数据处理
- **Service Worker**: 离线缓存和后台同步

---

**© 2024 智能医疗监测系统 | 页面跳转功能 v2.0**
