<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生理数据模拟器测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .output {
            background: #2d3436;
            color: #00b894;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .data-display {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        .data-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .data-item .value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 生理数据模拟器测试</h1>
        <p>测试与CH32V307VCT6相同的数据生成逻辑</p>

        <div class="test-section">
            <h3>📡 模拟器控制</h3>
            <button class="btn" onclick="startTest()">开始测试</button>
            <button class="btn" onclick="stopTest()">停止测试</button>
            <button class="btn" onclick="clearOutput()">清空输出</button>
            <button class="btn" onclick="testSingleData()">生成单次数据</button>
        </div>

        <div class="test-section">
            <h3>📊 实时数据显示</h3>
            <div class="data-display">
                <div class="data-item">
                    <div class="value" id="heartRate">--</div>
                    <div>心率 (次/分)</div>
                </div>
                <div class="data-item">
                    <div class="value" id="spo2">--</div>
                    <div>血氧 (%)</div>
                </div>
                <div class="data-item">
                    <div class="value" id="temperature">--</div>
                    <div>体温 (°C)</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 输出日志</h3>
            <div class="output" id="output"></div>
        </div>
    </div>

    <script>
        // 模拟CH32V307VCT6的数据生成逻辑
        class HealthDataSimulator {
            constructor() {
                this.isRunning = false;
                this.interval = null;
                
                // 心率模拟参数（与CH32V307VCT6相同）
                this.HR_BASE_VALUE = 75;
                this.HR_MIN_VALUE = 55;
                this.HR_MAX_VALUE = 105;
                this.HR_CHANGE_PROBABILITY = 95;
                this.HR_MAX_CHANGE = 3;
                this.simulatedHeartRate = this.HR_BASE_VALUE;
                this.randomSeed = 12345;
                
                // 心率影响因子
                this.stressLevel = 0;
                this.activityIntensity = 0;
                this.fatigueLevel = 0;
                this.dailyCycleCounter = 0;
                this.hrSimulationMode = 2; // 0-睡眠, 1-休息, 2-正常, 3-活跃, 4-运动
                this.modeChangeTimer = 0;
                this.changeCounter = 0;
            }
            
            // 简单随机数生成器（与CH32V307VCT6相同）
            simpleRandom() {
                this.randomSeed = (this.randomSeed * 1103515245 + 12345) & 0x7FFFFFFF;
                return this.randomSeed;
            }
            
            // 获取范围内随机数
            getRandomInRange(min, max) {
                return min + (this.simpleRandom() % (max - min + 1));
            }
            
            // 应用自然变化（与CH32V307VCT6相同）
            applyNaturalVariation(value) {
                const variation = this.getRandomInRange(-2, 2);
                let result = value + variation;
                
                if (result < this.HR_MIN_VALUE) result = this.HR_MIN_VALUE;
                if (result > this.HR_MAX_VALUE) result = this.HR_MAX_VALUE;
                
                return result;
            }
            
            // 更新心率影响因子
            updateHeartRateFactors() {
                this.dailyCycleCounter++;
                
                // 模拟日常周期影响
                const hourOfDay = (this.dailyCycleCounter / 60) % 24;
                
                // 根据时间调整基础参数
                if (hourOfDay >= 22 || hourOfDay <= 6) {
                    this.stressLevel = Math.max(0, this.stressLevel - 1);
                    this.activityIntensity = 0;
                    this.fatigueLevel = Math.min(10, this.fatigueLevel + 1);
                } else if (hourOfDay >= 7 && hourOfDay <= 9) {
                    this.stressLevel = Math.min(10, this.stressLevel + 2);
                    this.activityIntensity = Math.min(10, this.activityIntensity + 3);
                }
                
                // 随机波动
                if (this.getRandomInRange(0, 100) < 20) {
                    this.stressLevel = Math.max(0, Math.min(10, this.stressLevel + this.getRandomInRange(-2, 2)));
                    this.activityIntensity = Math.max(0, Math.min(10, this.activityIntensity + this.getRandomInRange(-1, 1)));
                    this.fatigueLevel = Math.max(0, Math.min(10, this.fatigueLevel + this.getRandomInRange(-1, 1)));
                }
            }
            
            // 心率模拟（与CH32V307VCT6相同逻辑）
            simulateHeartRate() {
                this.updateHeartRateFactors();
                
                // 定期切换模式
                this.modeChangeTimer++;
                if (this.modeChangeTimer > 80) {
                    this.modeChangeTimer = 0;
                    this.hrSimulationMode = this.getRandomInRange(0, 4);
                }
                
                // 控制变化频率
                this.changeCounter++;
                if (this.changeCounter < 1) {
                    return this.simulatedHeartRate;
                }
                this.changeCounter = 0;
                
                // 根据概率决定是否变化
                if (this.getRandomInRange(0, 100) > this.HR_CHANGE_PROBABILITY) {
                    return this.simulatedHeartRate;
                }
                
                // 根据模式设置目标心率
                let targetHR = this.HR_BASE_VALUE;
                switch (this.hrSimulationMode) {
                    case 0: targetHR = 60; break; // 睡眠
                    case 1: targetHR = 70; break; // 休息
                    case 2: targetHR = 75; break; // 正常
                    case 3: targetHR = 85; break; // 活跃
                    case 4: targetHR = 95; break; // 运动
                }
                
                // 应用影响因子
                targetHR += this.stressLevel * 2;
                targetHR += this.activityIntensity * 1.5;
                targetHR -= this.fatigueLevel * 0.5;
                
                // 计算变化方向和幅度
                const diff = targetHR - this.simulatedHeartRate;
                let change = 0;
                
                if (Math.abs(diff) > 10) {
                    change = diff > 0 ? this.HR_MAX_CHANGE : -this.HR_MAX_CHANGE;
                } else if (Math.abs(diff) > 5) {
                    change = diff > 0 ? 2 : -2;
                } else {
                    change = this.getRandomInRange(-this.HR_MAX_CHANGE, this.HR_MAX_CHANGE);
                }
                
                let newHR = this.simulatedHeartRate + change;
                
                // 限制范围
                if (newHR < this.HR_MIN_VALUE) newHR = this.HR_MIN_VALUE;
                if (newHR > this.HR_MAX_VALUE) newHR = this.HR_MAX_VALUE;
                
                this.simulatedHeartRate = newHR;
                return this.applyNaturalVariation(this.simulatedHeartRate);
            }
            
            // 模拟血氧数据
            simulateSpO2() {
                const baseSpO2 = 94;
                const variation = this.getRandomInRange(-4, 6);
                let spo2 = baseSpO2 + variation;
                
                // 限制在80-100范围内
                if (spo2 < 80) spo2 = 80;
                if (spo2 > 100) spo2 = 100;
                
                return spo2;
            }
            
            // 模拟体温数据
            simulateTemperature() {
                const baseTemp = 36.5;
                const variation = (this.getRandomInRange(-10, 10) / 10.0);
                let temp = baseTemp + variation;
                
                // 限制在35.0-40.0范围内
                if (temp < 35.0) temp = 35.0;
                if (temp > 40.0) temp = 40.0;
                
                return parseFloat(temp.toFixed(1));
            }
            
            // 生成完整健康数据
            generateHealthData() {
                const heartRate = this.simulateHeartRate();
                const spo2 = this.simulateSpO2();
                const temperature = this.simulateTemperature();
                
                return { heartRate, spo2, temperature };
            }
        }
        
        // 全局变量
        let simulator = new HealthDataSimulator();
        let testInterval = null;
        
        // 添加输出日志
        function addOutput(text) {
            const output = document.getElementById('output');
            const line = document.createElement('div');
            line.textContent = `[${new Date().toLocaleTimeString()}] ${text}`;
            output.appendChild(line);
            output.scrollTop = output.scrollHeight;
        }
        
        // 更新数据显示
        function updateDisplay(data) {
            document.getElementById('heartRate').textContent = data.heartRate;
            document.getElementById('spo2').textContent = data.spo2;
            document.getElementById('temperature').textContent = data.temperature;
        }
        
        // 开始测试
        function startTest() {
            if (testInterval) return;
            
            addOutput('开始生理数据模拟测试');
            testInterval = setInterval(() => {
                const data = simulator.generateHealthData();
                updateDisplay(data);
                
                // 生成与CH32V307VCT6相同格式的日志
                const logMessage = `健康数据:心率=${data.heartRate}次/分,血氧=${data.spo2}%,体温=${data.temperature}°C`;
                addOutput(logMessage);
            }, 1000);
        }
        
        // 停止测试
        function stopTest() {
            if (testInterval) {
                clearInterval(testInterval);
                testInterval = null;
                addOutput('停止生理数据模拟测试');
            }
        }
        
        // 生成单次数据
        function testSingleData() {
            const data = simulator.generateHealthData();
            updateDisplay(data);
            const logMessage = `健康数据:心率=${data.heartRate}次/分,血氧=${data.spo2}%,体温=${data.temperature}°C`;
            addOutput(logMessage);
        }
        
        // 清空输出
        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            addOutput('生理数据模拟器测试页面已加载');
            addOutput('使用与CH32V307VCT6完全相同的数据生成算法');
            addOutput('点击"开始测试"按钮开始连续生成数据');
        });
    </script>
</body>
</html>
