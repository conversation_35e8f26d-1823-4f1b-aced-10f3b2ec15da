<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短期检测 - 智能医疗监测系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border-radius: 15px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding: 10px 15px;
            background: rgba(255,255,255,0.8);
            border-radius: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: #666;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb-item:hover {
            color: #4facfe;
        }

        .breadcrumb-item.active {
            color: #333;
            font-weight: bold;
        }

        .breadcrumb-separator {
            color: #999;
        }

        .quick-test-panel {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            text-align: center;
        }

        .test-timer {
            font-size: 4em;
            font-weight: bold;
            color: #ff6b6b;
            margin: 20px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .test-status {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #666;
        }

        .test-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            min-width: 150px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #2ed573 0%, #17c0eb 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffa726 0%, #fb8c00 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .data-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .data-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #ee5a24);
        }

        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .data-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
            display: block;
        }

        .data-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .data-label {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 10px;
        }

        .data-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-normal { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-danger { background: #f8d7da; color: #721c24; }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ee5a24);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .test-instructions {
            background: #e8f4fd;
            border-left: 4px solid #4facfe;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .test-instructions h3 {
            color: #4facfe;
            margin-bottom: 10px;
        }

        .test-instructions ul {
            margin-left: 20px;
        }

        .test-instructions li {
            margin-bottom: 5px;
            color: #666;
        }

        .results-panel {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            display: none;
        }

        .results-panel.show {
            display: block;
            animation: slideIn 0.5s ease;
        }

        @keyframes slideIn {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .results-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .result-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .result-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .result-label {
            color: #666;
            font-size: 1.1em;
        }

        .result-trend {
            font-size: 0.9em;
            margin-top: 5px;
        }

        .trend-up { color: #e74c3c; }
        .trend-down { color: #27ae60; }
        .trend-stable { color: #f39c12; }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.9);
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 5px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .test-timer {
                font-size: 3em;
            }
            
            .test-controls {
                flex-direction: column;
                align-items: center;
            }
            
            .data-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goBack()" title="返回主页">←</button>
    
    <div class="container">
        <div class="header">
            <h1>⚡ 短期检测模式</h1>
            <p>5秒快速生理参数检测 | 适用于快速体检和初步筛查</p>
        </div>

        <div class="breadcrumb">
            <a href="#" class="breadcrumb-item" onclick="goBack()">🏠 首页</a>
            <span class="breadcrumb-separator">></span>
            <span class="breadcrumb-item active">短期检测</span>
        </div>

        <div class="test-instructions">
            <h3>📋 检测说明</h3>
            <ul>
                <li>请保持安静，避免剧烈运动</li>
                <li>确保传感器正确佩戴</li>
                <li>检测过程中请勿移动</li>
                <li>整个检测过程约需5秒钟</li>
                <li>检测完成后将显示详细结果</li>
            </ul>
        </div>

        <div class="quick-test-panel">
            <h2>🔍 快速检测</h2>
            <div class="test-timer" id="testTimer">5.0</div>
            <div class="test-status" id="testStatus">准备开始检测</div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div class="test-controls">
                <button class="btn btn-success" id="startBtn" onclick="startQuickTest()">开始检测</button>
                <button class="btn btn-warning" id="pauseBtn" onclick="pauseTest()" disabled>暂停</button>
                <button class="btn btn-danger" id="stopBtn" onclick="stopTest()" disabled>停止</button>
                <button class="btn btn-primary" onclick="retestAll()">重新检测</button>
            </div>
        </div>

        <div class="data-grid">
            <div class="data-card">
                <div class="data-icon">❤️</div>
                <div class="data-value" id="heartRate">--</div>
                <div class="data-label">心率 (bpm)</div>
                <div class="data-status status-normal" id="heartRateStatus">待检测</div>
            </div>

            <div class="data-card">
                <div class="data-icon">🩸</div>
                <div class="data-value" id="bloodPressure">--/--</div>
                <div class="data-label">血压 (mmHg)</div>
                <div class="data-status status-normal" id="bloodPressureStatus">待检测</div>
            </div>

            <div class="data-card">
                <div class="data-icon">🫁</div>
                <div class="data-value" id="spo2">--</div>
                <div class="data-label">血氧饱和度 (%)</div>
                <div class="data-status status-normal" id="spo2Status">待检测</div>
            </div>

            <div class="data-card">
                <div class="data-icon">🌡️</div>
                <div class="data-value" id="temperature">--</div>
                <div class="data-label">体温 (°C)</div>
                <div class="data-status status-normal" id="temperatureStatus">待检测</div>
            </div>

            <div class="data-card">
                <div class="data-icon">🩺</div>
                <div class="data-value" id="bloodGlucose">--</div>
                <div class="data-label">血糖 (mmol/L)</div>
                <div class="data-status status-normal" id="bloodGlucoseStatus">待检测</div>
            </div>

            <div class="data-card">
                <div class="data-icon">💨</div>
                <div class="data-value" id="respiratoryRate">--</div>
                <div class="data-label">呼吸频率 (次/分)</div>
                <div class="data-status status-normal" id="respiratoryRateStatus">待检测</div>
            </div>
        </div>

        <div class="results-panel" id="resultsPanel">
            <h2>📊 检测结果</h2>
            <div class="results-summary" id="resultsSummary">
                <!-- 结果将在这里动态生成 -->
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-success" onclick="saveResults()">保存结果</button>
                <button class="btn btn-primary" onclick="printResults()">打印报告</button>
                <button class="btn btn-warning" onclick="shareResults()">分享结果</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let testTimer = null;
        let currentTime = 5.0;
        let isTestRunning = false;
        let isPaused = false;
        let testResults = {};

        // 返回主页
        function goBack() {
            window.location.href = '完善版智能医疗监测系统.html';
        }

        // 开始快速检测
        function startQuickTest() {
            if (isTestRunning) return;
            
            isTestRunning = true;
            isPaused = false;
            currentTime = 5.0;
            
            // 更新按钮状态
            document.getElementById('startBtn').disabled = true;
            document.getElementById('pauseBtn').disabled = false;
            document.getElementById('stopBtn').disabled = false;
            
            // 更新状态显示
            document.getElementById('testStatus').textContent = '正在检测中...';
            
            // 隐藏结果面板
            document.getElementById('resultsPanel').classList.remove('show');
            
            // 重置数据显示
            resetDataDisplay();
            
            // 开始倒计时
            testTimer = setInterval(updateTimer, 100);
            
            // 开始模拟数据采集
            startDataCollection();
        }

        // 暂停检测
        function pauseTest() {
            if (!isTestRunning) return;
            
            if (isPaused) {
                // 恢复检测
                isPaused = false;
                testTimer = setInterval(updateTimer, 100);
                document.getElementById('pauseBtn').textContent = '暂停';
                document.getElementById('testStatus').textContent = '正在检测中...';
            } else {
                // 暂停检测
                isPaused = true;
                clearInterval(testTimer);
                document.getElementById('pauseBtn').textContent = '继续';
                document.getElementById('testStatus').textContent = '检测已暂停';
            }
        }

        // 停止检测
        function stopTest() {
            if (!isTestRunning) return;
            
            isTestRunning = false;
            isPaused = false;
            clearInterval(testTimer);
            
            // 重置按钮状态
            document.getElementById('startBtn').disabled = false;
            document.getElementById('pauseBtn').disabled = true;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('pauseBtn').textContent = '暂停';
            
            // 更新状态显示
            document.getElementById('testStatus').textContent = '检测已停止';
            document.getElementById('testTimer').textContent = '5.0';
            document.getElementById('progressFill').style.width = '0%';
            
            // 重置数据显示
            resetDataDisplay();
        }

        // 重新检测
        function retestAll() {
            stopTest();
            setTimeout(() => {
                startQuickTest();
            }, 500);
        }

        // 更新计时器
        function updateTimer() {
            if (isPaused) return;
            
            currentTime -= 0.1;
            
            if (currentTime <= 0) {
                currentTime = 0;
                completeTest();
            }
            
            // 更新显示
            document.getElementById('testTimer').textContent = currentTime.toFixed(1);
            
            // 更新进度条
            const progress = ((5.0 - currentTime) / 5.0) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 完成检测
        function completeTest() {
            clearInterval(testTimer);
            isTestRunning = false;
            
            // 重置按钮状态
            document.getElementById('startBtn').disabled = false;
            document.getElementById('pauseBtn').disabled = true;
            document.getElementById('stopBtn').disabled = true;
            
            // 更新状态显示
            document.getElementById('testStatus').textContent = '检测完成！';
            
            // 显示最终结果
            showFinalResults();
        }

        // 开始数据采集
        function startDataCollection() {
            const dataCollectionInterval = setInterval(() => {
                if (!isTestRunning || isPaused) {
                    clearInterval(dataCollectionInterval);
                    return;
                }
                
                // 模拟实时数据更新
                updateVitalSigns();
                
                if (currentTime <= 0) {
                    clearInterval(dataCollectionInterval);
                }
            }, 200);
        }

        // 更新生理参数
        function updateVitalSigns() {
            // 模拟数据生成（更真实的变化）
            const heartRate = Math.round(70 + Math.random() * 30 + Math.sin(Date.now() / 1000) * 5);
            const systolicBP = Math.round(115 + Math.random() * 20 + Math.sin(Date.now() / 1500) * 3);
            const diastolicBP = Math.round(75 + Math.random() * 15 + Math.sin(Date.now() / 1200) * 2);
            const spo2 = Math.round(96 + Math.random() * 4);
            const temperature = (36.2 + Math.random() * 1.0 + Math.sin(Date.now() / 2000) * 0.2).toFixed(1);
            const bloodGlucose = (4.5 + Math.random() * 2.0).toFixed(1);
            const respiratoryRate = Math.round(14 + Math.random() * 6);
            
            // 更新显示
            document.getElementById('heartRate').textContent = heartRate;
            document.getElementById('bloodPressure').textContent = `${systolicBP}/${diastolicBP}`;
            document.getElementById('spo2').textContent = spo2;
            document.getElementById('temperature').textContent = temperature;
            document.getElementById('bloodGlucose').textContent = bloodGlucose;
            document.getElementById('respiratoryRate').textContent = respiratoryRate;
            
            // 更新状态
            updateVitalStatus('heartRate', heartRate, 60, 100);
            updateVitalStatus('bloodPressure', systolicBP, 90, 140);
            updateVitalStatus('spo2', spo2, 95, 100);
            updateVitalStatus('temperature', parseFloat(temperature), 36.0, 37.5);
            updateVitalStatus('bloodGlucose', parseFloat(bloodGlucose), 3.9, 6.1);
            updateVitalStatus('respiratoryRate', respiratoryRate, 12, 20);
            
            // 保存最终结果
            testResults = {
                heartRate,
                systolicBP,
                diastolicBP,
                spo2,
                temperature: parseFloat(temperature),
                bloodGlucose: parseFloat(bloodGlucose),
                respiratoryRate,
                timestamp: new Date().toISOString()
            };
        }

        // 更新生理参数状态
        function updateVitalStatus(param, value, min, max) {
            const statusElement = document.getElementById(param + 'Status');
            if (value < min || value > max) {
                statusElement.textContent = '异常';
                statusElement.className = 'data-status status-danger';
            } else if (value < min * 1.1 || value > max * 0.9) {
                statusElement.textContent = '注意';
                statusElement.className = 'data-status status-warning';
            } else {
                statusElement.textContent = '正常';
                statusElement.className = 'data-status status-normal';
            }
        }

        // 重置数据显示
        function resetDataDisplay() {
            const params = ['heartRate', 'bloodPressure', 'spo2', 'temperature', 'bloodGlucose', 'respiratoryRate'];
            params.forEach(param => {
                if (param === 'bloodPressure') {
                    document.getElementById(param).textContent = '--/--';
                } else {
                    document.getElementById(param).textContent = '--';
                }
                document.getElementById(param + 'Status').textContent = '待检测';
                document.getElementById(param + 'Status').className = 'data-status status-normal';
            });
        }

        // 显示最终结果
        function showFinalResults() {
            const resultsPanel = document.getElementById('resultsPanel');
            const resultsSummary = document.getElementById('resultsSummary');

            // 生成结果摘要 - 显示全部6种数据
            let summaryHTML = '';

            // 心率结果
            summaryHTML += `
                <div class="result-item">
                    <div class="result-value" style="color: #ff6b6b;">${testResults.heartRate}</div>
                    <div class="result-label">心率 (bpm)</div>
                    <div class="result-trend ${getTrend(testResults.heartRate, 75)}">${getTrendText(testResults.heartRate, 75)}</div>
                </div>
            `;

            // 血压结果
            summaryHTML += `
                <div class="result-item">
                    <div class="result-value" style="color: #4ecdc4;">${testResults.systolicBP}/${testResults.diastolicBP}</div>
                    <div class="result-label">血压 (mmHg)</div>
                    <div class="result-trend ${getTrend(testResults.systolicBP, 120)}">${getTrendText(testResults.systolicBP, 120)}</div>
                </div>
            `;

            // 血氧结果
            summaryHTML += `
                <div class="result-item">
                    <div class="result-value" style="color: #45b7d1;">${testResults.spo2}%</div>
                    <div class="result-label">血氧饱和度</div>
                    <div class="result-trend ${getTrend(testResults.spo2, 98)}">${getTrendText(testResults.spo2, 98)}</div>
                </div>
            `;

            // 体温结果
            summaryHTML += `
                <div class="result-item">
                    <div class="result-value" style="color: #f39c12;">${testResults.temperature}°C</div>
                    <div class="result-label">体温</div>
                    <div class="result-trend ${getTrend(testResults.temperature, 36.5)}">${getTrendText(testResults.temperature, 36.5)}</div>
                </div>
            `;

            // 血糖结果
            summaryHTML += `
                <div class="result-item">
                    <div class="result-value" style="color: #9b59b6;">${testResults.bloodGlucose}</div>
                    <div class="result-label">血糖 (mmol/L)</div>
                    <div class="result-trend ${getTrend(testResults.bloodGlucose, 5.2)}">${getTrendText(testResults.bloodGlucose, 5.2)}</div>
                </div>
            `;

            // 呼吸频率结果
            summaryHTML += `
                <div class="result-item">
                    <div class="result-value" style="color: #e67e22;">${testResults.respiratoryRate}</div>
                    <div class="result-label">呼吸频率 (次/分)</div>
                    <div class="result-trend ${getTrend(testResults.respiratoryRate, 16)}">${getTrendText(testResults.respiratoryRate, 16)}</div>
                </div>
            `;

            resultsSummary.innerHTML = summaryHTML;
            resultsPanel.classList.add('show');
        }

        // 获取趋势类名
        function getTrend(value, normal) {
            if (value > normal * 1.05) return 'trend-up';
            if (value < normal * 0.95) return 'trend-down';
            return 'trend-stable';
        }

        // 获取趋势文本
        function getTrendText(value, normal) {
            if (value > normal * 1.05) return '↗ 偏高';
            if (value < normal * 0.95) return '↘ 偏低';
            return '→ 正常';
        }

        // 保存结果
        function saveResults() {
            const data = {
                ...testResults,
                testType: '短期检测',
                patientId: 'P001',
                testDate: new Date().toISOString()
            };
            
            // 保存到本地存储
            const savedResults = JSON.parse(localStorage.getItem('medicalResults') || '[]');
            savedResults.push(data);
            localStorage.setItem('medicalResults', JSON.stringify(savedResults));
            
            alert('检测结果已保存！');
        }

        // 打印报告
        function printResults() {
            const reportContent = `
                <div style="padding: 20px; font-family: Arial, sans-serif;">
                    <h1 style="text-align: center; color: #333;">短期检测报告</h1>
                    <p style="text-align: center; color: #666; margin-bottom: 30px;">检测时间: ${new Date().toLocaleString()}</p>

                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h3>心率</h3>
                            <p style="font-size: 1.8em; color: #ff6b6b;">${testResults.heartRate} bpm</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h3>血压</h3>
                            <p style="font-size: 1.8em; color: #4ecdc4;">${testResults.systolicBP}/${testResults.diastolicBP} mmHg</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h3>血氧饱和度</h3>
                            <p style="font-size: 1.8em; color: #45b7d1;">${testResults.spo2}%</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h3>体温</h3>
                            <p style="font-size: 1.8em; color: #f39c12;">${testResults.temperature}°C</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h3>血糖</h3>
                            <p style="font-size: 1.8em; color: #9b59b6;">${testResults.bloodGlucose} mmol/L</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <h3>呼吸频率</h3>
                            <p style="font-size: 1.8em; color: #e67e22;">${testResults.respiratoryRate} 次/分</p>
                        </div>
                    </div>

                    <div style="margin-top: 30px; text-align: center; color: #666;">
                        <p>本报告由智能医疗监测系统自动生成</p>
                    </div>
                </div>
            `;

            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head><title>短期检测报告</title></head>
                    <body>${reportContent}</body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // 分享结果
        function shareResults() {
            if (navigator.share) {
                navigator.share({
                    title: '短期检测结果',
                    text: `心率: ${testResults.heartRate} bpm, 血压: ${testResults.systolicBP}/${testResults.diastolicBP} mmHg, 血氧: ${testResults.spo2}%, 体温: ${testResults.temperature}°C, 血糖: ${testResults.bloodGlucose} mmol/L, 呼吸: ${testResults.respiratoryRate} 次/分`,
                    url: window.location.href
                });
            } else {
                // 复制到剪贴板
                const text = `短期检测结果 - 心率: ${testResults.heartRate} bpm, 血压: ${testResults.systolicBP}/${testResults.diastolicBP} mmHg, 血氧: ${testResults.spo2}%, 体温: ${testResults.temperature}°C, 血糖: ${testResults.bloodGlucose} mmol/L, 呼吸: ${testResults.respiratoryRate} 次/分`;
                navigator.clipboard.writeText(text).then(() => {
                    alert('结果已复制到剪贴板！');
                });
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                goBack();
            } else if (e.key === ' ') {
                e.preventDefault();
                if (!isTestRunning) {
                    startQuickTest();
                } else {
                    pauseTest();
                }
            } else if (e.key === 'Enter') {
                if (!isTestRunning) {
                    startQuickTest();
                }
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('短期检测页面已加载');
            
            // 检查是否有保存的结果
            const savedResults = JSON.parse(localStorage.getItem('medicalResults') || '[]');
            if (savedResults.length > 0) {
                console.log(`发现 ${savedResults.length} 条历史记录`);
            }
        });
    </script>
</body>
</html>
