<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>综合生理监测系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 15px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 25px;
        }

        .header h1 {
            color: #333;
            font-size: 2.3em;
            margin-bottom: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        /* 主界面 */
        .main-view {
            display: block;
        }

        .main-view.hidden {
            display: none;
        }

        .vitals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .vital-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .vital-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .vital-card:hover::before {
            left: 100%;
        }

        .vital-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .vital-card::after {
            content: '点击查看详细分析';
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.85em;
            color: #666;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .vital-card:hover::after {
            opacity: 1;
        }

        .vital-card .icon {
            font-size: 3.2em;
            margin-bottom: 12px;
            display: block;
        }

        .vital-card .value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .vital-card .label {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 8px;
        }

        .vital-card .range {
            color: #999;
            font-size: 0.9em;
        }

        /* 生理参数颜色主题 */
        .heart-rate .icon { color: #e74c3c; }
        .heart-rate .value { color: #e74c3c; }
        .heart-rate:hover { border-left: 4px solid #e74c3c; }

        .blood-pressure .icon { color: #8e44ad; }
        .blood-pressure .value { color: #8e44ad; }
        .blood-pressure:hover { border-left: 4px solid #8e44ad; }

        .spo2 .icon { color: #3498db; }
        .spo2 .value { color: #3498db; }
        .spo2:hover { border-left: 4px solid #3498db; }

        .temperature .icon { color: #f39c12; }
        .temperature .value { color: #f39c12; }
        .temperature:hover { border-left: 4px solid #f39c12; }

        .respiratory-rate .icon { color: #27ae60; }
        .respiratory-rate .value { color: #27ae60; }
        .respiratory-rate:hover { border-left: 4px solid #27ae60; }

        .blood-glucose .icon { color: #e67e22; }
        .blood-glucose .value { color: #e67e22; }
        .blood-glucose:hover { border-left: 4px solid #e67e22; }

        .ecg-rhythm .icon { color: #34495e; }
        .ecg-rhythm .value { color: #34495e; }
        .ecg-rhythm:hover { border-left: 4px solid #34495e; }

        .cvp .icon { color: #9b59b6; }
        .cvp .value { color: #9b59b6; }
        .cvp:hover { border-left: 4px solid #9b59b6; }

        /* 详细视图 */
        .detail-view {
            display: none;
        }

        .detail-view.active {
            display: block;
        }

        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .detail-title {
            display: flex;
            align-items: center;
            gap: 18px;
        }

        .detail-title .icon {
            font-size: 2.8em;
        }

        .detail-title .info h2 {
            color: #333;
            margin-bottom: 8px;
            font-size: 1.8em;
        }

        .detail-title .info .current-value {
            font-size: 1.6em;
            font-weight: bold;
        }

        .back-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1em;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .waveform-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
        }

        .waveform-container h3 {
            margin-bottom: 18px;
            color: #333;
            text-align: center;
            font-size: 1.4em;
        }

        .chart-wrapper {
            position: relative;
            height: 380px;
        }

        .info-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .info-panel h3 {
            margin-bottom: 18px;
            color: #333;
            text-align: center;
            font-size: 1.4em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 18px;
            border-radius: 10px;
            text-align: center;
        }

        .info-item .value {
            font-size: 1.6em;
            font-weight: bold;
            color: #333;
            margin-bottom: 6px;
        }

        .info-item .label {
            color: #666;
            font-size: 0.95em;
        }

        .pulse-animation {
            animation: heartbeat 1s infinite;
        }

        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .status-info {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.1em;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        @media (max-width: 768px) {
            .vitals-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            
            .vital-card {
                padding: 20px;
            }
            
            .vital-card .icon {
                font-size: 2.8em;
            }
            
            .vital-card .value {
                font-size: 2.2em;
            }
            
            .detail-header {
                flex-direction: column;
                gap: 15px;
            }
            
            .info-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .vitals-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 综合生理监测系统</h1>
            <p>多参数实时监测，全面评估生理状态</p>
        </div>

        <div class="status-info" id="statusInfo">
            监测系统运行正常 - 8项生理参数实时采集中
        </div>

        <!-- 主界面 -->
        <div class="main-view" id="mainView">
            <div class="vitals-grid">
                <div class="vital-card heart-rate" onclick="openDetailView('heartRate')">
                    <div class="icon">💓</div>
                    <div class="value" id="heartRateValue">72</div>
                    <div class="label">心率 (次/分)</div>
                    <div class="range">参考范围: 60-100</div>
                </div>
                <div class="vital-card blood-pressure" onclick="openDetailView('bloodPressure')">
                    <div class="icon">🩸</div>
                    <div class="value" id="bloodPressureValue">120/80</div>
                    <div class="label">血压 (mmHg)</div>
                    <div class="range">参考范围: 90-140/60-90</div>
                </div>
                <div class="vital-card spo2" onclick="openDetailView('spo2')">
                    <div class="icon">🫁</div>
                    <div class="value" id="spo2Value">98</div>
                    <div class="label">血氧饱和度 (%)</div>
                    <div class="range">参考范围: 95-100</div>
                </div>
                <div class="vital-card temperature" onclick="openDetailView('temperature')">
                    <div class="icon">🌡️</div>
                    <div class="value" id="temperatureValue">36.5</div>
                    <div class="label">体温 (°C)</div>
                    <div class="range">参考范围: 36.1-37.2</div>
                </div>
                <div class="vital-card respiratory-rate" onclick="openDetailView('respiratoryRate')">
                    <div class="icon">🌬️</div>
                    <div class="value" id="respiratoryRateValue">16</div>
                    <div class="label">呼吸频率 (次/分)</div>
                    <div class="range">参考范围: 12-20</div>
                </div>
                <div class="vital-card blood-glucose" onclick="openDetailView('bloodGlucose')">
                    <div class="icon">🍯</div>
                    <div class="value" id="bloodGlucoseValue">5.2</div>
                    <div class="label">血糖 (mmol/L)</div>
                    <div class="range">参考范围: 3.9-6.1</div>
                </div>
                <div class="vital-card ecg-rhythm" onclick="openDetailView('ecgRhythm')">
                    <div class="icon">📈</div>
                    <div class="value" id="ecgRhythmValue">窦性</div>
                    <div class="label">心电节律</div>
                    <div class="range">QT间期: 380-420ms</div>
                </div>
                <div class="vital-card cvp" onclick="openDetailView('cvp')">
                    <div class="icon">🔄</div>
                    <div class="value" id="cvpValue">8</div>
                    <div class="label">中心静脉压 (mmHg)</div>
                    <div class="range">参考范围: 2-12</div>
                </div>
            </div>
        </div>

        <!-- 详细视图 -->
        <div class="detail-view" id="detailView">
            <div class="detail-header">
                <div class="detail-title">
                    <div class="icon" id="detailIcon">💓</div>
                    <div class="info">
                        <h2 id="detailTitle">心率分析</h2>
                        <div class="current-value" id="detailCurrentValue">72 次/分</div>
                    </div>
                </div>
                <button class="back-btn" onclick="backToMain()">← 返回总览</button>
            </div>

            <div class="waveform-container">
                <h3 id="waveformTitle">心率变化趋势</h3>
                <div class="chart-wrapper">
                    <canvas id="detailChart"></canvas>
                </div>
            </div>

            <div class="info-panel">
                <h3>数据分析</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="value" id="currentValueInfo">72</div>
                        <div class="label">当前值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="averageValueInfo">74</div>
                        <div class="label">平均值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="maxValueInfo">85</div>
                        <div class="label">最高值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="minValueInfo">65</div>
                        <div class="label">最低值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="statusInfo">正常</div>
                        <div class="label">状态评估</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="trendInfo">稳定</div>
                        <div class="label">变化趋势</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentDetailParameter = null;
        let detailChart = null;
        let autoUpdateInterval = null;
        
        // 扩展的生理数据
        let vitalSigns = {
            heartRate: 72,
            systolicBP: 120,
            diastolicBP: 80,
            spo2: 98,
            temperature: 36.5,
            respiratoryRate: 16,
            bloodGlucose: 5.2,
            qtInterval: 400,
            cvp: 8
        };
        
        // 扩展的数据历史
        let dataHistory = {
            heartRate: [],
            systolicBP: [],
            spo2: [],
            temperature: [],
            respiratoryRate: [],
            bloodGlucose: [],
            qtInterval: [],
            cvp: [],
            timeLabels: []
        };
        
        // 扩展的统计数据
        let statistics = {
            heartRate: { sum: 0, count: 0, min: Infinity, max: -Infinity },
            systolicBP: { sum: 0, count: 0, min: Infinity, max: -Infinity },
            spo2: { sum: 0, count: 0, min: Infinity, max: -Infinity },
            temperature: { sum: 0, count: 0, min: Infinity, max: -Infinity },
            respiratoryRate: { sum: 0, count: 0, min: Infinity, max: -Infinity },
            bloodGlucose: { sum: 0, count: 0, min: Infinity, max: -Infinity },
            qtInterval: { sum: 0, count: 0, min: Infinity, max: -Infinity },
            cvp: { sum: 0, count: 0, min: Infinity, max: -Infinity }
        };
        
        // 初始化系统
        function initializeSystem() {
            updateDisplay();
            generateInitialHistoryData();
        }

        // 生成初始历史数据（模拟过去30分钟的数据）
        function generateInitialHistoryData() {
            const now = new Date();
            for (let i = 29; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 60000);
                dataHistory.timeLabels.push(time.toLocaleTimeString().slice(0, 5));

                // 心率数据
                const baseHR = 72 + (Math.random() - 0.5) * 8;
                dataHistory.heartRate.push(Math.round(Math.max(65, Math.min(85, baseHR))));

                // 血压数据
                const baseSys = 120 + (Math.random() - 0.5) * 10;
                dataHistory.systolicBP.push(Math.round(Math.max(110, Math.min(135, baseSys))));

                // 血氧数据
                const baseSpo2 = 98 + (Math.random() - 0.5) * 2;
                dataHistory.spo2.push(Math.round(Math.max(96, Math.min(100, baseSpo2))));

                // 体温数据
                const baseTemp = 36.5 + (Math.random() - 0.5) * 0.4;
                dataHistory.temperature.push(parseFloat(Math.max(36.2, Math.min(36.8, baseTemp)).toFixed(1)));

                // 呼吸频率数据
                const baseRR = 16 + (Math.random() - 0.5) * 4;
                dataHistory.respiratoryRate.push(Math.round(Math.max(14, Math.min(18, baseRR))));

                // 血糖数据
                const baseGlucose = 5.2 + (Math.random() - 0.5) * 0.8;
                dataHistory.bloodGlucose.push(parseFloat(Math.max(4.5, Math.min(5.8, baseGlucose)).toFixed(1)));

                // QT间期数据
                const baseQT = 400 + (Math.random() - 0.5) * 30;
                dataHistory.qtInterval.push(Math.round(Math.max(380, Math.min(420, baseQT))));

                // 中心静脉压数据
                const baseCVP = 8 + (Math.random() - 0.5) * 4;
                dataHistory.cvp.push(Math.round(Math.max(6, Math.min(10, baseCVP))));
            }

            updateAllStatistics();
        }

        // 更新所有统计数据
        function updateAllStatistics() {
            Object.keys(dataHistory).forEach(key => {
                if (key !== 'timeLabels') {
                    const data = dataHistory[key];
                    const stat = statistics[key];

                    stat.sum = data.reduce((a, b) => a + b, 0);
                    stat.count = data.length;
                    stat.min = Math.min(...data);
                    stat.max = Math.max(...data);
                }
            });
        }

        // 开始自动更新
        function startAutoUpdate() {
            autoUpdateInterval = setInterval(() => {
                generateNewData();
                updateDisplay();
                updateDataHistory();

                if (currentDetailParameter) {
                    updateDetailView();
                }
            }, 3000);
        }

        // 更新显示
        function updateDisplay() {
            document.getElementById('heartRateValue').textContent = Math.round(vitalSigns.heartRate);
            document.getElementById('bloodPressureValue').textContent = `${Math.round(vitalSigns.systolicBP)}/${Math.round(vitalSigns.diastolicBP)}`;
            document.getElementById('spo2Value').textContent = Math.round(vitalSigns.spo2);
            document.getElementById('temperatureValue').textContent = vitalSigns.temperature.toFixed(1);
            document.getElementById('respiratoryRateValue').textContent = Math.round(vitalSigns.respiratoryRate);
            document.getElementById('bloodGlucoseValue').textContent = vitalSigns.bloodGlucose.toFixed(1);

            // 心电节律显示
            const qtValue = Math.round(vitalSigns.qtInterval);
            let rhythmText = '窦性';
            if (qtValue > 420) rhythmText = '窦缓';
            else if (qtValue < 380) rhythmText = '窦速';
            document.getElementById('ecgRhythmValue').textContent = rhythmText;

            document.getElementById('cvpValue').textContent = Math.round(vitalSigns.cvp);

            // 心跳动画
            const heartCard = document.querySelector('.heart-rate');
            if (heartCard) {
                heartCard.classList.add('pulse-animation');
                setTimeout(() => heartCard.classList.remove('pulse-animation'), 1000);
            }
        }

        // 生成新数据
        function generateNewData() {
            // 心率变化
            vitalSigns.heartRate += (Math.random() - 0.5) * 3;
            vitalSigns.heartRate = Math.max(68, Math.min(82, vitalSigns.heartRate));

            // 血压变化
            vitalSigns.systolicBP += (Math.random() - 0.5) * 4;
            vitalSigns.systolicBP = Math.max(115, Math.min(130, vitalSigns.systolicBP));
            vitalSigns.diastolicBP += (Math.random() - 0.5) * 3;
            vitalSigns.diastolicBP = Math.max(75, Math.min(85, vitalSigns.diastolicBP));

            // 血氧变化（非常稳定）
            vitalSigns.spo2 += (Math.random() - 0.5) * 0.5;
            vitalSigns.spo2 = Math.max(97, Math.min(99, vitalSigns.spo2));

            // 体温变化（极其稳定）
            vitalSigns.temperature += (Math.random() - 0.5) * 0.1;
            vitalSigns.temperature = Math.max(36.3, Math.min(36.7, vitalSigns.temperature));

            // 呼吸频率变化
            vitalSigns.respiratoryRate += (Math.random() - 0.5) * 1;
            vitalSigns.respiratoryRate = Math.max(15, Math.min(17, vitalSigns.respiratoryRate));

            // 血糖变化（相对稳定）
            vitalSigns.bloodGlucose += (Math.random() - 0.5) * 0.2;
            vitalSigns.bloodGlucose = Math.max(4.8, Math.min(5.6, vitalSigns.bloodGlucose));

            // QT间期变化（与心率相关）
            const hrFactor = (vitalSigns.heartRate - 72) * -1.5;
            vitalSigns.qtInterval = 400 + hrFactor + (Math.random() - 0.5) * 10;
            vitalSigns.qtInterval = Math.max(380, Math.min(420, vitalSigns.qtInterval));

            // 中心静脉压变化
            vitalSigns.cvp += (Math.random() - 0.5) * 0.5;
            vitalSigns.cvp = Math.max(7, Math.min(9, vitalSigns.cvp));
        }

        // 更新数据历史
        function updateDataHistory() {
            const now = new Date().toLocaleTimeString().slice(0, 5);

            dataHistory.timeLabels.push(now);
            dataHistory.heartRate.push(Math.round(vitalSigns.heartRate));
            dataHistory.systolicBP.push(Math.round(vitalSigns.systolicBP));
            dataHistory.spo2.push(Math.round(vitalSigns.spo2));
            dataHistory.temperature.push(parseFloat(vitalSigns.temperature.toFixed(1)));
            dataHistory.respiratoryRate.push(Math.round(vitalSigns.respiratoryRate));
            dataHistory.bloodGlucose.push(parseFloat(vitalSigns.bloodGlucose.toFixed(1)));
            dataHistory.qtInterval.push(Math.round(vitalSigns.qtInterval));
            dataHistory.cvp.push(Math.round(vitalSigns.cvp));

            // 限制数据点数量
            const maxPoints = 30;
            if (dataHistory.timeLabels.length > maxPoints) {
                dataHistory.timeLabels.shift();
                Object.keys(dataHistory).forEach(key => {
                    if (key !== 'timeLabels') {
                        dataHistory[key].shift();
                    }
                });
            }

            updateAllStatistics();
        }

        // 打开详细视图
        function openDetailView(parameterType) {
            currentDetailParameter = parameterType;

            document.getElementById('mainView').classList.add('hidden');
            document.getElementById('detailView').classList.add('active');

            setupDetailView(parameterType);
        }

        // 配置详细视图
        function setupDetailView(parameterType) {
            const parameterConfigs = {
                heartRate: {
                    icon: '💓',
                    title: '心率分析',
                    unit: '次/分',
                    color: '#e74c3c',
                    getCurrentValue: () => Math.round(vitalSigns.heartRate),
                    getDataArray: () => dataHistory.heartRate
                },
                bloodPressure: {
                    icon: '🩸',
                    title: '血压分析',
                    unit: 'mmHg',
                    color: '#8e44ad',
                    getCurrentValue: () => `${Math.round(vitalSigns.systolicBP)}/${Math.round(vitalSigns.diastolicBP)}`,
                    getDataArray: () => dataHistory.systolicBP
                },
                spo2: {
                    icon: '🫁',
                    title: '血氧分析',
                    unit: '%',
                    color: '#3498db',
                    getCurrentValue: () => Math.round(vitalSigns.spo2),
                    getDataArray: () => dataHistory.spo2
                },
                temperature: {
                    icon: '🌡️',
                    title: '体温分析',
                    unit: '°C',
                    color: '#f39c12',
                    getCurrentValue: () => vitalSigns.temperature.toFixed(1),
                    getDataArray: () => dataHistory.temperature
                },
                respiratoryRate: {
                    icon: '🌬️',
                    title: '呼吸分析',
                    unit: '次/分',
                    color: '#27ae60',
                    getCurrentValue: () => Math.round(vitalSigns.respiratoryRate),
                    getDataArray: () => dataHistory.respiratoryRate
                },
                bloodGlucose: {
                    icon: '🍯',
                    title: '血糖分析',
                    unit: 'mmol/L',
                    color: '#e67e22',
                    getCurrentValue: () => vitalSigns.bloodGlucose.toFixed(1),
                    getDataArray: () => dataHistory.bloodGlucose
                },
                ecgRhythm: {
                    icon: '📈',
                    title: '心电分析',
                    unit: 'ms',
                    color: '#34495e',
                    getCurrentValue: () => Math.round(vitalSigns.qtInterval),
                    getDataArray: () => dataHistory.qtInterval
                },
                cvp: {
                    icon: '🔄',
                    title: '静脉压分析',
                    unit: 'mmHg',
                    color: '#9b59b6',
                    getCurrentValue: () => Math.round(vitalSigns.cvp),
                    getDataArray: () => dataHistory.cvp
                }
            };

            const config = parameterConfigs[parameterType];
            if (!config) return;

            // 更新详细视图标题和图标
            document.getElementById('detailIcon').textContent = config.icon;
            document.getElementById('detailIcon').style.color = config.color;
            document.getElementById('detailTitle').textContent = config.title;
            document.getElementById('detailCurrentValue').textContent = `${config.getCurrentValue()} ${config.unit}`;
            document.getElementById('detailCurrentValue').style.color = config.color;
            document.getElementById('waveformTitle').textContent = `${config.title.replace('分析', '')}变化趋势`;

            updateDetailInfo(parameterType, config);
            initializeDetailChart(parameterType, config);
        }

        // 返回主界面
        function backToMain() {
            document.getElementById('mainView').classList.remove('hidden');
            document.getElementById('detailView').classList.remove('active');

            currentDetailParameter = null;

            if (detailChart) {
                detailChart.destroy();
                detailChart = null;
            }
        }

        // 初始化详细图表
        function initializeDetailChart(parameterType, config) {
            if (detailChart) {
                detailChart.destroy();
                detailChart = null;
            }

            const canvas = document.getElementById('detailChart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            detailChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dataHistory.timeLabels,
                    datasets: [{
                        label: config.title.replace('分析', ''),
                        data: config.getDataArray(),
                        borderColor: config.color,
                        backgroundColor: config.color + '15',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 3,
                        pointHoverRadius: 6,
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                font: { size: 14 },
                                usePointStyle: true
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间',
                                font: { size: 14 }
                            },
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: `${config.title.replace('分析', '')} (${config.unit})`,
                                font: { size: 14 }
                            },
                            grid: { color: 'rgba(0,0,0,0.1)' }
                        }
                    },
                    animation: {
                        duration: 800,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }

        // 更新详细视图
        function updateDetailView() {
            if (!currentDetailParameter) return;

            const parameterConfigs = {
                heartRate: {
                    getCurrentValue: () => Math.round(vitalSigns.heartRate),
                    unit: '次/分',
                    getDataArray: () => dataHistory.heartRate
                },
                bloodPressure: {
                    getCurrentValue: () => `${Math.round(vitalSigns.systolicBP)}/${Math.round(vitalSigns.diastolicBP)}`,
                    unit: 'mmHg',
                    getDataArray: () => dataHistory.systolicBP
                },
                spo2: {
                    getCurrentValue: () => Math.round(vitalSigns.spo2),
                    unit: '%',
                    getDataArray: () => dataHistory.spo2
                },
                temperature: {
                    getCurrentValue: () => vitalSigns.temperature.toFixed(1),
                    unit: '°C',
                    getDataArray: () => dataHistory.temperature
                },
                respiratoryRate: {
                    getCurrentValue: () => Math.round(vitalSigns.respiratoryRate),
                    unit: '次/分',
                    getDataArray: () => dataHistory.respiratoryRate
                },
                bloodGlucose: {
                    getCurrentValue: () => vitalSigns.bloodGlucose.toFixed(1),
                    unit: 'mmol/L',
                    getDataArray: () => dataHistory.bloodGlucose
                },
                ecgRhythm: {
                    getCurrentValue: () => Math.round(vitalSigns.qtInterval),
                    unit: 'ms',
                    getDataArray: () => dataHistory.qtInterval
                },
                cvp: {
                    getCurrentValue: () => Math.round(vitalSigns.cvp),
                    unit: 'mmHg',
                    getDataArray: () => dataHistory.cvp
                }
            };

            const config = parameterConfigs[currentDetailParameter];
            if (!config) return;

            // 更新当前值显示
            document.getElementById('detailCurrentValue').textContent = `${config.getCurrentValue()} ${config.unit}`;

            // 更新图表
            if (detailChart) {
                detailChart.data.labels = dataHistory.timeLabels;
                detailChart.data.datasets[0].data = config.getDataArray();
                detailChart.update('none');
            }

            updateDetailInfo(currentDetailParameter, config);
        }

        // 更新详细信息
        function updateDetailInfo(parameterType, config) {
            const stat = statistics[parameterType];

            // 更新当前值
            document.getElementById('currentValueInfo').textContent = config.getCurrentValue();

            if (stat && stat.count > 0) {
                // 更新统计信息
                document.getElementById('averageValueInfo').textContent = (stat.sum / stat.count).toFixed(1);
                document.getElementById('maxValueInfo').textContent = stat.max.toFixed(1);
                document.getElementById('minValueInfo').textContent = stat.min.toFixed(1);

                // 判断状态
                const currentValue = parseFloat(config.getCurrentValue());
                let status = '正常';
                let statusColor = '#4caf50';

                // 根据医学标准判断状态
                switch(parameterType) {
                    case 'heartRate':
                        if (currentValue < 60 || currentValue > 100) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    case 'spo2':
                        if (currentValue < 95) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    case 'temperature':
                        if (currentValue < 36.1 || currentValue > 37.2) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    case 'respiratoryRate':
                        if (currentValue < 12 || currentValue > 20) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    case 'bloodGlucose':
                        if (currentValue < 3.9 || currentValue > 6.1) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    case 'cvp':
                        if (currentValue < 2 || currentValue > 12) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    case 'ecgRhythm':
                        if (currentValue < 380 || currentValue > 420) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    default:
                        // 血压特殊处理
                        if (parameterType === 'bloodPressure') {
                            const systolic = Math.round(vitalSigns.systolicBP);
                            if (systolic < 90 || systolic > 140) {
                                status = '注意';
                                statusColor = '#ff9800';
                            }
                        }
                        break;
                }

                document.getElementById('statusInfo').textContent = status;
                document.getElementById('statusInfo').style.color = statusColor;

                // 判断趋势
                const dataArray = config.getDataArray();
                if (dataArray.length >= 5) {
                    const recent = dataArray.slice(-5);
                    const first = recent[0];
                    const last = recent[recent.length - 1];
                    const diff = last - first;

                    let trend = '稳定';
                    const threshold = parameterType === 'temperature' ? 0.2 :
                                    parameterType === 'bloodGlucose' ? 0.3 : 1;

                    if (Math.abs(diff) > threshold) {
                        trend = diff > 0 ? '上升' : '下降';
                    }

                    document.getElementById('trendInfo').textContent = trend;
                } else {
                    document.getElementById('trendInfo').textContent = '稳定';
                }
            } else {
                // 默认值
                document.getElementById('averageValueInfo').textContent = '--';
                document.getElementById('maxValueInfo').textContent = '--';
                document.getElementById('minValueInfo').textContent = '--';
                document.getElementById('statusInfo').textContent = '正常';
                document.getElementById('statusInfo').style.color = '#4caf50';
                document.getElementById('trendInfo').textContent = '稳定';
            }
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (autoUpdateInterval) {
                clearInterval(autoUpdateInterval);
            }
            if (detailChart) {
                detailChart.destroy();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystem();
            startAutoUpdate();
        });
    </script>
</body>
</html>
