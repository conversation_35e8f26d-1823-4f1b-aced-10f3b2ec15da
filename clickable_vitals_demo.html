<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可点击生理数据演示</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .btn.active {
            background: linear-gradient(45deg, #2e7d32, #4caf50);
        }

        /* 主界面 */
        .main-view {
            display: block;
        }

        .main-view.hidden {
            display: none;
        }

        .vitals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .vital-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .vital-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .vital-card:hover::before {
            left: 100%;
        }

        .vital-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .vital-card::after {
            content: '🔍 点击查看详细波形';
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.9em;
            color: #666;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .vital-card:hover::after {
            opacity: 1;
        }

        .vital-card .icon {
            font-size: 3.5em;
            margin-bottom: 15px;
            display: block;
        }

        .vital-card .value {
            font-size: 2.8em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .vital-card .label {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 10px;
        }

        .vital-card .range {
            color: #999;
            font-size: 0.9em;
        }

        /* 生理参数颜色主题 */
        .heart-rate .icon { color: #e74c3c; }
        .heart-rate .value { color: #e74c3c; }
        .heart-rate:hover { border-left: 5px solid #e74c3c; }

        .blood-pressure .icon { color: #8e44ad; }
        .blood-pressure .value { color: #8e44ad; }
        .blood-pressure:hover { border-left: 5px solid #8e44ad; }

        .spo2 .icon { color: #3498db; }
        .spo2 .value { color: #3498db; }
        .spo2:hover { border-left: 5px solid #3498db; }

        .temperature .icon { color: #f39c12; }
        .temperature .value { color: #f39c12; }
        .temperature:hover { border-left: 5px solid #f39c12; }

        /* 详细视图 */
        .detail-view {
            display: none;
        }

        .detail-view.active {
            display: block;
        }

        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .detail-title {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .detail-title .icon {
            font-size: 3em;
        }

        .detail-title .info h2 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2em;
        }

        .detail-title .info .current-value {
            font-size: 1.8em;
            font-weight: bold;
        }

        .back-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1em;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .waveform-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .waveform-container h3 {
            margin-bottom: 20px;
            color: #333;
            text-align: center;
            font-size: 1.5em;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
        }

        .info-panel {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .info-panel h3 {
            margin-bottom: 20px;
            color: #333;
            text-align: center;
            font-size: 1.5em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .info-item .value {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .info-item .label {
            color: #666;
            font-size: 0.9em;
        }

        .pulse-animation {
            animation: heartbeat 1s infinite;
        }

        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @media (max-width: 768px) {
            .vitals-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .detail-header {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 可点击生理数据演示</h1>
            <p>点击任意生理数据卡片查看详细波形图和实时分析</p>
        </div>

        <div class="control-panel">
            <button class="btn" id="startBtn" onclick="startDemo()">开始演示</button>
            <button class="btn" id="stopBtn" onclick="stopDemo()" disabled>停止演示</button>
            <button class="btn" onclick="resetDemo()">重置数据</button>
        </div>

        <!-- 主界面 -->
        <div class="main-view" id="mainView">
            <div class="vitals-grid">
                <div class="vital-card heart-rate" onclick="showDetailView('heartRate')">
                    <div class="icon">💓</div>
                    <div class="value" id="heartRate">72</div>
                    <div class="label">心率 (次/分)</div>
                    <div class="range">正常范围: 60-100</div>
                </div>
                <div class="vital-card blood-pressure" onclick="showDetailView('bloodPressure')">
                    <div class="icon">🩸</div>
                    <div class="value" id="bloodPressure">120/80</div>
                    <div class="label">血压 (mmHg)</div>
                    <div class="range">正常范围: 90-140/60-90</div>
                </div>
                <div class="vital-card spo2" onclick="showDetailView('spo2')">
                    <div class="icon">🫁</div>
                    <div class="value" id="spo2">98</div>
                    <div class="label">血氧饱和度 (%)</div>
                    <div class="range">正常范围: 95-100</div>
                </div>
                <div class="vital-card temperature" onclick="showDetailView('temperature')">
                    <div class="icon">🌡️</div>
                    <div class="value" id="temperature">36.5</div>
                    <div class="label">体温 (°C)</div>
                    <div class="range">正常范围: 36.1-37.2</div>
                </div>
            </div>
        </div>

        <!-- 详细视图 -->
        <div class="detail-view" id="detailView">
            <div class="detail-header">
                <div class="detail-title">
                    <div class="icon" id="detailIcon">💓</div>
                    <div class="info">
                        <h2 id="detailTitle">心率监测</h2>
                        <div class="current-value" id="detailCurrentValue">72 次/分</div>
                    </div>
                </div>
                <button class="back-btn" onclick="showMainView()">← 返回主界面</button>
            </div>

            <div class="waveform-container">
                <h3 id="waveformTitle">心率实时波形图</h3>
                <div class="chart-wrapper">
                    <canvas id="detailChart"></canvas>
                </div>
            </div>

            <div class="info-panel">
                <h3>实时数据分析</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="value" id="infoCurrentValue">72</div>
                        <div class="label">当前值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="infoAverageValue">74</div>
                        <div class="label">平均值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="infoMaxValue">85</div>
                        <div class="label">最高值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="infoMinValue">65</div>
                        <div class="label">最低值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="infoStatus">正常</div>
                        <div class="label">状态</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="infoTrend">稳定</div>
                        <div class="label">趋势</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简化的演示数据生成器
        class VitalSignsDemo {
            constructor() {
                this.isRunning = false;
                this.interval = null;
                this.currentView = null;
                
                // 基础数据
                this.heartRate = 72;
                this.systolicBP = 120;
                this.diastolicBP = 80;
                this.spo2 = 98;
                this.temperature = 36.5;
                
                // 数据历史
                this.timeLabels = [];
                this.dataHistory = {
                    heartRate: [],
                    systolicBP: [],
                    spo2: [],
                    temperature: []
                };
                
                // 统计数据
                this.stats = {
                    heartRate: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                    systolicBP: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                    spo2: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                    temperature: { sum: 0, count: 0, min: Infinity, max: -Infinity }
                };
                
                this.maxDataPoints = 50;
            }
            
            // 生成模拟数据
            generateData() {
                // 心率变化
                this.heartRate += (Math.random() - 0.5) * 4;
                this.heartRate = Math.max(55, Math.min(105, this.heartRate));
                
                // 血压变化
                this.systolicBP += (Math.random() - 0.5) * 6;
                this.systolicBP = Math.max(90, Math.min(150, this.systolicBP));
                this.diastolicBP += (Math.random() - 0.5) * 4;
                this.diastolicBP = Math.max(60, Math.min(100, this.diastolicBP));
                
                // 血氧变化（相对稳定）
                this.spo2 += (Math.random() - 0.5) * 1;
                this.spo2 = Math.max(95, Math.min(100, this.spo2));
                
                // 体温变化（非常稳定）
                this.temperature += (Math.random() - 0.5) * 0.2;
                this.temperature = Math.max(36.0, Math.min(37.5, this.temperature));
                
                const data = {
                    heartRate: Math.round(this.heartRate),
                    systolicBP: Math.round(this.systolicBP),
                    diastolicBP: Math.round(this.diastolicBP),
                    spo2: Math.round(this.spo2),
                    temperature: parseFloat(this.temperature.toFixed(1))
                };
                
                this.updateStats(data);
                return data;
            }
            
            // 更新统计数据
            updateStats(data) {
                Object.keys(data).forEach(key => {
                    const stat = this.stats[key];
                    if (stat) {
                        stat.sum += data[key];
                        stat.count++;
                        stat.min = Math.min(stat.min, data[key]);
                        stat.max = Math.max(stat.max, data[key]);
                    }
                });
            }
        }
        
        // 全局变量
        let demo = new VitalSignsDemo();
        let detailChart = null;
        
        // 开始演示
        function startDemo() {
            if (demo.isRunning) return;

            demo.isRunning = true;
            document.getElementById('startBtn').disabled = true;
            document.getElementById('startBtn').classList.remove('active');
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('stopBtn').classList.add('active');

            demo.interval = setInterval(() => {
                const data = demo.generateData();
                updateDisplay(data);
                updateCharts(data);

                // 如果在详细视图中，更新详细信息
                if (demo.currentView) {
                    updateDetailView(data);
                }
            }, 1000);
        }

        // 停止演示
        function stopDemo() {
            if (!demo.isRunning) return;

            demo.isRunning = false;
            clearInterval(demo.interval);

            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('stopBtn').classList.remove('active');
        }

        // 重置演示
        function resetDemo() {
            stopDemo();

            // 重置数据
            demo = new VitalSignsDemo();

            // 重置显示
            document.getElementById('heartRate').textContent = '72';
            document.getElementById('bloodPressure').textContent = '120/80';
            document.getElementById('spo2').textContent = '98';
            document.getElementById('temperature').textContent = '36.5';

            // 如果在详细视图中，返回主界面
            if (demo.currentView) {
                showMainView();
            }
        }

        // 更新显示
        function updateDisplay(data) {
            document.getElementById('heartRate').textContent = data.heartRate;
            document.getElementById('bloodPressure').textContent = `${data.systolicBP}/${data.diastolicBP}`;
            document.getElementById('spo2').textContent = data.spo2;
            document.getElementById('temperature').textContent = data.temperature;

            // 添加心跳动画
            const heartRateCard = document.querySelector('.heart-rate');
            heartRateCard.classList.add('pulse-animation');
            setTimeout(() => heartRateCard.classList.remove('pulse-animation'), 1000);
        }

        // 更新图表数据
        function updateCharts(data) {
            const now = new Date().toLocaleTimeString();

            // 添加新数据
            demo.timeLabels.push(now);
            demo.dataHistory.heartRate.push(data.heartRate);
            demo.dataHistory.systolicBP.push(data.systolicBP);
            demo.dataHistory.spo2.push(data.spo2);
            demo.dataHistory.temperature.push(data.temperature);

            // 限制数据点数量
            if (demo.timeLabels.length > demo.maxDataPoints) {
                demo.timeLabels.shift();
                Object.keys(demo.dataHistory).forEach(key => {
                    demo.dataHistory[key].shift();
                });
            }
        }

        // 显示详细视图
        function showDetailView(parameterType) {
            demo.currentView = parameterType;

            // 隐藏主界面，显示详细视图
            document.getElementById('mainView').classList.add('hidden');
            document.getElementById('detailView').classList.add('active');

            // 配置详细视图内容
            const parameterConfig = {
                heartRate: {
                    icon: '💓',
                    title: '心率监测',
                    unit: '次/分',
                    color: '#e74c3c',
                    getValue: () => demo.heartRate,
                    getDataArray: () => demo.dataHistory.heartRate
                },
                bloodPressure: {
                    icon: '🩸',
                    title: '血压监测',
                    unit: 'mmHg',
                    color: '#8e44ad',
                    getValue: () => `${Math.round(demo.systolicBP)}/${Math.round(demo.diastolicBP)}`,
                    getDataArray: () => demo.dataHistory.systolicBP
                },
                spo2: {
                    icon: '🫁',
                    title: '血氧监测',
                    unit: '%',
                    color: '#3498db',
                    getValue: () => Math.round(demo.spo2),
                    getDataArray: () => demo.dataHistory.spo2
                },
                temperature: {
                    icon: '🌡️',
                    title: '体温监测',
                    unit: '°C',
                    color: '#f39c12',
                    getValue: () => demo.temperature.toFixed(1),
                    getDataArray: () => demo.dataHistory.temperature
                }
            };

            const config = parameterConfig[parameterType];
            if (!config) return;

            // 更新详细视图标题和图标
            document.getElementById('detailIcon').textContent = config.icon;
            document.getElementById('detailIcon').style.color = config.color;
            document.getElementById('detailTitle').textContent = config.title;
            document.getElementById('detailCurrentValue').textContent = `${config.getValue()} ${config.unit}`;
            document.getElementById('detailCurrentValue').style.color = config.color;
            document.getElementById('waveformTitle').textContent = `${config.title}实时波形图`;

            // 初始化详细图表
            initDetailChart(parameterType, config);

            // 更新信息面板
            updateInfoPanel(parameterType, config);
        }

        // 显示主界面
        function showMainView() {
            document.getElementById('mainView').classList.remove('hidden');
            document.getElementById('detailView').classList.remove('active');
            demo.currentView = null;

            // 销毁详细图表
            if (detailChart) {
                detailChart.destroy();
                detailChart = null;
            }
        }

        // 初始化详细图表
        function initDetailChart(parameterType, config) {
            if (detailChart) {
                detailChart.destroy();
            }

            const ctx = document.getElementById('detailChart').getContext('2d');
            detailChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: demo.timeLabels,
                    datasets: [{
                        label: config.title,
                        data: config.getDataArray(),
                        borderColor: config.color,
                        backgroundColor: config.color + '20',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 4,
                        pointHoverRadius: 8,
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                font: {
                                    size: 14
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间',
                                font: {
                                    size: 14
                                }
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: `${config.title} (${config.unit})`,
                                font: {
                                    size: 14
                                }
                            }
                        }
                    },
                    animation: {
                        duration: 300
                    }
                }
            });
        }

        // 更新详细视图
        function updateDetailView(data) {
            if (!demo.currentView || !detailChart) return;

            const parameterConfig = {
                heartRate: { getValue: () => data.heartRate, unit: '次/分' },
                bloodPressure: { getValue: () => `${data.systolicBP}/${data.diastolicBP}`, unit: 'mmHg' },
                spo2: { getValue: () => data.spo2, unit: '%' },
                temperature: { getValue: () => data.temperature, unit: '°C' }
            };

            const config = parameterConfig[demo.currentView];
            if (!config) return;

            // 更新当前值显示
            document.getElementById('detailCurrentValue').textContent = `${config.getValue()} ${config.unit}`;

            // 更新详细图表
            detailChart.data.labels = demo.timeLabels;
            detailChart.data.datasets[0].data = demo.dataHistory[demo.currentView] || [];
            detailChart.update('none');

            // 更新信息面板
            updateInfoPanel(demo.currentView, config);
        }

        // 更新信息面板
        function updateInfoPanel(parameterType, config) {
            const stat = demo.stats[parameterType];

            if (stat && stat.count > 0) {
                document.getElementById('infoCurrentValue').textContent = config.getValue();
                document.getElementById('infoAverageValue').textContent = (stat.sum / stat.count).toFixed(1);
                document.getElementById('infoMaxValue').textContent = stat.max;
                document.getElementById('infoMinValue').textContent = stat.min;

                // 简单的状态判断
                const current = parseFloat(config.getValue());
                let status = '正常';
                if (parameterType === 'heartRate' && (current < 60 || current > 100)) status = '异常';
                if (parameterType === 'spo2' && current < 95) status = '异常';
                if (parameterType === 'temperature' && (current < 36.1 || current > 37.2)) status = '异常';

                document.getElementById('infoStatus').textContent = status;
                document.getElementById('infoStatus').style.color = status === '正常' ? '#4caf50' : '#f44336';

                // 简单的趋势判断
                const dataArray = demo.dataHistory[parameterType] || [];
                if (dataArray.length >= 3) {
                    const recent = dataArray.slice(-3);
                    const trend = recent[recent.length - 1] > recent[0] ? '上升' :
                                 recent[recent.length - 1] < recent[0] ? '下降' : '稳定';
                    document.getElementById('infoTrend').textContent = trend;
                } else {
                    document.getElementById('infoTrend').textContent = '数据不足';
                }
            } else {
                document.getElementById('infoCurrentValue').textContent = config.getValue();
                document.getElementById('infoAverageValue').textContent = '--';
                document.getElementById('infoMaxValue').textContent = '--';
                document.getElementById('infoMinValue').textContent = '--';
                document.getElementById('infoStatus').textContent = '正常';
                document.getElementById('infoStatus').style.color = '#4caf50';
                document.getElementById('infoTrend').textContent = '数据不足';
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            console.log('可点击生理数据演示系统已加载');
            console.log('点击任意生理数据卡片查看详细波形图');
        });
    </script>
</body>
</html>
