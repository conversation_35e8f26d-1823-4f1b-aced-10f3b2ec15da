# Air780e合宙4G通信模块M100P接线配置说明

## 📱 模块概述

Air780e是合宙推出的4G Cat.1通信模块，M100P是其DTU（数据透传单元）固件版本，可以实现串口数据与网络数据的透明传输，非常适合医疗监测设备的联网需求。

## 🔌 硬件接线方案

### 基本引脚定义
```
Air780e M100P 主要引脚：
┌─────────────────────────────────────┐
│ 引脚号 │ 引脚名称 │ 功能描述        │
├─────────────────────────────────────┤
│   1    │   GND    │ 电源地          │
│   2    │   VCC    │ 电源正(3.3-4.2V)│
│   3    │   RST    │ 复位引脚(低有效) │
│   4    │   BOOT   │ 启动模式选择     │
│   5    │   TXD    │ 串口发送         │
│   6    │   RXD    │ 串口接收         │
│   7    │   RTS    │ 串口流控(可选)   │
│   8    │   CTS    │ 串口流控(可选)   │
│   9    │   STATUS │ 状态指示         │
│  10    │   WAKEUP │ 唤醒引脚         │
└─────────────────────────────────────┘
```

### 与CH32V307VCT6的接线方案

#### 方案一：基础连接（推荐）
```
CH32V307VCT6        Air780e M100P
┌─────────────┐    ┌─────────────┐
│    3.3V     │────│    VCC      │ 电源正
│    GND      │────│    GND      │ 电源地
│    PA9(TX1) │────│    RXD      │ 串口接收
│    PA10(RX1)│────│    TXD      │ 串口发送
│    PC13     │────│    RST      │ 复位控制
│    PC14     │────│    STATUS   │ 状态监测
└─────────────┘    └─────────────┘
```

#### 方案二：完整连接（带流控）
```
CH32V307VCT6        Air780e M100P
┌─────────────┐    ┌─────────────┐
│    3.3V     │────│    VCC      │ 电源正
│    GND      │────│    GND      │ 电源地
│    PA9(TX1) │────│    RXD      │ 串口接收
│    PA10(RX1)│────│    TXD      │ 串口发送
│    PA11(CTS)│────│    RTS      │ 流控信号
│    PA12(RTS)│────│    CTS      │ 流控信号
│    PC13     │────│    RST      │ 复位控制
│    PC14     │────│    STATUS   │ 状态监测
│    PC15     │────│    WAKEUP   │ 唤醒控制
└─────────────┘    └─────────────┘
```

## ⚡ 电源设计

### 电源要求
- **工作电压**: 3.3V - 4.2V
- **工作电流**: 
  - 待机: 1-3mA
  - 通信: 200-800mA（峰值可达2A）
- **推荐方案**: 使用3.7V锂电池或3.3V稳压电源

### 电源电路设计
```
电池/外部电源 → LDO稳压器 → 滤波电容 → Air780e
                    ↓
               电源指示LED
```

### 推荐电源电路
```
VIN(5V) ──┬── AMS1117-3.3 ──┬── 100uF ──┬── Air780e VCC
          │                  │           │
          └── 1000uF         └── 10uF    └── 1uF
          
GND ──────┴──────────────────┴───────────┴── Air780e GND
```

## 📡 天线连接

### 天线要求
- **频段**: 支持4G全网通（移动/联通/电信）
- **接口**: IPEX/U.FL接口
- **增益**: 2-5dBi
- **类型**: 全向天线或定向天线

### 天线安装注意事项
1. 天线应远离金属遮挡物
2. 天线接地良好
3. 天线馈线长度适中（建议<20cm）
4. 避免与其他射频器件干扰

## 🔧 DTU固件配置

### 串口参数配置
```
波特率: 115200 bps（默认）
数据位: 8
停止位: 1
校验位: 无
流控制: 无（或硬件流控）
```

### AT命令配置示例
```bash
# 1. 查询模块信息
AT+GMI          # 查询厂商信息
AT+GMM          # 查询模块型号
AT+GMR          # 查询固件版本

# 2. 网络配置
AT+CGDCONT=1,"IP","CMNET"    # 设置APN（移动）
AT+CGDCONT=1,"IP","3GNET"    # 设置APN（联通）
AT+CGDCONT=1,"IP","CTNET"    # 设置APN（电信）

# 3. DTU模式配置
AT+NETOPEN=1,"TCP","服务器IP",端口号    # 建立TCP连接
AT+NETOPEN=1,"UDP","服务器IP",端口号    # 建立UDP连接

# 4. 透传模式
AT+ENTM         # 进入透传模式
+++             # 退出透传模式（需要1秒间隔）
```

## 💻 CH32V307代码实现

### 串口初始化代码
```c
#include "ch32v30x.h"

// 串口1初始化（连接Air780e）
void UART1_Init(u32 baudrate) {
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    
    // 使能时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_USART1, ENABLE);
    
    // 配置TX引脚
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 配置RX引脚
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 串口配置
    USART_InitStructure.USART_BaudRate = baudrate;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    
    USART_Init(USART1, &USART_InitStructure);
    USART_Cmd(USART1, ENABLE);
}

// 发送数据到Air780e
void Air780e_SendData(uint8_t *data, uint16_t len) {
    for(uint16_t i = 0; i < len; i++) {
        while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);
        USART_SendData(USART1, data[i]);
    }
}

// 从Air780e接收数据
uint8_t Air780e_ReceiveData(void) {
    while(USART_GetFlagStatus(USART1, USART_FLAG_RXNE) == RESET);
    return USART_ReceiveData(USART1);
}
```

### 4G模块控制代码
```c
// Air780e控制引脚定义
#define AIR780E_RST_PIN     GPIO_Pin_13
#define AIR780E_RST_PORT    GPIOC
#define AIR780E_STATUS_PIN  GPIO_Pin_14
#define AIR780E_STATUS_PORT GPIOC

// 初始化控制引脚
void Air780e_GPIO_Init(void) {
    GPIO_InitTypeDef GPIO_InitStructure;
    
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);
    
    // RST引脚配置（输出）
    GPIO_InitStructure.GPIO_Pin = AIR780E_RST_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(AIR780E_RST_PORT, &GPIO_InitStructure);
    
    // STATUS引脚配置（输入）
    GPIO_InitStructure.GPIO_Pin = AIR780E_STATUS_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_Init(AIR780E_STATUS_PORT, &GPIO_InitStructure);
    
    // 初始状态
    GPIO_SetBits(AIR780E_RST_PORT, AIR780E_RST_PIN);
}

// 复位Air780e模块
void Air780e_Reset(void) {
    GPIO_ResetBits(AIR780E_RST_PORT, AIR780E_RST_PIN);
    Delay_Ms(100);
    GPIO_SetBits(AIR780E_RST_PORT, AIR780E_RST_PIN);
    Delay_Ms(3000); // 等待模块启动
}

// 检查模块状态
uint8_t Air780e_GetStatus(void) {
    return GPIO_ReadInputDataBit(AIR780E_STATUS_PORT, AIR780E_STATUS_PIN);
}
```

### 医疗数据发送示例
```c
// 医疗数据结构
typedef struct {
    uint16_t heartRate;      // 心率
    uint16_t systolicBP;     // 收缩压
    uint16_t diastolicBP;    // 舒张压
    uint8_t  spo2;          // 血氧
    float    temperature;    // 体温
    float    bloodGlucose;   // 血糖
    uint32_t timestamp;      // 时间戳
} MedicalData_t;

// 发送医疗数据到云端
void SendMedicalDataToCloud(MedicalData_t *data) {
    char jsonBuffer[256];
    
    // 构造JSON数据
    sprintf(jsonBuffer, 
        "{"
        "\"heartRate\":%d,"
        "\"systolicBP\":%d,"
        "\"diastolicBP\":%d,"
        "\"spo2\":%d,"
        "\"temperature\":%.1f,"
        "\"bloodGlucose\":%.1f,"
        "\"timestamp\":%lu,"
        "\"deviceId\":\"MEDICAL_001\""
        "}\r\n",
        data->heartRate,
        data->systolicBP,
        data->diastolicBP,
        data->spo2,
        data->temperature,
        data->bloodGlucose,
        data->timestamp
    );
    
    // 通过Air780e发送到云端
    Air780e_SendData((uint8_t*)jsonBuffer, strlen(jsonBuffer));
}
```

## 🌐 网络配置

### 服务器端配置
```javascript
// Node.js WebSocket服务器示例
const WebSocket = require('ws');
const net = require('net');

// TCP服务器（用于接收Air780e数据）
const tcpServer = net.createServer((socket) => {
    console.log('Air780e设备连接:', socket.remoteAddress);
    
    socket.on('data', (data) => {
        console.log('接收到医疗数据:', data.toString());
        
        // 解析JSON数据
        try {
            const medicalData = JSON.parse(data.toString());
            // 转发到WebSocket客户端
            broadcastToWebClients(medicalData);
        } catch (error) {
            console.error('数据解析错误:', error);
        }
    });
    
    socket.on('close', () => {
        console.log('Air780e设备断开连接');
    });
});

tcpServer.listen(8081, () => {
    console.log('TCP服务器监听端口 8081');
});
```

### APN配置说明
```
中国移动: CMNET 或 CMIOT
中国联通: 3GNET 或 UNINET
中国电信: CTNET 或 CTLTE

配置命令:
AT+CGDCONT=1,"IP","APN名称"
```

## 🔍 调试与测试

### 串口调试工具
1. **推荐工具**: 串口助手、Putty、Tera Term
2. **调试步骤**:
   - 连接串口
   - 发送AT命令测试
   - 检查网络注册状态
   - 测试数据传输

### 常用AT命令
```bash
AT                    # 测试通信
AT+CPIN?             # 检查SIM卡状态
AT+CSQ               # 检查信号强度
AT+CREG?             # 检查网络注册状态
AT+CGATT?            # 检查GPRS附着状态
AT+CIPSTATUS         # 检查连接状态
```

## ⚠️ 注意事项

### 硬件注意事项
1. **电源稳定**: 确保电源纹波小于100mV
2. **天线匹配**: 使用50Ω阻抗匹配的天线
3. **接地良好**: 模块和天线都需要良好接地
4. **EMC设计**: 注意电磁兼容性设计

### 软件注意事项
1. **AT命令间隔**: AT命令之间需要适当延时
2. **数据缓冲**: 接收数据时注意缓冲区管理
3. **错误处理**: 完善的网络异常处理机制
4. **功耗管理**: 合理使用休眠和唤醒功能

## 📋 物料清单

### 必需器件
- Air780e M100P模块 × 1
- 4G全网通天线 × 1
- SIM卡 × 1
- 3.3V稳压器(AMS1117) × 1
- 电容(100uF, 10uF, 1uF) × 各2个
- 连接线若干

### 可选器件
- LED指示灯 × 2（电源、状态）
- 按键开关 × 1（复位）
- 保险丝 × 1（电源保护）

---

**配置完成后，您的医疗监测设备就可以通过4G网络实时上传数据到云端服务器了！**
