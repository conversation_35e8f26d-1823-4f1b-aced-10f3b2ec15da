# 医疗监测系统配置说明

## 📊 系统模式

### 正常模式（默认）
- **特点**: 所有数据稳定，无异常警报
- **适用**: 日常监测、实际使用场景
- **异常概率**: 0%（完全禁用异常数据生成）
- **启用方法**: 在浏览器控制台输入 `enableNormalMode()`

### 演示模式
- **特点**: 偶尔生成血糖轻微异常用于展示报警功能
- **异常类型**: 仅限血糖轻微超出正常范围（6.2-7.0 或 3.5-3.8 mmol/L）
- **其他参数**: 始终保持正常范围
- **异常概率**: 5%（每20次更新约1次血糖异常）
- **启用方法**: 在浏览器控制台输入 `enableDemoMode()`

## 🎯 数据稳定性优化

### 正常状态下的数据范围：

| 参数 | 稳定范围 | 变化幅度 |
|------|----------|----------|
| 心率 | 70-78 次/分 | ±1次/分 |
| 收缩压 | 118-125 mmHg | ±1.5mmHg |
| 舒张压 | 78-82 mmHg | ±1mmHg |
| 血氧饱和度 | 97.5-98.5% | ±0.15% |
| 体温 | 36.4-36.6°C | ±0.025°C |
| 呼吸频率 | 15.5-16.5 次/分 | ±0.25次/分 |
| 血糖 | 5.1-5.3 mmol/L | ±0.05mmol/L |
| QT间期 | 395-405 ms | ±2.5ms |
| 中心静脉压 | 7.5-8.5 mmHg | ±0.25mmHg |

## 🛠️ 控制台命令

### 基本配置命令
```javascript
// 启用正常模式（推荐日常使用）
enableNormalMode()

// 启用演示模式（用于功能展示）
enableDemoMode()

// 查看当前配置
getSystemConfig()

// 自定义异常概率（0-1之间的数值）
setAbnormalProbability(0.01)  // 设置为1%
```

### 测试命令
```javascript
// 血糖异常测试（推荐使用）
testGlucoseAbnormal('high')       // 血糖轻微偏高
testGlucoseAbnormal('low')        // 血糖轻微偏低

// 传统测试命令（现已调整为血糖异常）
testAbnormalData('bloodGlucose')  // 血糖异常
testAbnormalData('glucose')       // 血糖异常
testAbnormalData('lowGlucose')    // 血糖偏低

// 其他测试命令（现在只会生成血糖异常）
testAbnormalData('heartRate')     // 已调整为血糖异常
testAbnormalData('bloodPressure') // 已调整为血糖异常
testAbnormalData('spo2')          // 已调整为血糖异常
testAbnormalData('temperature')   // 已调整为血糖异常

// 清除所有报警
clearAlerts()

// 查看报警历史
getAlertHistory()
```

## 📈 使用建议

### 日常监测
1. 使用 `enableNormalMode()` 确保数据稳定
2. 系统会显示正常的生理波动
3. 不会出现异常报警（除非手动触发测试）

### 系统演示
1. 使用 `enableDemoMode()` 启用演示功能
2. 系统会偶尔生成异常数据展示报警功能
3. 可以使用测试命令手动触发特定异常

### 功能测试
1. 使用 `testGlucoseAbnormal('high')` 或 `testGlucoseAbnormal('low')` 测试血糖异常报警
2. 使用 `clearAlerts()` 清除测试产生的报警
3. 使用 `getAlertHistory()` 查看报警记录
4. 所有其他参数始终保持正常范围，确保系统稳定性

## 🔧 技术细节

### 数据生成逻辑
- **正常模式**: 所有数据保持在正常范围内，变化幅度很小
- **演示模式**: 仅生成血糖轻微异常，其他参数保持正常
- **异常范围**: 血糖偏高 6.2-7.0 mmol/L，血糖偏低 3.5-3.8 mmol/L
- **历史数据**: 同样遵循稳定性原则，确保波形图平滑

### 异常检测阈值
系统使用两级阈值：
- **警告级**: 超出正常范围但未达到危险水平
- **危险级**: 超出临界值，需要立即关注

### 性能优化
- 数据更新频率: 3秒/次
- 历史数据保留: 最近50个数据点
- 界面刷新: 实时更新，无延迟

## 💡 常见问题

**Q: 为什么默认不生成异常数据？**
A: 符合实际医疗监测场景，正常情况下患者数据应该稳定。

**Q: 为什么只允许血糖轻微异常？**
A: 这样既能演示报警功能，又保持系统的整体稳定性，符合实际医疗场景。

**Q: 如何快速测试报警功能？**
A: 使用 `testGlucoseAbnormal('high')` 或 `testGlucoseAbnormal('low')` 测试血糖异常。

**Q: 其他生理参数会出现异常吗？**
A: 不会，所有其他参数（心率、血压、血氧、体温等）始终保持正常范围。

**Q: 如何恢复到出厂设置？**
A: 刷新页面即可恢复默认配置。
