/**
 * CH32V307 + Air780e 医疗数据上传示例
 * 功能：采集生理数据并通过Air780e 4G模块上传到云端服务器
 * 
 * 硬件连接：
 * CH32V307    Air780e M100P
 * PA9(TX1) -> RXD
 * PA10(RX1) -> TXD
 * PC13 -> RST
 * PC14 -> STATUS
 * 3.3V -> VCC
 * GND -> GND
 */

#include "ch32v30x.h"
#include <stdio.h>
#include <string.h>

// Air780e控制引脚定义
#define AIR780E_RST_PIN     GPIO_Pin_13
#define AIR780E_RST_PORT    GPIOC
#define AIR780E_STATUS_PIN  GPIO_Pin_14
#define AIR780E_STATUS_PORT GPIOC

// 医疗数据结构
typedef struct {
    uint16_t heartRate;      // 心率 (次/分)
    uint16_t systolicBP;     // 收缩压 (mmHg)
    uint16_t diastolicBP;    // 舒张压 (mmHg)
    uint8_t  spo2;          // 血氧饱和度 (%)
    float    temperature;    // 体温 (°C)
    float    bloodGlucose;   // 血糖 (mmol/L)
    uint16_t respiratoryRate; // 呼吸频率 (次/分)
    uint16_t qtInterval;     // QT间期 (ms)
    uint8_t  cvp;           // 中心静脉压 (mmHg)
    uint32_t timestamp;      // 时间戳
} MedicalData_t;

// 全局变量
volatile uint8_t uart1_rx_buffer[256];
volatile uint8_t uart1_rx_index = 0;
volatile uint8_t uart1_rx_complete = 0;

MedicalData_t currentMedicalData = {0};

// 函数声明
void SystemInit_Custom(void);
void GPIO_Init_Custom(void);
void UART1_Init(uint32_t baudrate);
void Air780e_Init(void);
void Air780e_Reset(void);
uint8_t Air780e_SendATCommand(const char* cmd, const char* expected_response, uint32_t timeout);
void Air780e_SendData(const uint8_t *data, uint16_t len);
void CollectMedicalData(MedicalData_t *data);
void SendMedicalDataToCloud(MedicalData_t *data);
void Delay_Ms(uint32_t ms);

/**
 * 主函数
 */
int main(void) {
    // 系统初始化
    SystemInit_Custom();
    GPIO_Init_Custom();
    UART1_Init(115200);
    
    printf("🏥 CH32V307 医疗监测系统启动\r\n");
    printf("📱 正在初始化Air780e模块...\r\n");
    
    // 初始化Air780e模块
    Air780e_Init();
    
    printf("✅ 系统初始化完成\r\n");
    printf("📊 开始采集和上传医疗数据...\r\n");
    
    uint32_t lastUploadTime = 0;
    const uint32_t UPLOAD_INTERVAL = 5000; // 5秒上传一次
    
    while(1) {
        uint32_t currentTime = SysTick->CNT; // 简化的时间获取
        
        // 定期采集和上传数据
        if(currentTime - lastUploadTime >= UPLOAD_INTERVAL) {
            // 采集医疗数据
            CollectMedicalData(&currentMedicalData);
            
            // 上传到云端
            SendMedicalDataToCloud(&currentMedicalData);
            
            lastUploadTime = currentTime;
            
            printf("📤 数据上传完成 - 心率:%d, 血压:%d/%d, 血氧:%d%%\r\n", 
                   currentMedicalData.heartRate,
                   currentMedicalData.systolicBP,
                   currentMedicalData.diastolicBP,
                   currentMedicalData.spo2);
        }
        
        Delay_Ms(100);
    }
}

/**
 * 系统初始化
 */
void SystemInit_Custom(void) {
    // 使能时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA | RCC_APB2Periph_GPIOC | 
                          RCC_APB2Periph_USART1, ENABLE);
}

/**
 * GPIO初始化
 */
void GPIO_Init_Custom(void) {
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // Air780e控制引脚初始化
    // RST引脚 (PC13) - 输出
    GPIO_InitStructure.GPIO_Pin = AIR780E_RST_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(AIR780E_RST_PORT, &GPIO_InitStructure);
    
    // STATUS引脚 (PC14) - 输入
    GPIO_InitStructure.GPIO_Pin = AIR780E_STATUS_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_Init(AIR780E_STATUS_PORT, &GPIO_InitStructure);
    
    // 初始状态
    GPIO_SetBits(AIR780E_RST_PORT, AIR780E_RST_PIN);
}

/**
 * UART1初始化 (连接Air780e)
 */
void UART1_Init(uint32_t baudrate) {
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // TX引脚配置 (PA9)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // RX引脚配置 (PA10)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // UART配置
    USART_InitStructure.USART_BaudRate = baudrate;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    
    USART_Init(USART1, &USART_InitStructure);
    
    // 使能接收中断
    USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);
    
    // 中断配置
    NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    USART_Cmd(USART1, ENABLE);
}

/**
 * Air780e模块初始化
 */
void Air780e_Init(void) {
    printf("🔄 复位Air780e模块...\r\n");
    Air780e_Reset();
    
    printf("📡 配置网络连接...\r\n");
    
    // 基本AT命令测试
    if(!Air780e_SendATCommand("AT\r\n", "OK", 3000)) {
        printf("❌ Air780e通信失败\r\n");
        return;
    }
    
    // 设置APN (根据运营商调整)
    Air780e_SendATCommand("AT+CGDCONT=1,\"IP\",\"CMNET\"\r\n", "OK", 5000);
    
    // 建立TCP连接到服务器
    char connect_cmd[100];
    sprintf(connect_cmd, "AT+NETOPEN=1,\"TCP\",\"你的服务器IP\",8081\r\n");
    
    if(Air780e_SendATCommand(connect_cmd, "CONNECT OK", 10000)) {
        printf("✅ 服务器连接成功\r\n");
    } else {
        printf("❌ 服务器连接失败\r\n");
    }
    
    // 进入透传模式
    Air780e_SendATCommand("AT+ENTM\r\n", "OK", 3000);
    printf("✅ 进入透传模式\r\n");
}

/**
 * 复位Air780e模块
 */
void Air780e_Reset(void) {
    GPIO_ResetBits(AIR780E_RST_PORT, AIR780E_RST_PIN);
    Delay_Ms(100);
    GPIO_SetBits(AIR780E_RST_PORT, AIR780E_RST_PIN);
    Delay_Ms(3000); // 等待模块启动
}

/**
 * 发送AT命令并等待响应
 */
uint8_t Air780e_SendATCommand(const char* cmd, const char* expected_response, uint32_t timeout) {
    // 清空接收缓冲区
    uart1_rx_index = 0;
    uart1_rx_complete = 0;
    memset((void*)uart1_rx_buffer, 0, sizeof(uart1_rx_buffer));
    
    // 发送命令
    Air780e_SendData((uint8_t*)cmd, strlen(cmd));
    
    // 等待响应
    uint32_t start_time = SysTick->CNT;
    while((SysTick->CNT - start_time) < timeout) {
        if(uart1_rx_complete) {
            if(strstr((char*)uart1_rx_buffer, expected_response) != NULL) {
                return 1; // 成功
            }
            uart1_rx_complete = 0;
            uart1_rx_index = 0;
        }
        Delay_Ms(10);
    }
    
    return 0; // 超时失败
}

/**
 * 向Air780e发送数据
 */
void Air780e_SendData(const uint8_t *data, uint16_t len) {
    for(uint16_t i = 0; i < len; i++) {
        while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);
        USART_SendData(USART1, data[i]);
    }
}

/**
 * 采集医疗数据 (模拟数据，实际应用中连接真实传感器)
 */
void CollectMedicalData(MedicalData_t *data) {
    // 模拟生理数据采集
    data->heartRate = 70 + (rand() % 20);           // 70-90 次/分
    data->systolicBP = 110 + (rand() % 30);         // 110-140 mmHg
    data->diastolicBP = 70 + (rand() % 20);         // 70-90 mmHg
    data->spo2 = 95 + (rand() % 5);                 // 95-100%
    data->temperature = 36.0 + (rand() % 20) / 10.0; // 36.0-38.0°C
    data->bloodGlucose = 4.5 + (rand() % 30) / 10.0; // 4.5-7.5 mmol/L
    data->respiratoryRate = 12 + (rand() % 8);       // 12-20 次/分
    data->qtInterval = 380 + (rand() % 40);          // 380-420 ms
    data->cvp = 6 + (rand() % 6);                    // 6-12 mmHg
    data->timestamp = SysTick->CNT;                  // 简化的时间戳
}

/**
 * 发送医疗数据到云端服务器
 */
void SendMedicalDataToCloud(MedicalData_t *data) {
    char jsonBuffer[512];
    
    // 构造JSON格式数据
    sprintf(jsonBuffer, 
        "{"
        "\"heartRate\":%d,"
        "\"systolicBP\":%d,"
        "\"diastolicBP\":%d,"
        "\"spo2\":%d,"
        "\"temperature\":%.1f,"
        "\"bloodGlucose\":%.1f,"
        "\"respiratoryRate\":%d,"
        "\"qtInterval\":%d,"
        "\"cvp\":%d,"
        "\"timestamp\":%lu,"
        "\"deviceId\":\"CH32V307_001\","
        "\"source\":\"Air780e\""
        "}\r\n",
        data->heartRate,
        data->systolicBP,
        data->diastolicBP,
        data->spo2,
        data->temperature,
        data->bloodGlucose,
        data->respiratoryRate,
        data->qtInterval,
        data->cvp,
        data->timestamp
    );
    
    // 通过Air780e发送到云端
    Air780e_SendData((uint8_t*)jsonBuffer, strlen(jsonBuffer));
}

/**
 * UART1中断服务函数
 */
void USART1_IRQHandler(void) {
    if(USART_GetITStatus(USART1, USART_IT_RXNE) != RESET) {
        uint8_t received_data = USART_ReceiveData(USART1);
        
        if(uart1_rx_index < sizeof(uart1_rx_buffer) - 1) {
            uart1_rx_buffer[uart1_rx_index++] = received_data;
            
            // 检查是否接收完成 (以\r\n结尾)
            if(received_data == '\n' && uart1_rx_index > 1 && 
               uart1_rx_buffer[uart1_rx_index-2] == '\r') {
                uart1_rx_complete = 1;
            }
        }
        
        USART_ClearITPendingBit(USART1, USART_IT_RXNE);
    }
}

/**
 * 延时函数 (毫秒)
 */
void Delay_Ms(uint32_t ms) {
    uint32_t i, j;
    for(i = 0; i < ms; i++) {
        for(j = 0; j < 8000; j++); // 根据系统时钟调整
    }
}

/**
 * printf重定向到UART (用于调试)
 */
int fputc(int ch, FILE *f) {
    while(USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);
    USART_SendData(USART1, (uint8_t)ch);
    return ch;
}
