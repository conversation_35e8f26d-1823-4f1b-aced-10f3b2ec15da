<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可用的交互式医学监测系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .btn.active {
            background: linear-gradient(45deg, #2e7d32, #4caf50);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        /* 主界面 */
        .main-view {
            display: block;
        }

        .main-view.hidden {
            display: none;
        }

        .vitals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .vital-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .vital-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .vital-card:hover::before {
            left: 100%;
        }

        .vital-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .vital-card::after {
            content: '🔍 点击查看详细波形';
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.9em;
            color: #666;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .vital-card:hover::after {
            opacity: 1;
        }

        .vital-card .icon {
            font-size: 4em;
            margin-bottom: 15px;
            display: block;
        }

        .vital-card .value {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .vital-card .label {
            color: #666;
            font-size: 1.3em;
            margin-bottom: 10px;
        }

        .vital-card .range {
            color: #999;
            font-size: 1em;
        }

        /* 生理参数颜色主题 */
        .heart-rate .icon { color: #e74c3c; }
        .heart-rate .value { color: #e74c3c; }
        .heart-rate:hover { border-left: 5px solid #e74c3c; }

        .blood-pressure .icon { color: #8e44ad; }
        .blood-pressure .value { color: #8e44ad; }
        .blood-pressure:hover { border-left: 5px solid #8e44ad; }

        .spo2 .icon { color: #3498db; }
        .spo2 .value { color: #3498db; }
        .spo2:hover { border-left: 5px solid #3498db; }

        .temperature .icon { color: #f39c12; }
        .temperature .value { color: #f39c12; }
        .temperature:hover { border-left: 5px solid #f39c12; }

        /* 详细视图 */
        .detail-view {
            display: none;
        }

        .detail-view.active {
            display: block;
        }

        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .detail-title {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .detail-title .icon {
            font-size: 3em;
        }

        .detail-title .info h2 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2em;
        }

        .detail-title .info .current-value {
            font-size: 1.8em;
            font-weight: bold;
        }

        .back-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1em;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .waveform-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .waveform-container h3 {
            margin-bottom: 20px;
            color: #333;
            text-align: center;
            font-size: 1.5em;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
        }

        .info-panel {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .info-panel h3 {
            margin-bottom: 20px;
            color: #333;
            text-align: center;
            font-size: 1.5em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .info-item .value {
            font-size: 1.8em;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .info-item .label {
            color: #666;
            font-size: 1em;
        }

        .pulse-animation {
            animation: heartbeat 1s infinite;
        }

        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .status-info {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.1em;
            color: #1976d2;
        }

        @media (max-width: 768px) {
            .vitals-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .detail-header {
                flex-direction: column;
                gap: 20px;
            }
            
            .info-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 可用的交互式医学监测系统</h1>
            <p>点击任意生理数据卡片查看详细波形图和实时分析</p>
        </div>

        <div class="status-info" id="statusInfo">
            系统已就绪 - 显示实时生理数据，点击卡片查看详细波形
        </div>

        <div class="control-panel">
            <button class="btn" id="startBtn" onclick="startMonitoring()">开始监测</button>
            <button class="btn" id="stopBtn" onclick="stopMonitoring()" disabled>停止监测</button>
            <button class="btn" onclick="resetSystem()">重置系统</button>
            <button class="btn" onclick="showDebugInfo()">调试信息</button>
        </div>

        <!-- 主界面 -->
        <div class="main-view" id="mainView">
            <div class="vitals-grid">
                <div class="vital-card heart-rate" onclick="openDetailView('heartRate')">
                    <div class="icon">💓</div>
                    <div class="value" id="heartRateValue">72</div>
                    <div class="label">心率 (次/分)</div>
                    <div class="range">正常范围: 60-100</div>
                </div>
                <div class="vital-card blood-pressure" onclick="openDetailView('bloodPressure')">
                    <div class="icon">🩸</div>
                    <div class="value" id="bloodPressureValue">120/80</div>
                    <div class="label">血压 (mmHg)</div>
                    <div class="range">正常范围: 90-140/60-90</div>
                </div>
                <div class="vital-card spo2" onclick="openDetailView('spo2')">
                    <div class="icon">🫁</div>
                    <div class="value" id="spo2Value">98</div>
                    <div class="label">血氧饱和度 (%)</div>
                    <div class="range">正常范围: 95-100</div>
                </div>
                <div class="vital-card temperature" onclick="openDetailView('temperature')">
                    <div class="icon">🌡️</div>
                    <div class="value" id="temperatureValue">36.5</div>
                    <div class="label">体温 (°C)</div>
                    <div class="range">正常范围: 36.1-37.2</div>
                </div>
            </div>
        </div>

        <!-- 详细视图 -->
        <div class="detail-view" id="detailView">
            <div class="detail-header">
                <div class="detail-title">
                    <div class="icon" id="detailIcon">💓</div>
                    <div class="info">
                        <h2 id="detailTitle">心率监测</h2>
                        <div class="current-value" id="detailCurrentValue">72 次/分</div>
                    </div>
                </div>
                <button class="back-btn" onclick="backToMain()">← 返回主界面</button>
            </div>

            <div class="waveform-container">
                <h3 id="waveformTitle">心率实时波形图</h3>
                <div class="chart-wrapper">
                    <canvas id="detailChart"></canvas>
                </div>
            </div>

            <div class="info-panel">
                <h3>实时数据分析</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="value" id="currentValueInfo">72</div>
                        <div class="label">当前值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="averageValueInfo">--</div>
                        <div class="label">平均值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="maxValueInfo">--</div>
                        <div class="label">最高值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="minValueInfo">--</div>
                        <div class="label">最低值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="statusInfo">正常</div>
                        <div class="label">状态</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="trendInfo">稳定</div>
                        <div class="label">趋势</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('🏥 可用的交互式医学监测系统开始加载...');
        
        // 全局变量
        let isMonitoring = false;
        let monitoringInterval = null;
        let currentDetailParameter = null;
        let detailChart = null;
        
        // 生理数据
        let vitalSigns = {
            heartRate: 72,
            systolicBP: 120,
            diastolicBP: 80,
            spo2: 98,
            temperature: 36.5
        };
        
        // 数据历史
        let dataHistory = {
            heartRate: [],
            systolicBP: [],
            spo2: [],
            temperature: [],
            timeLabels: []
        };
        
        // 统计数据
        let statistics = {
            heartRate: { sum: 0, count: 0, min: Infinity, max: -Infinity },
            systolicBP: { sum: 0, count: 0, min: Infinity, max: -Infinity },
            spo2: { sum: 0, count: 0, min: Infinity, max: -Infinity },
            temperature: { sum: 0, count: 0, min: Infinity, max: -Infinity }
        };
        
        // 初始化系统
        function initializeSystem() {
            console.log('🔧 初始化系统...');
            updateDisplay();
            updateStatusInfo('系统已就绪 - 显示实时生理数据，点击卡片查看详细波形');
            console.log('✅ 系统初始化完成');
        }

        // 更新显示
        function updateDisplay() {
            console.log('📊 更新显示数据...');

            // 更新主界面数值
            document.getElementById('heartRateValue').textContent = Math.round(vitalSigns.heartRate);
            document.getElementById('bloodPressureValue').textContent = `${Math.round(vitalSigns.systolicBP)}/${Math.round(vitalSigns.diastolicBP)}`;
            document.getElementById('spo2Value').textContent = Math.round(vitalSigns.spo2);
            document.getElementById('temperatureValue').textContent = vitalSigns.temperature.toFixed(1);

            // 添加心跳动画
            const heartCard = document.querySelector('.heart-rate');
            if (heartCard) {
                heartCard.classList.add('pulse-animation');
                setTimeout(() => heartCard.classList.remove('pulse-animation'), 1000);
            }

            console.log('✅ 显示数据已更新:', vitalSigns);
        }

        // 更新状态信息
        function updateStatusInfo(message) {
            const statusElement = document.getElementById('statusInfo');
            if (statusElement) {
                statusElement.textContent = message;
            }
            console.log('📢 状态更新:', message);
        }

        // 开始监测
        function startMonitoring() {
            console.log('🚀 开始监测...');

            if (isMonitoring) {
                console.log('⚠️ 监测已在运行中');
                return;
            }

            isMonitoring = true;
            document.getElementById('startBtn').disabled = true;
            document.getElementById('startBtn').classList.remove('active');
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('stopBtn').classList.add('active');

            updateStatusInfo('监测中 - 数据实时更新，点击卡片查看详细分析');

            // 开始数据模拟
            monitoringInterval = setInterval(() => {
                generateNewData();
                updateDisplay();
                updateDataHistory();

                // 如果在详细视图中，更新详细信息
                if (currentDetailParameter) {
                    updateDetailView();
                }
            }, 1000);

            console.log('✅ 监测已开始');
        }

        // 停止监测
        function stopMonitoring() {
            console.log('⏹️ 停止监测...');

            if (!isMonitoring) {
                console.log('⚠️ 监测未在运行');
                return;
            }

            isMonitoring = false;
            clearInterval(monitoringInterval);

            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('stopBtn').classList.remove('active');

            updateStatusInfo('监测已停止 - 点击开始监测继续数据采集');

            console.log('✅ 监测已停止');
        }

        // 重置系统
        function resetSystem() {
            console.log('🔄 重置系统...');

            stopMonitoring();

            // 重置数据
            vitalSigns = {
                heartRate: 72,
                systolicBP: 120,
                diastolicBP: 80,
                spo2: 98,
                temperature: 36.5
            };

            // 清空历史数据
            dataHistory = {
                heartRate: [],
                systolicBP: [],
                spo2: [],
                temperature: [],
                timeLabels: []
            };

            // 重置统计数据
            statistics = {
                heartRate: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                systolicBP: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                spo2: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                temperature: { sum: 0, count: 0, min: Infinity, max: -Infinity }
            };

            // 如果在详细视图中，返回主界面
            if (currentDetailParameter) {
                backToMain();
            }

            updateDisplay();
            updateStatusInfo('系统已重置 - 所有数据恢复到初始状态');

            console.log('✅ 系统重置完成');
        }

        // 显示调试信息
        function showDebugInfo() {
            console.log('🐛 调试信息:');
            console.log('- 监测状态:', isMonitoring);
            console.log('- 当前详细参数:', currentDetailParameter);
            console.log('- 生理数据:', vitalSigns);
            console.log('- 数据历史长度:', {
                heartRate: dataHistory.heartRate.length,
                systolicBP: dataHistory.systolicBP.length,
                spo2: dataHistory.spo2.length,
                temperature: dataHistory.temperature.length
            });

            alert('调试信息已输出到控制台，请按F12查看');
        }

        // 生成新数据
        function generateNewData() {
            // 心率变化
            vitalSigns.heartRate += (Math.random() - 0.5) * 4;
            vitalSigns.heartRate = Math.max(55, Math.min(105, vitalSigns.heartRate));

            // 血压变化
            vitalSigns.systolicBP += (Math.random() - 0.5) * 6;
            vitalSigns.systolicBP = Math.max(90, Math.min(150, vitalSigns.systolicBP));
            vitalSigns.diastolicBP += (Math.random() - 0.5) * 4;
            vitalSigns.diastolicBP = Math.max(60, Math.min(100, vitalSigns.diastolicBP));

            // 血氧变化（相对稳定）
            vitalSigns.spo2 += (Math.random() - 0.5) * 1;
            vitalSigns.spo2 = Math.max(95, Math.min(100, vitalSigns.spo2));

            // 体温变化（非常稳定）
            vitalSigns.temperature += (Math.random() - 0.5) * 0.2;
            vitalSigns.temperature = Math.max(36.0, Math.min(37.5, vitalSigns.temperature));

            // 更新统计数据
            updateStatistics();
        }

        // 更新统计数据
        function updateStatistics() {
            Object.keys(vitalSigns).forEach(key => {
                if (key !== 'diastolicBP') { // diastolicBP 不单独统计
                    const stat = statistics[key];
                    const value = vitalSigns[key];

                    stat.sum += value;
                    stat.count++;
                    stat.min = Math.min(stat.min, value);
                    stat.max = Math.max(stat.max, value);
                }
            });
        }

        // 更新数据历史
        function updateDataHistory() {
            const now = new Date().toLocaleTimeString();

            dataHistory.timeLabels.push(now);
            dataHistory.heartRate.push(Math.round(vitalSigns.heartRate));
            dataHistory.systolicBP.push(Math.round(vitalSigns.systolicBP));
            dataHistory.spo2.push(Math.round(vitalSigns.spo2));
            dataHistory.temperature.push(parseFloat(vitalSigns.temperature.toFixed(1)));

            // 限制数据点数量
            const maxPoints = 50;
            if (dataHistory.timeLabels.length > maxPoints) {
                dataHistory.timeLabels.shift();
                dataHistory.heartRate.shift();
                dataHistory.systolicBP.shift();
                dataHistory.spo2.shift();
                dataHistory.temperature.shift();
            }
        }

        // 打开详细视图
        function openDetailView(parameterType) {
            console.log('🔍 打开详细视图:', parameterType);

            if (!parameterType) {
                console.error('❌ 参数类型为空');
                alert('错误：参数类型为空');
                return;
            }

            currentDetailParameter = parameterType;

            // 隐藏主界面，显示详细视图
            document.getElementById('mainView').classList.add('hidden');
            document.getElementById('detailView').classList.add('active');

            // 配置详细视图
            setupDetailView(parameterType);

            console.log('✅ 详细视图已打开');
        }

        // 配置详细视图
        function setupDetailView(parameterType) {
            console.log('⚙️ 配置详细视图:', parameterType);

            const parameterConfigs = {
                heartRate: {
                    icon: '💓',
                    title: '心率监测',
                    unit: '次/分',
                    color: '#e74c3c',
                    normalRange: '60-100',
                    getCurrentValue: () => Math.round(vitalSigns.heartRate),
                    getDataArray: () => dataHistory.heartRate
                },
                bloodPressure: {
                    icon: '🩸',
                    title: '血压监测',
                    unit: 'mmHg',
                    color: '#8e44ad',
                    normalRange: '90-140/60-90',
                    getCurrentValue: () => `${Math.round(vitalSigns.systolicBP)}/${Math.round(vitalSigns.diastolicBP)}`,
                    getDataArray: () => dataHistory.systolicBP
                },
                spo2: {
                    icon: '🫁',
                    title: '血氧监测',
                    unit: '%',
                    color: '#3498db',
                    normalRange: '95-100',
                    getCurrentValue: () => Math.round(vitalSigns.spo2),
                    getDataArray: () => dataHistory.spo2
                },
                temperature: {
                    icon: '🌡️',
                    title: '体温监测',
                    unit: '°C',
                    color: '#f39c12',
                    normalRange: '36.1-37.2',
                    getCurrentValue: () => vitalSigns.temperature.toFixed(1),
                    getDataArray: () => dataHistory.temperature
                }
            };

            const config = parameterConfigs[parameterType];
            if (!config) {
                console.error('❌ 未找到参数配置:', parameterType);
                alert('错误：未找到参数配置');
                return;
            }

            // 更新详细视图标题和图标
            document.getElementById('detailIcon').textContent = config.icon;
            document.getElementById('detailIcon').style.color = config.color;
            document.getElementById('detailTitle').textContent = config.title;
            document.getElementById('detailCurrentValue').textContent = `${config.getCurrentValue()} ${config.unit}`;
            document.getElementById('detailCurrentValue').style.color = config.color;
            document.getElementById('waveformTitle').textContent = `${config.title}实时波形图`;

            // 更新信息面板
            updateDetailInfo(parameterType, config);

            // 初始化图表
            initializeDetailChart(parameterType, config);

            console.log('✅ 详细视图配置完成');
        }

        // 返回主界面
        function backToMain() {
            console.log('🔙 返回主界面');

            // 显示主界面，隐藏详细视图
            document.getElementById('mainView').classList.remove('hidden');
            document.getElementById('detailView').classList.remove('active');

            // 清理详细视图状态
            currentDetailParameter = null;

            // 销毁图表
            if (detailChart) {
                detailChart.destroy();
                detailChart = null;
                console.log('🗑️ 详细图表已销毁');
            }

            console.log('✅ 已返回主界面');
        }

        // 初始化详细图表
        function initializeDetailChart(parameterType, config) {
            console.log('📈 初始化详细图表:', parameterType);

            // 销毁旧图表
            if (detailChart) {
                detailChart.destroy();
                detailChart = null;
            }

            // 获取画布元素
            const canvas = document.getElementById('detailChart');
            if (!canvas) {
                console.error('❌ 未找到图表画布元素');
                return;
            }

            const ctx = canvas.getContext('2d');

            // 创建新图表
            detailChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dataHistory.timeLabels,
                    datasets: [{
                        label: config.title,
                        data: config.getDataArray(),
                        borderColor: config.color,
                        backgroundColor: config.color + '20',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 4,
                        pointHoverRadius: 8,
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                font: {
                                    size: 14
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间',
                                font: {
                                    size: 14
                                }
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: `${config.title} (${config.unit})`,
                                font: {
                                    size: 14
                                }
                            }
                        }
                    },
                    animation: {
                        duration: 300
                    }
                }
            });

            console.log('✅ 详细图表初始化完成');
        }

        // 更新详细视图
        function updateDetailView() {
            if (!currentDetailParameter) return;

            const parameterConfigs = {
                heartRate: {
                    getCurrentValue: () => Math.round(vitalSigns.heartRate),
                    unit: '次/分',
                    getDataArray: () => dataHistory.heartRate
                },
                bloodPressure: {
                    getCurrentValue: () => `${Math.round(vitalSigns.systolicBP)}/${Math.round(vitalSigns.diastolicBP)}`,
                    unit: 'mmHg',
                    getDataArray: () => dataHistory.systolicBP
                },
                spo2: {
                    getCurrentValue: () => Math.round(vitalSigns.spo2),
                    unit: '%',
                    getDataArray: () => dataHistory.spo2
                },
                temperature: {
                    getCurrentValue: () => vitalSigns.temperature.toFixed(1),
                    unit: '°C',
                    getDataArray: () => dataHistory.temperature
                }
            };

            const config = parameterConfigs[currentDetailParameter];
            if (!config) return;

            // 更新当前值显示
            document.getElementById('detailCurrentValue').textContent = `${config.getCurrentValue()} ${config.unit}`;

            // 更新图表
            if (detailChart) {
                detailChart.data.labels = dataHistory.timeLabels;
                detailChart.data.datasets[0].data = config.getDataArray();
                detailChart.update('none');
            }

            // 更新信息面板
            updateDetailInfo(currentDetailParameter, config);
        }

        // 更新详细信息
        function updateDetailInfo(parameterType, config) {
            const stat = statistics[parameterType];

            // 更新当前值
            document.getElementById('currentValueInfo').textContent = config.getCurrentValue();

            if (stat && stat.count > 0) {
                // 更新统计信息
                document.getElementById('averageValueInfo').textContent = (stat.sum / stat.count).toFixed(1);
                document.getElementById('maxValueInfo').textContent = stat.max.toFixed(1);
                document.getElementById('minValueInfo').textContent = stat.min.toFixed(1);

                // 判断状态
                const currentValue = parseFloat(config.getCurrentValue());
                let status = '正常';
                let statusColor = '#4caf50';

                if (parameterType === 'heartRate' && (currentValue < 60 || currentValue > 100)) {
                    status = '异常';
                    statusColor = '#f44336';
                }
                if (parameterType === 'spo2' && currentValue < 95) {
                    status = '异常';
                    statusColor = '#f44336';
                }
                if (parameterType === 'temperature' && (currentValue < 36.1 || currentValue > 37.2)) {
                    status = '异常';
                    statusColor = '#f44336';
                }

                document.getElementById('statusInfo').textContent = status;
                document.getElementById('statusInfo').style.color = statusColor;

                // 判断趋势
                const dataArray = config.getDataArray();
                if (dataArray.length >= 3) {
                    const recent = dataArray.slice(-3);
                    const trend = recent[recent.length - 1] > recent[0] ? '上升' :
                                 recent[recent.length - 1] < recent[0] ? '下降' : '稳定';
                    document.getElementById('trendInfo').textContent = trend;
                } else {
                    document.getElementById('trendInfo').textContent = '数据不足';
                }
            } else {
                // 没有统计数据时的默认值
                document.getElementById('averageValueInfo').textContent = '--';
                document.getElementById('maxValueInfo').textContent = '--';
                document.getElementById('minValueInfo').textContent = '--';
                document.getElementById('statusInfo').textContent = '正常';
                document.getElementById('statusInfo').style.color = '#4caf50';
                document.getElementById('trendInfo').textContent = '数据不足';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ 页面DOM加载完成');
            initializeSystem();
        });
    </script>
</body>
</html>
