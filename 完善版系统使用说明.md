# 智能医疗监测系统 - 完善版使用说明

## 🎯 系统概述

这是一个功能完善的智能医疗监测系统，集成了页面跳转、病人信息管理、历史记录查询等核心功能。

## 📋 主要功能

### 🔄 **页面跳转功能**
- **导航面包屑**: 显示当前页面位置
- **六大功能模块**: 短期检测、长期监测、设备连接、数据分析、病人管理、历史记录
- **快捷键支持**: Ctrl+1-6 快速切换模式

### 👤 **病人信息管理**
- **当前病人信息显示**: 头像、姓名、编号、年龄、性别、病房、主治医生
- **病人信息编辑**: 完整的病人资料编辑表单
- **病人列表管理**: 支持多病人切换和状态显示
- **病人搜索**: 实时搜索病人姓名或编号
- **数据导出**: 导出病人数据为JSON格式

### 📊 **历史记录功能**
- **数据查询**: 按时间范围和数据类型查询
- **历史图表**: 动态显示历史数据趋势
- **医疗报告**: 自动生成专业医疗报告
- **报告导出**: 支持打印和HTML格式导出

### 🔗 **硬件连接**
- **WebSocket连接**: 实时网络通信
- **串口连接**: 直接硬件连接
- **蓝牙连接**: 无线设备连接
- **模拟模式**: 演示和测试模式

### 📈 **数据监测**
- **实时监测**: 心率、血压、血氧、体温、血糖、呼吸频率
- **波形显示**: 心电图和生理参数趋势图
- **智能预警**: 异常数据自动报警
- **状态指示**: 正常/注意/异常三级预警

## 🚀 使用方法

### 启动系统
```bash
# 方法1: 直接双击打开
完善版智能医疗监测系统.html

# 方法2: Web服务器方式（推荐）
npx http-server -p 8080
# 访问: http://localhost:8080/完善版智能医疗监测系统.html
```

### 基本操作流程

#### 1. **病人管理**
1. 点击"👤 病人管理"模式
2. 查看当前病人信息
3. 点击"编辑信息"修改病人资料
4. 点击"切换病人"选择其他病人
5. 点击"新增病人"添加新病人

#### 2. **开始监测**
1. 选择监测模式（短期/长期）
2. 配置硬件连接（可选）
3. 点击"开始监测"
4. 观察实时数据和波形图
5. 注意异常预警提示

#### 3. **查看历史**
1. 点击"📋 历史记录"模式
2. 设置查询时间范围
3. 选择数据类型
4. 点击"查询数据"
5. 查看历史趋势图

#### 4. **生成报告**
1. 在历史记录页面点击"生成报告"
2. 查看完整医疗报告
3. 选择"打印报告"或"导出PDF"
4. 保存或分享报告

## ⌨️ 快捷键说明

| 快捷键 | 功能 |
|--------|------|
| Ctrl+1 | 切换到短期检测 |
| Ctrl+2 | 切换到长期监测 |
| Ctrl+3 | 切换到设备连接 |
| Ctrl+4 | 切换到数据分析 |
| Ctrl+5 | 切换到病人管理 |
| Ctrl+6 | 切换到历史记录 |
| Ctrl+S | 导出数据 |
| Ctrl+R | 重置系统 |
| Ctrl+E | 编辑病人信息 |
| Ctrl+P | 生成报告 |
| F11 | 全屏切换 |
| Esc | 关闭模态框 |

## 🎨 界面说明

### 主界面布局
```
┌─────────────────────────────────────┐
│           系统标题和说明             │
├─────────────────────────────────────┤
│           导航面包屑                 │
├─────────────────────────────────────┤
│           模式选择按钮               │
├─────────────────────────────────────┤
│           功能控制面板               │
├─────────────────────────────────────┤
│           生理参数显示               │
├─────────────────────────────────────┤
│           实时图表显示               │
├─────────────────────────────────────┤
│           系统日志面板               │
└─────────────────────────────────────┘
```

### 病人管理界面
- **左侧**: 当前病人信息和操作按钮
- **右侧**: 病人列表和管理功能

### 历史记录界面
- **上方**: 查询条件设置
- **下方**: 历史数据图表显示

## 📱 移动端适配

系统完全支持移动设备：
- **响应式布局**: 自动适配不同屏幕尺寸
- **触摸优化**: 按钮和交互元素适合触摸操作
- **手势支持**: 支持滑动和缩放操作

## 🔧 高级功能

### 数据导出格式
- **JSON格式**: 完整的结构化数据
- **HTML报告**: 可打印的医疗报告
- **图表数据**: 支持图表数据导出

### 自定义配置
- **监测参数**: 可调整正常值范围
- **预警阈值**: 自定义异常报警条件
- **显示设置**: 个性化界面配置

### 数据安全
- **本地存储**: 数据保存在浏览器本地
- **隐私保护**: 不上传敏感医疗数据
- **备份恢复**: 支持数据备份和恢复

## 🚨 注意事项

### 使用建议
1. **定期保存**: 重要数据及时导出备份
2. **浏览器兼容**: 推荐使用Chrome、Firefox、Edge
3. **网络连接**: WebSocket功能需要网络支持
4. **设备权限**: 串口和蓝牙需要浏览器权限

### 故障排除
1. **页面无法打开**: 检查文件路径和浏览器版本
2. **数据不更新**: 检查监测状态和连接状态
3. **图表不显示**: 确保Chart.js库正常加载
4. **模态框异常**: 刷新页面重新初始化

## 📞 技术支持

### 系统要求
- **浏览器**: Chrome 89+, Firefox 87+, Edge 88+
- **分辨率**: 最低1024x768，推荐1920x1080
- **内存**: 建议4GB以上RAM
- **存储**: 至少100MB可用空间

### 功能特色
- ✅ **完整的病人信息管理系统**
- ✅ **多页面跳转和导航功能**
- ✅ **专业的医疗报告生成**
- ✅ **历史数据查询和分析**
- ✅ **实时硬件连接支持**
- ✅ **响应式移动端适配**
- ✅ **丰富的快捷键支持**
- ✅ **数据导出和备份功能**

### 版本信息
- **版本**: v2.0 完善版
- **更新日期**: 2024年12月
- **开发框架**: 原生HTML5 + JavaScript + Chart.js
- **兼容性**: 现代浏览器全面支持

---

**© 2024 智能医疗监测系统 | 完善版功能说明**
