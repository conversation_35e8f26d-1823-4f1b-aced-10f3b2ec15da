<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式医学生理监测系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            color: #333;
            font-size: 2.2em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .btn.active {
            background: linear-gradient(45deg, #2e7d32, #4caf50);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        /* 主界面样式 */
        .main-view {
            display: block;
        }

        .main-view.hidden {
            display: none;
        }

        .vital-signs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .vital-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }

        .vital-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        }

        .vital-card::after {
            content: '点击查看详细波形';
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8em;
            color: #999;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .vital-card:hover::after {
            opacity: 1;
        }

        .vital-card .icon {
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .vital-card .value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .vital-card .label {
            color: #666;
            font-size: 1em;
            margin-bottom: 8px;
        }

        .vital-card .range {
            color: #999;
            font-size: 0.8em;
        }

        .vital-card .status {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
        }

        .vital-card.abnormal .status {
            background: #f44336;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        /* 各生理参数的颜色主题 */
        .heart-rate .icon { color: #e74c3c; }
        .heart-rate .value { color: #e74c3c; }

        .blood-pressure .icon { color: #8e44ad; }
        .blood-pressure .value { color: #8e44ad; }

        .spo2 .icon { color: #3498db; }
        .spo2 .value { color: #3498db; }

        .temperature .icon { color: #f39c12; }
        .temperature .value { color: #f39c12; }

        .respiratory-rate .icon { color: #27ae60; }
        .respiratory-rate .value { color: #27ae60; }

        .glucose .icon { color: #e67e22; }
        .glucose .value { color: #e67e22; }

        .ecg .icon { color: #34495e; }
        .ecg .value { color: #34495e; }

        .stress-index .icon { color: #9b59b6; }
        .stress-index .value { color: #9b59b6; }

        /* 详细视图样式 */
        .detail-view {
            display: none;
        }

        .detail-view.active {
            display: block;
        }

        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .detail-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .detail-title .icon {
            font-size: 2em;
        }

        .detail-title .info h2 {
            color: #333;
            margin-bottom: 5px;
        }

        .detail-title .info .current-value {
            font-size: 1.5em;
            font-weight: bold;
        }

        .back-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .detail-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .waveform-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .waveform-container h3 {
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
        }

        .parameter-info {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .parameter-info h3 {
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #666;
            font-size: 0.9em;
        }

        .info-value {
            font-weight: bold;
            color: #333;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-indicator.active {
            background: #4caf50;
        }

        .status-indicator.inactive {
            background: #ccc;
            animation: none;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pulse-animation {
            animation: heartbeat 1s infinite;
        }

        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .status-bar {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .mini-charts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .mini-chart {
            background: white;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .mini-chart h4 {
            margin-bottom: 10px;
            color: #333;
            text-align: center;
            font-size: 1em;
        }

        .mini-chart-wrapper {
            position: relative;
            height: 150px;
        }

        @media (max-width: 768px) {
            .vital-signs-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .detail-content {
                grid-template-columns: 1fr;
            }
            
            .mini-charts {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 交互式医学生理监测系统</h1>
            <p>点击生理数据卡片查看详细波形图和分析</p>
        </div>

        <div class="status-bar">
            <div>
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">系统已就绪</span>
            </div>
            <div>
                <span>数据点: <strong id="dataCount">0</strong></span>
                <span style="margin-left: 20px;">运行时间: <strong id="runTime">00:00</strong></span>
            </div>
        </div>

        <div class="control-panel">
            <button class="btn" id="startBtn" onclick="startSimulation()">开始监测</button>
            <button class="btn" id="stopBtn" onclick="stopSimulation()" disabled>停止监测</button>
            <button class="btn" onclick="resetData()">重置数据</button>
            <button class="btn" onclick="exportData()">导出数据</button>
        </div>

        <!-- 主界面 -->
        <div class="main-view" id="mainView">
            <div class="vital-signs-grid">
                <div class="vital-card heart-rate" onclick="showDetailView('heartRate')">
                    <div class="status"></div>
                    <div class="icon">💓</div>
                    <div class="value" id="heartRate">--</div>
                    <div class="label">心率 (次/分)</div>
                    <div class="range">正常: 60-100</div>
                </div>
                <div class="vital-card blood-pressure" onclick="showDetailView('bloodPressure')">
                    <div class="status"></div>
                    <div class="icon">🩸</div>
                    <div class="value" id="bloodPressure">--/--</div>
                    <div class="label">血压 (mmHg)</div>
                    <div class="range">正常: 90-140/60-90</div>
                </div>
                <div class="vital-card spo2" onclick="showDetailView('spo2')">
                    <div class="status"></div>
                    <div class="icon">🫁</div>
                    <div class="value" id="spo2">--</div>
                    <div class="label">血氧饱和度 (%)</div>
                    <div class="range">正常: 95-100</div>
                </div>
                <div class="vital-card temperature" onclick="showDetailView('temperature')">
                    <div class="status"></div>
                    <div class="icon">🌡️</div>
                    <div class="value" id="temperature">--</div>
                    <div class="label">体温 (°C)</div>
                    <div class="range">正常: 36.1-37.2</div>
                </div>
                <div class="vital-card respiratory-rate" onclick="showDetailView('respiratoryRate')">
                    <div class="status"></div>
                    <div class="icon">🌬️</div>
                    <div class="value" id="respiratoryRate">--</div>
                    <div class="label">呼吸频率 (次/分)</div>
                    <div class="range">正常: 12-20</div>
                </div>
                <div class="vital-card glucose" onclick="showDetailView('glucose')">
                    <div class="status"></div>
                    <div class="icon">🍯</div>
                    <div class="value" id="glucose">--</div>
                    <div class="label">血糖 (mmol/L)</div>
                    <div class="range">正常: 3.9-6.1</div>
                </div>
                <div class="vital-card ecg" onclick="showDetailView('qtInterval')">
                    <div class="status"></div>
                    <div class="icon">📈</div>
                    <div class="value" id="ecgQT">--</div>
                    <div class="label">QT间期 (ms)</div>
                    <div class="range">正常: 350-450</div>
                </div>
                <div class="vital-card stress-index" onclick="showDetailView('stressIndex')">
                    <div class="status"></div>
                    <div class="icon">🧠</div>
                    <div class="value" id="stressIndex">--</div>
                    <div class="label">压力指数</div>
                    <div class="range">正常: 0-30</div>
                </div>
            </div>

            <div class="mini-charts">
                <div class="mini-chart">
                    <h4>💓 心率趋势</h4>
                    <div class="mini-chart-wrapper">
                        <canvas id="miniHeartRateChart"></canvas>
                    </div>
                </div>
                <div class="mini-chart">
                    <h4>🩸 血压趋势</h4>
                    <div class="mini-chart-wrapper">
                        <canvas id="miniBloodPressureChart"></canvas>
                    </div>
                </div>
                <div class="mini-chart">
                    <h4>🌡️ 体温趋势</h4>
                    <div class="mini-chart-wrapper">
                        <canvas id="miniTemperatureChart"></canvas>
                    </div>
                </div>
                <div class="mini-chart">
                    <h4>🫁 血氧趋势</h4>
                    <div class="mini-chart-wrapper">
                        <canvas id="miniSpO2Chart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细视图模板 -->
        <div class="detail-view" id="detailView">
            <div class="detail-header">
                <div class="detail-title">
                    <div class="icon" id="detailIcon">💓</div>
                    <div class="info">
                        <h2 id="detailTitle">心率监测</h2>
                        <div class="current-value" id="detailCurrentValue">72 次/分</div>
                    </div>
                </div>
                <button class="back-btn" onclick="showMainView()">← 返回主界面</button>
            </div>

            <div class="detail-content">
                <div class="waveform-container">
                    <h3 id="waveformTitle">心率波形图</h3>
                    <div class="chart-wrapper">
                        <canvas id="detailChart"></canvas>
                    </div>
                </div>

                <div class="parameter-info">
                    <h3>参数信息</h3>
                    <div class="info-item">
                        <span class="info-label">当前值</span>
                        <span class="info-value" id="infoCurrentValue">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">正常范围</span>
                        <span class="info-value" id="infoNormalRange">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">平均值</span>
                        <span class="info-value" id="infoAverageValue">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">最高值</span>
                        <span class="info-value" id="infoMaxValue">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">最低值</span>
                        <span class="info-value" id="infoMinValue">--</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">状态</span>
                        <span class="info-value" id="infoStatus">正常</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">变化趋势</span>
                        <span class="info-value" id="infoTrend">稳定</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">数据点数</span>
                        <span class="info-value" id="infoDataPoints">--</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 交互式医学生理数据模拟器
        class InteractiveMedicalSimulator {
            constructor() {
                this.isRunning = false;
                this.interval = null;
                this.startTime = null;
                this.dataCount = 0;
                
                // 基础生理参数
                this.heartRate = 72;
                this.systolicBP = 120;
                this.diastolicBP = 80;
                this.spo2 = 98;
                this.temperature = 36.5;
                this.respiratoryRate = 16;
                this.glucose = 5.0;
                this.qtInterval = 400;
                this.stressIndex = 15;
                
                // 生理状态影响因子
                this.stressLevel = 0;
                this.activityLevel = 0;
                this.fatigueLevel = 0;
                this.timeOfDay = 12;
                this.physiologicalState = 'normal';
                
                // 数据存储
                this.maxDataPoints = 100;
                this.timeLabels = [];
                this.dataHistory = {
                    heartRate: [],
                    systolicBP: [],
                    diastolicBP: [],
                    spo2: [],
                    temperature: [],
                    respiratoryRate: [],
                    glucose: [],
                    qtInterval: [],
                    stressIndex: []
                };
                
                // 统计数据
                this.stats = {};
                this.resetStats();
                
                // 随机数种子
                this.randomSeed = 12345;
                
                // 当前详细视图
                this.currentDetailView = null;
                
                this.initCharts();
            }
        }
        
        // 全局变量
        let simulator = new InteractiveMedicalSimulator();
        let charts = {};
        let detailChart = null;
        
            // 简单随机数生成器
            simpleRandom() {
                this.randomSeed = (this.randomSeed * ********** + 12345) & 0x7FFFFFFF;
                return this.randomSeed;
            }

            // 获取正态分布随机数
            getNormalRandom(mean, stdDev) {
                if (this.spare !== undefined) {
                    const temp = this.spare;
                    delete this.spare;
                    return temp * stdDev + mean;
                }

                const u = Math.random();
                const v = Math.random();
                const mag = stdDev * Math.sqrt(-2.0 * Math.log(u));
                this.spare = mag * Math.cos(2.0 * Math.PI * v);
                return mag * Math.sin(2.0 * Math.PI * v) + mean;
            }

            // 更新生理状态
            updatePhysiologicalState() {
                this.timeOfDay = (this.timeOfDay + 0.0167) % 24;

                if (this.timeOfDay >= 22 || this.timeOfDay <= 6) {
                    this.physiologicalState = 'sleep';
                    this.stressLevel = Math.max(0, this.stressLevel - 0.1);
                    this.activityLevel = 0;
                    this.fatigueLevel = Math.max(0, this.fatigueLevel - 0.05);
                } else if (this.timeOfDay >= 7 && this.timeOfDay <= 9) {
                    this.physiologicalState = 'active';
                    this.activityLevel = Math.min(10, this.activityLevel + 0.2);
                } else {
                    this.physiologicalState = 'normal';
                    this.stressLevel = Math.max(0, this.stressLevel - 0.05);
                    this.activityLevel = Math.max(0, this.activityLevel - 0.1);
                }

                if (Math.random() < 0.02) {
                    this.stressLevel = Math.min(10, this.stressLevel + Math.random() * 3);
                    this.physiologicalState = 'stress';
                }
            }

            // 生成完整的生理数据
            generateVitalSigns() {
                this.updatePhysiologicalState();

                // 心率模拟
                let baseHR = 72;
                switch (this.physiologicalState) {
                    case 'sleep': baseHR = 55; break;
                    case 'normal': baseHR = 72; break;
                    case 'active': baseHR = 85; break;
                    case 'stress': baseHR = 95; break;
                }
                baseHR += this.stressLevel * 2 + this.activityLevel * 1.5 - this.fatigueLevel * 0.5;
                this.heartRate = Math.round(Math.max(45, Math.min(150, baseHR + this.getNormalRandom(0, 2))));

                // 血压模拟
                let baseSystolic = 120, baseDiastolic = 80;
                const circadianFactor = Math.sin((this.timeOfDay - 6) * Math.PI / 12) * 5;
                baseSystolic += circadianFactor;
                baseDiastolic += circadianFactor * 0.6;

                switch (this.physiologicalState) {
                    case 'sleep': baseSystolic -= 10; baseDiastolic -= 5; break;
                    case 'stress': baseSystolic += 15; baseDiastolic += 10; break;
                    case 'active': baseSystolic += 8; baseDiastolic += 5; break;
                }

                this.systolicBP = Math.round(Math.max(90, Math.min(180, baseSystolic + this.getNormalRandom(0, 3))));
                this.diastolicBP = Math.round(Math.max(50, Math.min(110, baseDiastolic + this.getNormalRandom(0, 2))));

                // 血氧模拟
                let baseSpo2 = 98;
                switch (this.physiologicalState) {
                    case 'sleep': baseSpo2 = 97; break;
                    case 'active': baseSpo2 = 99; break;
                    case 'stress': baseSpo2 = 97; break;
                }
                this.spo2 = Math.round(Math.max(94, Math.min(100, baseSpo2 + this.getNormalRandom(0, 0.5))));

                // 体温模拟
                let baseTemp = 36.5;
                const tempCircadian = Math.sin((this.timeOfDay - 6) * Math.PI / 12) * 0.4;
                baseTemp += tempCircadian;
                switch (this.physiologicalState) {
                    case 'sleep': baseTemp -= 0.2; break;
                    case 'active': baseTemp += 0.1; break;
                    case 'stress': baseTemp += 0.2; break;
                }
                const targetTemp = baseTemp;
                this.temperature += (targetTemp - this.temperature) * 0.1;
                this.temperature += this.getNormalRandom(0, 0.05);
                this.temperature = Math.max(35.5, Math.min(38.5, this.temperature));

                // 呼吸频率
                let baseRR = 16;
                switch (this.physiologicalState) {
                    case 'sleep': baseRR = 12; break;
                    case 'active': baseRR = 18; break;
                    case 'stress': baseRR = 22; break;
                }
                this.respiratoryRate = Math.round(Math.max(8, Math.min(30, baseRR + this.getNormalRandom(0, 1.5))));

                // 血糖
                let baseGlucose = 5.0;
                const mealTimes = [7, 12, 18];
                for (const mealTime of mealTimes) {
                    const timeSinceMeal = Math.abs(this.timeOfDay - mealTime);
                    if (timeSinceMeal < 2) {
                        baseGlucose += (2 - timeSinceMeal) * 1.5;
                    }
                }
                const targetGlucose = baseGlucose;
                this.glucose += (targetGlucose - this.glucose) * 0.05;
                this.glucose += this.getNormalRandom(0, 0.1);
                this.glucose = Math.max(3.5, Math.min(8.0, this.glucose));

                // QT间期
                this.qtInterval = Math.round(Math.max(320, Math.min(480, 400 - (this.heartRate - 72) * 1.5 + this.getNormalRandom(0, 10))));

                // 压力指数
                const hrStress = Math.max(0, (this.heartRate - 80) * 0.5);
                const bpStress = Math.max(0, (this.systolicBP - 130) * 0.3);
                const rrStress = Math.max(0, (this.respiratoryRate - 18) * 0.8);
                this.stressIndex = Math.round(Math.max(0, Math.min(100, hrStress + bpStress + rrStress + this.stressLevel * 2 + this.getNormalRandom(0, 2))));

                const data = {
                    heartRate: this.heartRate,
                    systolicBP: this.systolicBP,
                    diastolicBP: this.diastolicBP,
                    spo2: this.spo2,
                    temperature: parseFloat(this.temperature.toFixed(1)),
                    respiratoryRate: this.respiratoryRate,
                    glucose: parseFloat(this.glucose.toFixed(1)),
                    qtInterval: this.qtInterval,
                    stressIndex: this.stressIndex,
                    timestamp: new Date()
                };

                this.updateStats(data);
                return data;
            }

            // 更新统计数据
            updateStats(data) {
                Object.keys(data).forEach(key => {
                    if (key !== 'timestamp') {
                        if (!this.stats[key]) {
                            this.stats[key] = { sum: 0, count: 0, min: Infinity, max: -Infinity, values: [] };
                        }

                        const stat = this.stats[key];
                        stat.sum += data[key];
                        stat.count++;
                        stat.min = Math.min(stat.min, data[key]);
                        stat.max = Math.max(stat.max, data[key]);

                        // 保存最近的值用于趋势分析
                        stat.values.push(data[key]);
                        if (stat.values.length > 10) {
                            stat.values.shift();
                        }
                    }
                });
            }

            // 重置统计数据
            resetStats() {
                this.stats = {};
            }

            // 检查异常值
            checkAbnormalValues(data) {
                const abnormal = [];
                if (data.heartRate < 60 || data.heartRate > 100) abnormal.push('心率');
                if (data.systolicBP < 90 || data.systolicBP > 140) abnormal.push('收缩压');
                if (data.diastolicBP < 60 || data.diastolicBP > 90) abnormal.push('舒张压');
                if (data.spo2 < 95) abnormal.push('血氧');
                if (data.temperature < 36.1 || data.temperature > 37.2) abnormal.push('体温');
                if (data.respiratoryRate < 12 || data.respiratoryRate > 20) abnormal.push('呼吸');
                if (data.glucose < 3.9 || data.glucose > 6.1) abnormal.push('血糖');
                if (data.qtInterval < 350 || data.qtInterval > 450) abnormal.push('QT间期');
                if (data.stressIndex > 30) abnormal.push('压力指数');
                return abnormal;
            }

            // 获取趋势
            getTrend(parameterName) {
                const stat = this.stats[parameterName];
                if (!stat || stat.values.length < 3) return '数据不足';

                const recent = stat.values.slice(-3);
                const avg = recent.reduce((a, b) => a + b, 0) / recent.length;
                const first = recent[0];
                const last = recent[recent.length - 1];

                if (Math.abs(last - first) < avg * 0.05) return '稳定';
                return last > first ? '上升' : '下降';
            }

            // 初始化图表
            initCharts() {
                // 图表配置将在后续添加
            }
        }

        // 初始化所有图表
        function initAllCharts() {
            const chartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    x: { display: true },
                    y: { display: true }
                },
                animation: { duration: 0 }
            };

            // 迷你图表配置
            const miniChartConfig = (color, min, max) => ({
                type: 'line',
                data: { labels: [], datasets: [{ data: [], borderColor: color, backgroundColor: color + '20', tension: 0.4, fill: true }] },
                options: { ...chartOptions, scales: { ...chartOptions.scales, y: { min, max } } }
            });

            // 初始化迷你图表
            charts.miniHeartRate = new Chart(document.getElementById('miniHeartRateChart').getContext('2d'), miniChartConfig('#e74c3c', 40, 120));
            charts.miniBloodPressure = new Chart(document.getElementById('miniBloodPressureChart').getContext('2d'), miniChartConfig('#8e44ad', 80, 160));
            charts.miniTemperature = new Chart(document.getElementById('miniTemperatureChart').getContext('2d'), miniChartConfig('#f39c12', 35.5, 38.5));
            charts.miniSpO2 = new Chart(document.getElementById('miniSpO2Chart').getContext('2d'), miniChartConfig('#3498db', 90, 100));
        }

        // 显示详细视图
        function showDetailView(parameterType) {
            console.log('点击了参数:', parameterType); // 调试信息
            simulator.currentDetailView = parameterType;

            // 隐藏主界面，显示详细视图
            document.getElementById('mainView').classList.add('hidden');
            document.getElementById('detailView').classList.add('active');

            // 配置详细视图内容
            const parameterConfig = {
                heartRate: {
                    icon: '💓',
                    title: '心率监测',
                    unit: '次/分',
                    color: '#e74c3c',
                    normalRange: '60-100',
                    getValue: () => simulator.heartRate,
                    getDataArray: () => simulator.dataHistory.heartRate
                },
                bloodPressure: {
                    icon: '🩸',
                    title: '血压监测',
                    unit: 'mmHg',
                    color: '#8e44ad',
                    normalRange: '90-140/60-90',
                    getValue: () => `${simulator.systolicBP}/${simulator.diastolicBP}`,
                    getDataArray: () => simulator.dataHistory.systolicBP
                },
                spo2: {
                    icon: '🫁',
                    title: '血氧监测',
                    unit: '%',
                    color: '#3498db',
                    normalRange: '95-100',
                    getValue: () => simulator.spo2,
                    getDataArray: () => simulator.dataHistory.spo2
                },
                temperature: {
                    icon: '🌡️',
                    title: '体温监测',
                    unit: '°C',
                    color: '#f39c12',
                    normalRange: '36.1-37.2',
                    getValue: () => simulator.temperature.toFixed(1),
                    getDataArray: () => simulator.dataHistory.temperature
                },
                respiratoryRate: {
                    icon: '🌬️',
                    title: '呼吸监测',
                    unit: '次/分',
                    color: '#27ae60',
                    normalRange: '12-20',
                    getValue: () => simulator.respiratoryRate,
                    getDataArray: () => simulator.dataHistory.respiratoryRate
                },
                glucose: {
                    icon: '🍯',
                    title: '血糖监测',
                    unit: 'mmol/L',
                    color: '#e67e22',
                    normalRange: '3.9-6.1',
                    getValue: () => simulator.glucose.toFixed(1),
                    getDataArray: () => simulator.dataHistory.glucose
                },
                qtInterval: {
                    icon: '📈',
                    title: 'QT间期监测',
                    unit: 'ms',
                    color: '#34495e',
                    normalRange: '350-450',
                    getValue: () => simulator.qtInterval,
                    getDataArray: () => simulator.dataHistory.qtInterval
                },
                stressIndex: {
                    icon: '🧠',
                    title: '压力指数监测',
                    unit: '',
                    color: '#9b59b6',
                    normalRange: '0-30',
                    getValue: () => simulator.stressIndex,
                    getDataArray: () => simulator.dataHistory.stressIndex
                }
            };

            const config = parameterConfig[parameterType];
            if (!config) return;

            // 更新详细视图标题和图标
            document.getElementById('detailIcon').textContent = config.icon;
            document.getElementById('detailIcon').style.color = config.color;
            document.getElementById('detailTitle').textContent = config.title;
            document.getElementById('detailCurrentValue').textContent = `${config.getValue()} ${config.unit}`;
            document.getElementById('detailCurrentValue').style.color = config.color;
            document.getElementById('waveformTitle').textContent = `${config.title}波形图`;

            console.log('配置详细视图:', config); // 调试信息

            // 更新参数信息
            updateDetailInfo(parameterType, config);

            // 初始化详细图表
            initDetailChart(parameterType, config);
        }

        // 显示主界面
        function showMainView() {
            document.getElementById('mainView').classList.remove('hidden');
            document.getElementById('detailView').classList.remove('active');
            simulator.currentDetailView = null;

            // 销毁详细图表
            if (detailChart) {
                detailChart.destroy();
                detailChart = null;
            }
        }

        // 初始化详细图表
        function initDetailChart(parameterType, config) {
            if (detailChart) {
                detailChart.destroy();
            }

            const ctx = document.getElementById('detailChart').getContext('2d');
            detailChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: simulator.timeLabels,
                    datasets: [{
                        label: config.title,
                        data: config.getDataArray(),
                        borderColor: config.color,
                        backgroundColor: config.color + '20',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 3,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: `${config.title} (${config.unit})`
                            }
                        }
                    },
                    animation: {
                        duration: 0
                    }
                }
            });
        }

        // 更新详细信息
        function updateDetailInfo(parameterType, config) {
            const stat = simulator.stats[parameterType];

            document.getElementById('infoCurrentValue').textContent = `${config.getValue()} ${config.unit}`;
            document.getElementById('infoNormalRange').textContent = config.normalRange;

            if (stat && stat.count > 0) {
                document.getElementById('infoAverageValue').textContent = `${(stat.sum / stat.count).toFixed(1)} ${config.unit}`;
                document.getElementById('infoMaxValue').textContent = `${stat.max} ${config.unit}`;
                document.getElementById('infoMinValue').textContent = `${stat.min} ${config.unit}`;
                document.getElementById('infoDataPoints').textContent = stat.count;
                document.getElementById('infoTrend').textContent = simulator.getTrend(parameterType);

                // 判断状态
                const currentValue = config.getValue();
                const abnormal = simulator.checkAbnormalValues(simulator.generateVitalSigns());
                const parameterNames = {
                    heartRate: '心率',
                    bloodPressure: '收缩压',
                    spo2: '血氧',
                    temperature: '体温',
                    respiratoryRate: '呼吸',
                    glucose: '血糖',
                    qtInterval: 'QT间期',
                    stressIndex: '压力指数'
                };

                const isAbnormal = abnormal.includes(parameterNames[parameterType]);
                document.getElementById('infoStatus').textContent = isAbnormal ? '异常' : '正常';
                document.getElementById('infoStatus').style.color = isAbnormal ? '#f44336' : '#4caf50';
            } else {
                document.getElementById('infoAverageValue').textContent = '--';
                document.getElementById('infoMaxValue').textContent = '--';
                document.getElementById('infoMinValue').textContent = '--';
                document.getElementById('infoDataPoints').textContent = '0';
                document.getElementById('infoTrend').textContent = '数据不足';
                document.getElementById('infoStatus').textContent = '正常';
                document.getElementById('infoStatus').style.color = '#4caf50';
            }
        }

        // 开始模拟
        function startSimulation() {
            if (simulator.isRunning) return;

            simulator.isRunning = true;
            simulator.startTime = Date.now();
            simulator.dataCount = 0;

            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('stopBtn').classList.add('active');

            updateStatus('监测中', true);

            simulator.interval = setInterval(() => {
                const data = simulator.generateVitalSigns();
                simulator.dataCount++;

                updateDisplay(data);
                updateCharts(data);
                updateDataCount(simulator.dataCount);
                updateRunTime(Date.now() - simulator.startTime);

                // 检查异常值
                const abnormal = simulator.checkAbnormalValues(data);
                updateAbnormalStatus(abnormal);

                // 如果在详细视图中，更新详细信息
                if (simulator.currentDetailView) {
                    updateDetailViewData();
                }
            }, 1000);
        }

        // 停止模拟
        function stopSimulation() {
            if (!simulator.isRunning) return;

            simulator.isRunning = false;
            clearInterval(simulator.interval);

            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('stopBtn').classList.remove('active');

            updateStatus('已停止', false);
        }

        // 重置数据
        function resetData() {
            stopSimulation();

            simulator.timeLabels = [];
            Object.keys(simulator.dataHistory).forEach(key => {
                simulator.dataHistory[key] = [];
            });
            simulator.dataCount = 0;
            simulator.resetStats();

            // 重置显示
            const elements = ['heartRate', 'bloodPressure', 'spo2', 'temperature', 'respiratoryRate', 'glucose', 'ecgQT', 'stressIndex'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.textContent = '--';
            });

            // 重置图表
            Object.values(charts).forEach(chart => {
                chart.data.labels = [];
                chart.data.datasets.forEach(dataset => dataset.data = []);
                chart.update();
            });

            // 如果在详细视图中，也重置详细图表
            if (detailChart) {
                detailChart.data.labels = [];
                detailChart.data.datasets.forEach(dataset => dataset.data = []);
                detailChart.update();
            }

            updateDataCount(0);
            updateRunTime(0);
            updateAbnormalStatus([]);
        }

        // 更新状态显示
        function updateStatus(text, isActive) {
            document.getElementById('statusText').textContent = text;
            const indicator = document.getElementById('statusIndicator');
            indicator.className = `status-indicator ${isActive ? 'active' : 'inactive'}`;
        }

        // 更新数据显示
        function updateDisplay(data) {
            document.getElementById('heartRate').textContent = data.heartRate;
            document.getElementById('bloodPressure').textContent = `${data.systolicBP}/${data.diastolicBP}`;
            document.getElementById('spo2').textContent = data.spo2;
            document.getElementById('temperature').textContent = data.temperature;
            document.getElementById('respiratoryRate').textContent = data.respiratoryRate;
            document.getElementById('glucose').textContent = data.glucose;
            document.getElementById('ecgQT').textContent = data.qtInterval;
            document.getElementById('stressIndex').textContent = data.stressIndex;

            // 添加心跳动画
            const heartRateCard = document.querySelector('.heart-rate');
            heartRateCard.classList.add('pulse-animation');
            setTimeout(() => heartRateCard.classList.remove('pulse-animation'), 1000);
        }

        // 更新图表
        function updateCharts(data) {
            const now = new Date().toLocaleTimeString();

            // 添加新数据
            simulator.timeLabels.push(now);
            simulator.dataHistory.heartRate.push(data.heartRate);
            simulator.dataHistory.systolicBP.push(data.systolicBP);
            simulator.dataHistory.diastolicBP.push(data.diastolicBP);
            simulator.dataHistory.spo2.push(data.spo2);
            simulator.dataHistory.temperature.push(data.temperature);
            simulator.dataHistory.respiratoryRate.push(data.respiratoryRate);
            simulator.dataHistory.glucose.push(data.glucose);
            simulator.dataHistory.qtInterval.push(data.qtInterval);
            simulator.dataHistory.stressIndex.push(data.stressIndex);

            // 限制数据点数量
            if (simulator.timeLabels.length > simulator.maxDataPoints) {
                simulator.timeLabels.shift();
                Object.keys(simulator.dataHistory).forEach(key => {
                    simulator.dataHistory[key].shift();
                });
            }

            // 更新迷你图表
            const chartUpdates = [
                { chart: charts.miniHeartRate, data: simulator.dataHistory.heartRate },
                { chart: charts.miniBloodPressure, data: simulator.dataHistory.systolicBP },
                { chart: charts.miniTemperature, data: simulator.dataHistory.temperature },
                { chart: charts.miniSpO2, data: simulator.dataHistory.spo2 }
            ];

            chartUpdates.forEach(({ chart, data }) => {
                chart.data.labels = simulator.timeLabels;
                chart.data.datasets[0].data = data;
                chart.update('none');
            });
        }

        // 更新详细视图数据
        function updateDetailViewData() {
            if (!simulator.currentDetailView || !detailChart) return;

            const parameterConfig = {
                heartRate: { getValue: () => simulator.heartRate, getDataArray: () => simulator.dataHistory.heartRate, unit: '次/分' },
                bloodPressure: { getValue: () => `${simulator.systolicBP}/${simulator.diastolicBP}`, getDataArray: () => simulator.dataHistory.systolicBP, unit: 'mmHg' },
                spo2: { getValue: () => simulator.spo2, getDataArray: () => simulator.dataHistory.spo2, unit: '%' },
                temperature: { getValue: () => simulator.temperature.toFixed(1), getDataArray: () => simulator.dataHistory.temperature, unit: '°C' },
                respiratoryRate: { getValue: () => simulator.respiratoryRate, getDataArray: () => simulator.dataHistory.respiratoryRate, unit: '次/分' },
                glucose: { getValue: () => simulator.glucose.toFixed(1), getDataArray: () => simulator.dataHistory.glucose, unit: 'mmol/L' },
                qtInterval: { getValue: () => simulator.qtInterval, getDataArray: () => simulator.dataHistory.qtInterval, unit: 'ms' },
                stressIndex: { getValue: () => simulator.stressIndex, getDataArray: () => simulator.dataHistory.stressIndex, unit: '' }
            };

            const config = parameterConfig[simulator.currentDetailView];
            if (!config) return;

            // 更新当前值显示
            document.getElementById('detailCurrentValue').textContent = `${config.getValue()} ${config.unit}`;

            // 更新详细图表
            detailChart.data.labels = simulator.timeLabels;
            detailChart.data.datasets[0].data = config.getDataArray();
            detailChart.update('none');

            // 更新详细信息
            updateDetailInfo(simulator.currentDetailView, config);
        }

        // 更新异常状态
        function updateAbnormalStatus(abnormalList) {
            // 重置所有卡片
            const cards = document.querySelectorAll('.vital-card');
            cards.forEach(card => card.classList.remove('abnormal'));

            // 标记异常的卡片
            abnormalList.forEach(param => {
                const mapping = {
                    '心率': '.heart-rate',
                    '收缩压': '.blood-pressure',
                    '舒张压': '.blood-pressure',
                    '血氧': '.spo2',
                    '体温': '.temperature',
                    '呼吸': '.respiratory-rate',
                    '血糖': '.glucose',
                    'QT间期': '.ecg',
                    '压力指数': '.stress-index'
                };

                const selector = mapping[param];
                if (selector) {
                    const card = document.querySelector(selector);
                    if (card) card.classList.add('abnormal');
                }
            });
        }

        // 更新数据计数
        function updateDataCount(count) {
            document.getElementById('dataCount').textContent = count;
        }

        // 更新运行时间
        function updateRunTime(milliseconds) {
            const seconds = Math.floor(milliseconds / 1000);
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            document.getElementById('runTime').textContent =
                `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        // 导出数据
        function exportData() {
            if (simulator.timeLabels.length === 0) {
                alert('没有数据可导出');
                return;
            }

            let csvContent = "时间,心率,收缩压,舒张压,血氧,体温,呼吸频率,血糖,QT间期,压力指数\n";
            for (let i = 0; i < simulator.timeLabels.length; i++) {
                const row = [
                    simulator.timeLabels[i],
                    simulator.dataHistory.heartRate[i] || '',
                    simulator.dataHistory.systolicBP[i] || '',
                    simulator.dataHistory.diastolicBP[i] || '',
                    simulator.dataHistory.spo2[i] || '',
                    simulator.dataHistory.temperature[i] || '',
                    simulator.dataHistory.respiratoryRate[i] || '',
                    simulator.dataHistory.glucose[i] || '',
                    simulator.dataHistory.qtInterval[i] || '',
                    simulator.dataHistory.stressIndex[i] || ''
                ].join(',');
                csvContent += row + '\n';
            }

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `interactive_medical_data_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
            link.click();
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initAllCharts();
            updateStatus('系统已就绪', false);
            updateDataCount(0);
            updateRunTime(0);

            // 显示初始数据
            displayInitialData();
        });

        // 显示初始数据
        function displayInitialData() {
            const initialData = {
                heartRate: simulator.heartRate,
                systolicBP: simulator.systolicBP,
                diastolicBP: simulator.diastolicBP,
                spo2: simulator.spo2,
                temperature: simulator.temperature,
                respiratoryRate: simulator.respiratoryRate,
                glucose: simulator.glucose,
                qtInterval: simulator.qtInterval,
                stressIndex: simulator.stressIndex
            };

            updateDisplay(initialData);
        }
    </script>
</body>
</html>
