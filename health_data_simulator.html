<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生理数据模拟器 - 实时可视化</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .btn.active {
            background: linear-gradient(45deg, #2e7d32, #4caf50);
        }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .data-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .data-card:hover {
            transform: translateY(-5px);
        }

        .data-card .icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .data-card .value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .data-card .label {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 15px;
        }

        .data-card .range {
            color: #999;
            font-size: 0.9em;
        }

        .heart-rate .icon { color: #e74c3c; }
        .heart-rate .value { color: #e74c3c; }

        .spo2 .icon { color: #3498db; }
        .spo2 .value { color: #3498db; }

        .temperature .icon { color: #f39c12; }
        .temperature .value { color: #f39c12; }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .chart-container h3 {
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }

        .chart-wrapper {
            position: relative;
            height: 300px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-indicator.active {
            background: #4caf50;
        }

        .status-indicator.inactive {
            background: #ccc;
            animation: none;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pulse-animation {
            animation: heartbeat 1s infinite;
        }

        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .log-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-entry {
            padding: 8px 12px;
            margin-bottom: 5px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            background: #e8f5e8;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 生理数据模拟器</h1>
            <p>实时生成与CH32V307VCT6相同的生理数据并可视化显示</p>
        </div>

        <div class="control-panel">
            <span class="status-indicator" id="statusIndicator"></span>
            <span id="statusText">系统已就绪</span>
            <br><br>
            <button class="btn" id="startBtn" onclick="startSimulation()">开始模拟</button>
            <button class="btn" id="stopBtn" onclick="stopSimulation()" disabled>停止模拟</button>
            <button class="btn" onclick="resetData()">重置数据</button>
        </div>

        <div class="data-grid">
            <div class="data-card heart-rate">
                <div class="icon">💓</div>
                <div class="value" id="heartRate">--</div>
                <div class="label">心率 (次/分)</div>
                <div class="range">正常范围: 55-105</div>
            </div>
            <div class="data-card spo2">
                <div class="icon">🫁</div>
                <div class="value" id="spo2">--</div>
                <div class="label">血氧饱和度 (%)</div>
                <div class="range">正常范围: 80-100</div>
            </div>
            <div class="data-card temperature">
                <div class="icon">🌡️</div>
                <div class="value" id="temperature">--</div>
                <div class="label">体温 (°C)</div>
                <div class="range">正常范围: 35.0-40.0</div>
            </div>
        </div>

        <div class="chart-container">
            <h3>📈 实时数据图表</h3>
            <div class="chart-wrapper">
                <canvas id="healthChart"></canvas>
            </div>
        </div>

        <div class="log-panel">
            <h3>📋 数据日志</h3>
            <div id="logContainer"></div>
        </div>
    </div>

    <script>
        // 模拟CH32V307VCT6的数据生成逻辑
        class HealthDataSimulator {
            constructor() {
                this.isRunning = false;
                this.interval = null;
                
                // 心率模拟参数（与CH32V307VCT6相同）
                this.HR_BASE_VALUE = 75;
                this.HR_MIN_VALUE = 55;
                this.HR_MAX_VALUE = 105;
                this.HR_CHANGE_PROBABILITY = 95;
                this.HR_MAX_CHANGE = 3;
                this.simulatedHeartRate = this.HR_BASE_VALUE;
                this.randomSeed = 12345;
                
                // 心率影响因子
                this.stressLevel = 0;
                this.activityIntensity = 0;
                this.fatigueLevel = 0;
                this.dailyCycleCounter = 0;
                this.hrSimulationMode = 2; // 0-睡眠, 1-休息, 2-正常, 3-活跃, 4-运动
                this.modeChangeTimer = 0;
                this.changeCounter = 0;
                
                // 数据存储
                this.heartRateData = [];
                this.spo2Data = [];
                this.temperatureData = [];
                this.timeLabels = [];
                this.maxDataPoints = 50;
                
                this.initChart();
            }
            
            // 简单随机数生成器（与CH32V307VCT6相同）
            simpleRandom() {
                this.randomSeed = (this.randomSeed * 1103515245 + 12345) & 0x7FFFFFFF;
                return this.randomSeed;
            }
            
            // 获取范围内随机数
            getRandomInRange(min, max) {
                return min + (this.simpleRandom() % (max - min + 1));
            }
            
            // 应用自然变化（与CH32V307VCT6相同）
            applyNaturalVariation(value) {
                const variation = this.getRandomInRange(-2, 2);
                let result = value + variation;
                
                if (result < this.HR_MIN_VALUE) result = this.HR_MIN_VALUE;
                if (result > this.HR_MAX_VALUE) result = this.HR_MAX_VALUE;
                
                return result;
            }
        }
        
        // 全局变量
        let simulator = new HealthDataSimulator();
        let chart;
        
        // 初始化图表
        function initChart() {
            const ctx = document.getElementById('healthChart').getContext('2d');
            chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '心率 (次/分)',
                        data: [],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '血氧 (%)',
                        data: [],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }, {
                        label: '体温 (°C)',
                        data: [],
                        borderColor: '#f39c12',
                        backgroundColor: 'rgba(243, 156, 18, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y2'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '心率 (次/分)'
                            },
                            min: 50,
                            max: 110
                        },
                        y1: {
                            type: 'linear',
                            display: false,
                            position: 'right',
                            min: 80,
                            max: 100
                        },
                        y2: {
                            type: 'linear',
                            display: false,
                            position: 'right',
                            min: 35,
                            max: 41
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    animation: {
                        duration: 0
                    }
                }
            });
        }
        
            // 更新心率影响因子（与CH32V307VCT6相同）
            updateHeartRateFactors() {
                this.dailyCycleCounter++;

                // 模拟日常周期影响
                const hourOfDay = (this.dailyCycleCounter / 60) % 24;

                // 根据时间调整基础参数
                if (hourOfDay >= 22 || hourOfDay <= 6) {
                    this.stressLevel = Math.max(0, this.stressLevel - 1);
                    this.activityIntensity = 0;
                    this.fatigueLevel = Math.min(10, this.fatigueLevel + 1);
                } else if (hourOfDay >= 7 && hourOfDay <= 9) {
                    this.stressLevel = Math.min(10, this.stressLevel + 2);
                    this.activityIntensity = Math.min(10, this.activityIntensity + 3);
                } else if (hourOfDay >= 18 && hourOfDay <= 21) {
                    this.stressLevel = Math.max(0, this.stressLevel - 2);
                    this.activityIntensity = Math.max(0, this.activityIntensity - 2);
                }

                // 随机波动
                if (this.getRandomInRange(0, 100) < 20) {
                    this.stressLevel = Math.max(0, Math.min(10, this.stressLevel + this.getRandomInRange(-2, 2)));
                    this.activityIntensity = Math.max(0, Math.min(10, this.activityIntensity + this.getRandomInRange(-1, 1)));
                    this.fatigueLevel = Math.max(0, Math.min(10, this.fatigueLevel + this.getRandomInRange(-1, 1)));
                }
            }

            // 心率模拟（与CH32V307VCT6相同逻辑）
            simulateHeartRate() {
                this.updateHeartRateFactors();

                // 定期切换模式
                this.modeChangeTimer++;
                if (this.modeChangeTimer > 80) {
                    this.modeChangeTimer = 0;
                    this.hrSimulationMode = this.getRandomInRange(0, 4);
                }

                // 控制变化频率
                this.changeCounter++;
                if (this.changeCounter < 1) {
                    return this.simulatedHeartRate;
                }
                this.changeCounter = 0;

                // 根据概率决定是否变化
                if (this.getRandomInRange(0, 100) > this.HR_CHANGE_PROBABILITY) {
                    return this.simulatedHeartRate;
                }

                // 根据模式设置目标心率
                let targetHR = this.HR_BASE_VALUE;
                switch (this.hrSimulationMode) {
                    case 0: targetHR = 60; break; // 睡眠
                    case 1: targetHR = 70; break; // 休息
                    case 2: targetHR = 75; break; // 正常
                    case 3: targetHR = 85; break; // 活跃
                    case 4: targetHR = 95; break; // 运动
                }

                // 应用影响因子
                targetHR += this.stressLevel * 2;
                targetHR += this.activityIntensity * 1.5;
                targetHR -= this.fatigueLevel * 0.5;

                // 计算变化方向和幅度
                const diff = targetHR - this.simulatedHeartRate;
                let change = 0;

                if (Math.abs(diff) > 10) {
                    change = diff > 0 ? this.HR_MAX_CHANGE : -this.HR_MAX_CHANGE;
                } else if (Math.abs(diff) > 5) {
                    change = diff > 0 ? 2 : -2;
                } else {
                    change = this.getRandomInRange(-this.HR_MAX_CHANGE, this.HR_MAX_CHANGE);
                }

                let newHR = this.simulatedHeartRate + change;

                // 限制范围
                if (newHR < this.HR_MIN_VALUE) newHR = this.HR_MIN_VALUE;
                if (newHR > this.HR_MAX_VALUE) newHR = this.HR_MAX_VALUE;

                this.simulatedHeartRate = newHR;
                return this.applyNaturalVariation(this.simulatedHeartRate);
            }

            // 模拟血氧数据
            simulateSpO2() {
                const baseSpO2 = 94;
                const variation = this.getRandomInRange(-4, 6);
                let spo2 = baseSpO2 + variation;

                // 限制在80-100范围内
                if (spo2 < 80) spo2 = 80;
                if (spo2 > 100) spo2 = 100;

                return spo2;
            }

            // 模拟体温数据
            simulateTemperature() {
                const baseTemp = 36.5;
                const variation = (this.getRandomInRange(-10, 10) / 10.0);
                let temp = baseTemp + variation;

                // 限制在35.0-40.0范围内
                if (temp < 35.0) temp = 35.0;
                if (temp > 40.0) temp = 40.0;

                return parseFloat(temp.toFixed(1));
            }

            // 生成完整健康数据
            generateHealthData() {
                const heartRate = this.simulateHeartRate();
                const spo2 = this.simulateSpO2();
                const temperature = this.simulateTemperature();

                return { heartRate, spo2, temperature };
            }
        }

        // 开始模拟
        function startSimulation() {
            if (simulator.isRunning) return;

            simulator.isRunning = true;
            document.getElementById('startBtn').disabled = true;
            document.getElementById('startBtn').classList.remove('active');
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('stopBtn').classList.add('active');

            updateStatus('运行中', true);
            addLogEntry('开始生理数据模拟');

            simulator.interval = setInterval(() => {
                const data = simulator.generateHealthData();
                updateDisplay(data);
                updateChart(data);

                // 生成与CH32V307VCT6相同格式的日志
                const logMessage = `健康数据:心率=${data.heartRate}次/分,血氧=${data.spo2}%,体温=${data.temperature}°C`;
                addLogEntry(logMessage);
            }, 1000); // 每秒更新一次
        }

        // 停止模拟
        function stopSimulation() {
            if (!simulator.isRunning) return;

            simulator.isRunning = false;
            clearInterval(simulator.interval);

            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('stopBtn').classList.remove('active');

            updateStatus('已停止', false);
            addLogEntry('停止生理数据模拟');
        }

        // 重置数据
        function resetData() {
            stopSimulation();

            simulator.heartRateData = [];
            simulator.spo2Data = [];
            simulator.temperatureData = [];
            simulator.timeLabels = [];

            // 重置显示
            document.getElementById('heartRate').textContent = '--';
            document.getElementById('spo2').textContent = '--';
            document.getElementById('temperature').textContent = '--';

            // 重置图表
            chart.data.labels = [];
            chart.data.datasets[0].data = [];
            chart.data.datasets[1].data = [];
            chart.data.datasets[2].data = [];
            chart.update();

            // 清空日志
            document.getElementById('logContainer').innerHTML = '';

            addLogEntry('数据已重置');
        }

        // 更新状态显示
        function updateStatus(text, isActive) {
            document.getElementById('statusText').textContent = text;
            const indicator = document.getElementById('statusIndicator');
            indicator.className = `status-indicator ${isActive ? 'active' : 'inactive'}`;
        }

        // 更新数据显示
        function updateDisplay(data) {
            const heartRateElement = document.getElementById('heartRate');
            const spo2Element = document.getElementById('spo2');
            const temperatureElement = document.getElementById('temperature');

            heartRateElement.textContent = data.heartRate;
            spo2Element.textContent = data.spo2;
            temperatureElement.textContent = data.temperature;

            // 添加脉冲动画
            heartRateElement.parentElement.classList.add('pulse-animation');
            setTimeout(() => heartRateElement.parentElement.classList.remove('pulse-animation'), 1000);
        }

        // 更新图表
        function updateChart(data) {
            const now = new Date().toLocaleTimeString();

            // 添加新数据
            simulator.timeLabels.push(now);
            simulator.heartRateData.push(data.heartRate);
            simulator.spo2Data.push(data.spo2);
            simulator.temperatureData.push(data.temperature);

            // 限制数据点数量
            if (simulator.timeLabels.length > simulator.maxDataPoints) {
                simulator.timeLabels.shift();
                simulator.heartRateData.shift();
                simulator.spo2Data.shift();
                simulator.temperatureData.shift();
            }

            // 更新图表
            chart.data.labels = simulator.timeLabels;
            chart.data.datasets[0].data = simulator.heartRateData;
            chart.data.datasets[1].data = simulator.spo2Data;
            chart.data.datasets[2].data = simulator.temperatureData;
            chart.update('none'); // 无动画更新以提高性能
        }

        // 添加日志条目
        function addLogEntry(message) {
            const logContainer = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;

            // 限制日志条目数量
            const entries = logContainer.children;
            if (entries.length > 100) {
                logContainer.removeChild(entries[0]);
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            simulator.initChart = initChart;
            initChart();
            updateStatus('系统已就绪', false);
            addLogEntry('生理数据模拟器已启动');
            addLogEntry('点击"开始模拟"按钮开始生成数据');
            addLogEntry('数据生成逻辑与CH32V307VCT6完全相同');
        });
    </script>
</body>
</html>
