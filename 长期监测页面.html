<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>长期监测 - 智能医疗监测系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #2ed573 0%, #17c0eb 100%);
            border-radius: 15px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding: 10px 15px;
            background: rgba(255,255,255,0.8);
            border-radius: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: #666;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb-item:hover {
            color: #4facfe;
        }

        .breadcrumb-item.active {
            color: #333;
            font-weight: bold;
        }

        .breadcrumb-separator {
            color: #999;
        }

        .monitoring-panel {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .monitoring-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #2ed573;
        }

        .status-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #2ed573;
            margin-bottom: 5px;
        }

        .status-label {
            color: #666;
            font-size: 1.1em;
        }

        .monitoring-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            min-width: 150px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #2ed573 0%, #17c0eb 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffa726 0%, #fb8c00 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .data-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .data-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2ed573, #17c0eb);
        }

        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .data-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
            display: block;
        }

        .data-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .data-label {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 10px;
        }

        .data-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-normal { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-danger { background: #f8d7da; color: #721c24; }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .chart-container h3 {
            margin-bottom: 20px;
            color: #333;
            text-align: center;
            font-size: 1.5em;
        }

        .chart-wrapper {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        .waveform-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .waveform-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .waveform-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2ed573, #17c0eb);
        }

        .waveform-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .waveform-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .waveform-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #2ed573;
        }

        .waveform-wrapper {
            position: relative;
            height: 200px;
        }

        .monitoring-settings {
            background: #e8f5e8;
            border-left: 4px solid #2ed573;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .monitoring-settings h3 {
            color: #2ed573;
            margin-bottom: 15px;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .setting-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .setting-item label {
            font-weight: bold;
            color: #666;
            min-width: 80px;
        }

        .setting-item select,
        .setting-item input {
            flex: 1;
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .alert-panel {
            background: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
            animation: slideIn 0.5s ease;
        }

        .alert-panel.show {
            display: block;
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .log-panel {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .timestamp {
            color: #3498db;
            margin-right: 10px;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(0,0,0,0.9);
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 5px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .monitoring-controls {
                flex-direction: column;
                align-items: center;
            }
            
            .data-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .settings-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goBack()" title="返回主页">←</button>
    
    <div class="container">
        <div class="header">
            <h1>📊 长期监测模式</h1>
            <p>24小时连续生理参数监测 | 适用于重症监护和慢病管理</p>
        </div>

        <div class="breadcrumb">
            <a href="#" class="breadcrumb-item" onclick="goBack()">🏠 首页</a>
            <span class="breadcrumb-separator">></span>
            <span class="breadcrumb-item active">长期监测</span>
        </div>

        <div class="alert-panel" id="alertPanel">
            <strong>⚠️ 异常警报：</strong>
            <span id="alertMessage">检测到生理参数异常，请立即关注！</span>
        </div>

        <div class="monitoring-panel">
            <h2>📈 监测状态</h2>
            <div class="monitoring-status">
                <div class="status-card">
                    <div class="status-value" id="monitoringTime">00:00:00</div>
                    <div class="status-label">监测时长</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="dataPoints">0</div>
                    <div class="status-label">数据点数</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="alertCount">0</div>
                    <div class="status-label">异常次数</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="connectionStatus">在线</div>
                    <div class="status-label">连接状态</div>
                </div>
            </div>
            
            <div class="monitoring-controls">
                <button class="btn btn-success" id="startBtn" onclick="startMonitoring()">开始监测</button>
                <button class="btn btn-warning" id="pauseBtn" onclick="pauseMonitoring()" disabled>暂停监测</button>
                <button class="btn btn-danger" id="stopBtn" onclick="stopMonitoring()" disabled>停止监测</button>
                <button class="btn btn-primary" onclick="exportData()">导出数据</button>
            </div>
        </div>

        <div class="monitoring-settings">
            <h3>⚙️ 监测设置</h3>
            <div class="settings-grid">
                <div class="setting-item">
                    <label>采样频率:</label>
                    <select id="samplingRate">
                        <option value="1">1秒/次</option>
                        <option value="5" selected>5秒/次</option>
                        <option value="10">10秒/次</option>
                        <option value="30">30秒/次</option>
                        <option value="60">1分钟/次</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>监测时长:</label>
                    <select id="monitoringDuration">
                        <option value="1">1小时</option>
                        <option value="6">6小时</option>
                        <option value="12">12小时</option>
                        <option value="24" selected>24小时</option>
                        <option value="0">持续监测</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>预警阈值:</label>
                    <select id="alertThreshold">
                        <option value="low">低敏感度</option>
                        <option value="medium" selected>中等敏感度</option>
                        <option value="high">高敏感度</option>
                    </select>
                </div>
                <div class="setting-item">
                    <label>数据存储:</label>
                    <select id="dataStorage">
                        <option value="local" selected>本地存储</option>
                        <option value="cloud">云端存储</option>
                        <option value="both">本地+云端</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="data-grid">
            <div class="data-card">
                <div class="data-icon">❤️</div>
                <div class="data-value" id="heartRate">--</div>
                <div class="data-label">心率 (bpm)</div>
                <div class="data-status status-normal" id="heartRateStatus">待监测</div>
            </div>

            <div class="data-card">
                <div class="data-icon">🩸</div>
                <div class="data-value" id="bloodPressure">--/--</div>
                <div class="data-label">血压 (mmHg)</div>
                <div class="data-status status-normal" id="bloodPressureStatus">待监测</div>
            </div>

            <div class="data-card">
                <div class="data-icon">🫁</div>
                <div class="data-value" id="spo2">--</div>
                <div class="data-label">血氧饱和度 (%)</div>
                <div class="data-status status-normal" id="spo2Status">待监测</div>
            </div>

            <div class="data-card">
                <div class="data-icon">🌡️</div>
                <div class="data-value" id="temperature">--</div>
                <div class="data-label">体温 (°C)</div>
                <div class="data-status status-normal" id="temperatureStatus">待监测</div>
            </div>

            <div class="data-card">
                <div class="data-icon">🩺</div>
                <div class="data-value" id="bloodGlucose">--</div>
                <div class="data-label">血糖 (mmol/L)</div>
                <div class="data-status status-normal" id="bloodGlucoseStatus">待监测</div>
            </div>

            <div class="data-card">
                <div class="data-icon">💨</div>
                <div class="data-value" id="respiratoryRate">--</div>
                <div class="data-label">呼吸频率 (次/分)</div>
                <div class="data-status status-normal" id="respiratoryRateStatus">待监测</div>
            </div>
        </div>

        <div class="chart-container">
            <h3>📈 实时趋势图 (最近1小时)</h3>
            <div class="chart-wrapper">
                <canvas id="trendChart"></canvas>
            </div>
        </div>

        <div class="chart-container">
            <h3>💓 心电图波形</h3>
            <div class="chart-wrapper">
                <canvas id="ecgChart"></canvas>
            </div>
        </div>

        <div class="waveform-grid">
            <div class="waveform-card">
                <div class="waveform-header">
                    <div class="waveform-title">❤️ 心率波形</div>
                    <div class="waveform-value" id="heartRateWaveValue">--</div>
                </div>
                <div class="waveform-wrapper">
                    <canvas id="heartRateWave"></canvas>
                </div>
            </div>

            <div class="waveform-card">
                <div class="waveform-header">
                    <div class="waveform-title">🩸 血压波形</div>
                    <div class="waveform-value" id="bloodPressureWaveValue">--/--</div>
                </div>
                <div class="waveform-wrapper">
                    <canvas id="bloodPressureWave"></canvas>
                </div>
            </div>

            <div class="waveform-card">
                <div class="waveform-header">
                    <div class="waveform-title">🫁 血氧波形</div>
                    <div class="waveform-value" id="spo2WaveValue">--%</div>
                </div>
                <div class="waveform-wrapper">
                    <canvas id="spo2Wave"></canvas>
                </div>
            </div>

            <div class="waveform-card">
                <div class="waveform-header">
                    <div class="waveform-title">🌡️ 体温波形</div>
                    <div class="waveform-value" id="temperatureWaveValue">--°C</div>
                </div>
                <div class="waveform-wrapper">
                    <canvas id="temperatureWave"></canvas>
                </div>
            </div>

            <div class="waveform-card">
                <div class="waveform-header">
                    <div class="waveform-title">🩺 血糖波形</div>
                    <div class="waveform-value" id="bloodGlucoseWaveValue">--</div>
                </div>
                <div class="waveform-wrapper">
                    <canvas id="bloodGlucoseWave"></canvas>
                </div>
            </div>

            <div class="waveform-card">
                <div class="waveform-header">
                    <div class="waveform-title">💨 呼吸波形</div>
                    <div class="waveform-value" id="respiratoryWaveValue">--</div>
                </div>
                <div class="waveform-wrapper">
                    <canvas id="respiratoryWave"></canvas>
                </div>
            </div>
        </div>

        <div class="log-panel" id="logPanel">
            <div class="log-entry">
                <span class="timestamp">[2024-12-19 10:30:15]</span>
                <span>长期监测系统已准备就绪...</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let isMonitoring = false;
        let isPaused = false;
        let startTime = null;
        let monitoringTimer = null;
        let dataCollectionTimer = null;
        let dataPoints = 0;
        let alertCount = 0;
        let trendChart = null;
        let ecgChart = null;

        // 波形图对象
        let waveformCharts = {
            heartRate: null,
            bloodPressure: null,
            spo2: null,
            temperature: null,
            bloodGlucose: null,
            respiratory: null
        };

        // 数据存储
        let monitoringData = {
            heartRate: [],
            bloodPressure: [],
            spo2: [],
            temperature: [],
            timestamps: []
        };

        let ecgData = {
            values: [],
            timestamps: []
        };

        // 波形数据存储
        let waveformData = {
            heartRate: { values: [], timestamps: [] },
            bloodPressure: { systolic: [], diastolic: [], timestamps: [] },
            spo2: { values: [], timestamps: [] },
            temperature: { values: [], timestamps: [] },
            bloodGlucose: { values: [], timestamps: [] },
            respiratory: { values: [], timestamps: [] }
        };

        // 返回主页
        function goBack() {
            if (isMonitoring && !confirm('监测正在进行中，确定要离开吗？')) {
                return;
            }
            window.location.href = '完善版智能医疗监测系统.html';
        }

        // 开始监测
        function startMonitoring() {
            if (isMonitoring) return;
            
            isMonitoring = true;
            isPaused = false;
            startTime = Date.now();
            dataPoints = 0;
            alertCount = 0;
            
            // 更新按钮状态
            document.getElementById('startBtn').disabled = true;
            document.getElementById('pauseBtn').disabled = false;
            document.getElementById('stopBtn').disabled = false;
            
            // 开始计时器
            monitoringTimer = setInterval(updateMonitoringTime, 1000);
            
            // 开始数据采集
            const samplingRate = parseInt(document.getElementById('samplingRate').value) * 1000;
            dataCollectionTimer = setInterval(collectData, samplingRate);
            
            addLogEntry('开始长期监测');
        }

        // 暂停监测
        function pauseMonitoring() {
            if (!isMonitoring) return;
            
            if (isPaused) {
                // 恢复监测
                isPaused = false;
                const samplingRate = parseInt(document.getElementById('samplingRate').value) * 1000;
                dataCollectionTimer = setInterval(collectData, samplingRate);
                document.getElementById('pauseBtn').textContent = '暂停监测';
                addLogEntry('恢复监测');
            } else {
                // 暂停监测
                isPaused = true;
                clearInterval(dataCollectionTimer);
                document.getElementById('pauseBtn').textContent = '恢复监测';
                addLogEntry('暂停监测');
            }
        }

        // 停止监测
        function stopMonitoring() {
            if (!isMonitoring) return;
            
            if (!confirm('确定要停止监测吗？')) return;
            
            isMonitoring = false;
            isPaused = false;
            
            // 清除计时器
            clearInterval(monitoringTimer);
            clearInterval(dataCollectionTimer);
            
            // 重置按钮状态
            document.getElementById('startBtn').disabled = false;
            document.getElementById('pauseBtn').disabled = true;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('pauseBtn').textContent = '暂停监测';
            
            addLogEntry('停止监测');
            
            // 显示监测总结
            showMonitoringSummary();
        }

        // 更新监测时间
        function updateMonitoringTime() {
            if (!isMonitoring || isPaused) return;
            
            const elapsed = Date.now() - startTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            document.getElementById('monitoringTime').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // 数据采集
        function collectData() {
            if (!isMonitoring || isPaused) return;

            // 生成优化的模拟数据（更真实的长期变化）
            const time = Date.now();
            const timeOfDay = (time % (24 * 3600000)) / 3600000; // 0-24小时

            // 根据时间模拟生理节律
            const circadianFactor = Math.sin((timeOfDay - 6) * Math.PI / 12); // 6点为最低点

            const heartRate = Math.round(70 + circadianFactor * 10 + Math.random() * 15);
            const systolicBP = Math.round(120 + circadianFactor * 5 + Math.random() * 20);
            const diastolicBP = Math.round(80 + circadianFactor * 3 + Math.random() * 15);

            // 血氧：正常且长期稳定 (97-99%)，很少变化
            const spo2 = Math.round(98 + Math.sin(time / 30000) * 0.8 + Math.random() * 0.4 - 0.2);

            // 体温：变化不频繁且波动小，受生理节律影响
            const baseTemp = 36.6;
            const tempCircadian = circadianFactor * 0.4; // 生理节律影响
            const tempNoise = Math.sin(time / 20000) * 0.2 + (Math.random() - 0.5) * 0.1; // 小幅随机波动
            const temperature = (baseTemp + tempCircadian + tempNoise).toFixed(1);

            // 血糖：在正常值和偏高之间波动 (5.2-7.8 mmol/L)
            const baseGlucose = 6.2; // 正常偏高基准值
            const glucoseCircadian = circadianFactor * 0.5; // 受进食时间影响
            const glucoseTrend = Math.sin(time / 15000) * 1.0; // 较大波动
            const glucoseNoise = (Math.random() - 0.5) * 0.6;
            const bloodGlucose = Math.max(5.2, Math.min(7.8, baseGlucose + glucoseCircadian + glucoseTrend + glucoseNoise)).toFixed(1);

            const respiratoryRate = Math.round(16 + circadianFactor * 2 + Math.random() * 4);
            
            // 更新显示
            document.getElementById('heartRate').textContent = heartRate;
            document.getElementById('bloodPressure').textContent = `${systolicBP}/${diastolicBP}`;
            document.getElementById('spo2').textContent = spo2;
            document.getElementById('temperature').textContent = temperature;
            document.getElementById('bloodGlucose').textContent = bloodGlucose;
            document.getElementById('respiratoryRate').textContent = respiratoryRate;
            
            // 更新状态 - 调整阈值适应新的数据范围
            updateVitalStatus('heartRate', heartRate, 60, 100);
            updateVitalStatus('bloodPressure', systolicBP, 90, 140);
            updateVitalStatus('spo2', spo2, 96, 100); // 血氧正常范围调整
            updateVitalStatus('temperature', parseFloat(temperature), 36.0, 37.2); // 体温正常范围调整
            updateVitalStatus('bloodGlucose', parseFloat(bloodGlucose), 3.9, 7.0); // 血糖正常范围调整，允许偏高
            updateVitalStatus('respiratoryRate', respiratoryRate, 12, 20);
            
            // 存储数据
            const now = new Date();
            monitoringData.timestamps.push(now);
            monitoringData.heartRate.push(heartRate);
            monitoringData.spo2.push(spo2);
            monitoringData.temperature.push(parseFloat(temperature));

            // 存储波形数据
            waveformData.heartRate.timestamps.push(now);
            waveformData.heartRate.values.push(heartRate);

            waveformData.bloodPressure.timestamps.push(now);
            waveformData.bloodPressure.systolic.push(systolicBP);
            waveformData.bloodPressure.diastolic.push(diastolicBP);

            waveformData.spo2.timestamps.push(now);
            waveformData.spo2.values.push(spo2);

            waveformData.temperature.timestamps.push(now);
            waveformData.temperature.values.push(parseFloat(temperature));

            waveformData.bloodGlucose.timestamps.push(now);
            waveformData.bloodGlucose.values.push(parseFloat(bloodGlucose));

            waveformData.respiratory.timestamps.push(now);
            waveformData.respiratory.values.push(respiratoryRate);

            // 限制数据点数量（保留最近1小时的数据，按5秒采样约720个点）
            const maxPoints = 720;
            if (monitoringData.timestamps.length > maxPoints) {
                monitoringData.timestamps.shift();
                monitoringData.heartRate.shift();
                monitoringData.spo2.shift();
                monitoringData.temperature.shift();
            }

            // 限制波形数据点数量（保留最近30分钟的数据，约360个点）
            const maxWavePoints = 360;
            Object.keys(waveformData).forEach(key => {
                if (waveformData[key].timestamps && waveformData[key].timestamps.length > maxWavePoints) {
                    waveformData[key].timestamps.shift();
                    if (waveformData[key].values) {
                        waveformData[key].values.shift();
                    }
                    if (waveformData[key].systolic) {
                        waveformData[key].systolic.shift();
                        waveformData[key].diastolic.shift();
                    }
                }
            });
            
            // 更新图表
            updateCharts();
            
            // 更新统计
            dataPoints++;
            document.getElementById('dataPoints').textContent = dataPoints;
            
            // 检查异常
            checkAbnormalValues({
                heartRate,
                systolicBP,
                diastolicBP,
                spo2,
                temperature: parseFloat(temperature),
                bloodGlucose: parseFloat(bloodGlucose),
                respiratoryRate
            });
            
            // 生成ECG数据
            generateECGData(heartRate);
        }

        // 更新生理参数状态
        function updateVitalStatus(param, value, min, max) {
            const statusElement = document.getElementById(param + 'Status');
            if (value < min || value > max) {
                statusElement.textContent = '异常';
                statusElement.className = 'data-status status-danger';
            } else if (value < min * 1.1 || value > max * 0.9) {
                statusElement.textContent = '注意';
                statusElement.className = 'data-status status-warning';
            } else {
                statusElement.textContent = '正常';
                statusElement.className = 'data-status status-normal';
            }
        }

        // 检查异常值 - 优化阈值判断
        function checkAbnormalValues(data) {
            let alerts = [];

            const threshold = document.getElementById('alertThreshold').value;
            let multiplier = 1.0;
            if (threshold === 'low') multiplier = 1.2;
            else if (threshold === 'high') multiplier = 0.8;

            if (data.heartRate && (data.heartRate < 60 * multiplier || data.heartRate > 100 / multiplier)) {
                alerts.push(`心率异常: ${data.heartRate} bpm`);
            }
            if (data.systolicBP && (data.systolicBP < 90 * multiplier || data.systolicBP > 140 / multiplier)) {
                alerts.push(`收缩压异常: ${data.systolicBP} mmHg`);
            }
            // 血氧异常检测：由于血氧稳定在正常范围，只在真正异常时报警
            if (data.spo2 && data.spo2 < 96 * multiplier) {
                alerts.push(`血氧饱和度偏低: ${data.spo2}%`);
            }
            // 体温异常检测：调整正常范围，减少误报
            if (data.temperature && (data.temperature < 36.0 * multiplier || data.temperature > 37.2 / multiplier)) {
                alerts.push(`体温异常: ${data.temperature}°C`);
            }
            // 血糖异常检测：允许在正常偏高范围内波动，只在超出7.0时报警
            if (data.bloodGlucose && (data.bloodGlucose < 3.9 * multiplier || data.bloodGlucose > 7.0 / multiplier)) {
                alerts.push(`血糖异常: ${data.bloodGlucose} mmol/L`);
            }

            if (alerts.length > 0) {
                showAlert(alerts.join(', '));
                alertCount++;
                document.getElementById('alertCount').textContent = alertCount;
            }
        }

        // 显示警报
        function showAlert(message) {
            const alertPanel = document.getElementById('alertPanel');
            const alertMessage = document.getElementById('alertMessage');
            
            alertMessage.textContent = message;
            alertPanel.classList.add('show');
            
            setTimeout(() => {
                alertPanel.classList.remove('show');
            }, 10000); // 10秒后自动隐藏
            
            addLogEntry('⚠️ 异常警报: ' + message);
        }

        // 初始化图表
        function initializeCharts() {
            // 趋势图
            const ctx1 = document.getElementById('trendChart').getContext('2d');
            trendChart = new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '心率 (bpm)',
                        data: [],
                        borderColor: '#2ed573',
                        backgroundColor: 'rgba(46, 213, 115, 0.1)',
                        tension: 0.4
                    }, {
                        label: '血氧 (%)',
                        data: [],
                        borderColor: '#4ecdc4',
                        backgroundColor: 'rgba(78, 205, 196, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }, {
                        label: '体温 (°C)',
                        data: [],
                        borderColor: '#f39c12',
                        backgroundColor: 'rgba(243, 156, 18, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y2'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间'
                            }
                        },
                        y: {
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '心率 (bpm)'
                            },
                            min: 50,
                            max: 120
                        },
                        y1: {
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '血氧 (%)'
                            },
                            min: 90,
                            max: 100,
                            grid: {
                                drawOnChartArea: false,
                            },
                        },
                        y2: {
                            display: false,
                            min: 35,
                            max: 39
                        }
                    }
                }
            });

            // ECG图
            const ctx2 = document.getElementById('ecgChart').getContext('2d');
            ecgChart = new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'ECG信号 (mV)',
                        data: [],
                        borderColor: '#2ed573',
                        backgroundColor: 'rgba(46, 213, 115, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        tension: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: false,
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间 (ms)'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '电压 (mV)'
                            },
                            min: -2,
                            max: 2
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        // 初始化波形图
        function initializeWaveformCharts() {
            // 心率波形图
            const heartRateCtx = document.getElementById('heartRateWave').getContext('2d');
            waveformCharts.heartRate = new Chart(heartRateCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '心率',
                        data: [],
                        borderColor: '#ff6b6b',
                        backgroundColor: 'rgba(255, 107, 107, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        tension: 0.4
                    }]
                },
                options: getWaveformOptions('心率 (bpm)', 50, 120)
            });

            // 血压波形图
            const bloodPressureCtx = document.getElementById('bloodPressureWave').getContext('2d');
            waveformCharts.bloodPressure = new Chart(bloodPressureCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '收缩压',
                        data: [],
                        borderColor: '#4ecdc4',
                        backgroundColor: 'rgba(78, 205, 196, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        tension: 0.4
                    }, {
                        label: '舒张压',
                        data: [],
                        borderColor: '#45b7d1',
                        backgroundColor: 'rgba(69, 183, 209, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        tension: 0.4
                    }]
                },
                options: getWaveformOptions('血压 (mmHg)', 60, 160)
            });

            // 血氧波形图
            const spo2Ctx = document.getElementById('spo2Wave').getContext('2d');
            waveformCharts.spo2 = new Chart(spo2Ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '血氧饱和度',
                        data: [],
                        borderColor: '#2ed573',
                        backgroundColor: 'rgba(46, 213, 115, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        tension: 0.4
                    }]
                },
                options: getWaveformOptions('血氧 (%)', 90, 100)
            });

            // 体温波形图
            const temperatureCtx = document.getElementById('temperatureWave').getContext('2d');
            waveformCharts.temperature = new Chart(temperatureCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '体温',
                        data: [],
                        borderColor: '#f39c12',
                        backgroundColor: 'rgba(243, 156, 18, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        tension: 0.4
                    }]
                },
                options: getWaveformOptions('体温 (°C)', 35, 39)
            });

            // 血糖波形图
            const bloodGlucoseCtx = document.getElementById('bloodGlucoseWave').getContext('2d');
            waveformCharts.bloodGlucose = new Chart(bloodGlucoseCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '血糖',
                        data: [],
                        borderColor: '#9b59b6',
                        backgroundColor: 'rgba(155, 89, 182, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        tension: 0.4
                    }]
                },
                options: getWaveformOptions('血糖 (mmol/L)', 3, 8)
            });

            // 呼吸波形图
            const respiratoryCtx = document.getElementById('respiratoryWave').getContext('2d');
            waveformCharts.respiratory = new Chart(respiratoryCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '呼吸频率',
                        data: [],
                        borderColor: '#e67e22',
                        backgroundColor: 'rgba(230, 126, 34, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        tension: 0.4
                    }]
                },
                options: getWaveformOptions('呼吸 (次/分)', 10, 25)
            });
        }

        // 获取波形图通用配置
        function getWaveformOptions(yAxisLabel, minY, maxY) {
            return {
                responsive: true,
                maintainAspectRatio: false,
                animation: false,
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '时间'
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: yAxisLabel
                        },
                        min: minY,
                        max: maxY
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            };
        }

        // 更新图表
        function updateCharts() {
            if (trendChart && monitoringData.timestamps.length > 0) {
                trendChart.data.labels = monitoringData.timestamps.map(t => t.toLocaleTimeString());
                trendChart.data.datasets[0].data = monitoringData.heartRate;
                trendChart.data.datasets[1].data = monitoringData.spo2;
                trendChart.data.datasets[2].data = monitoringData.temperature;
                trendChart.update('none');
            }

            if (ecgChart && ecgData.values.length > 0) {
                ecgChart.data.labels = ecgData.timestamps;
                ecgChart.data.datasets[0].data = ecgData.values;
                ecgChart.update('none');
            }

            // 更新波形图
            updateWaveformCharts();
        }

        // 更新波形图
        function updateWaveformCharts() {
            const timeLabels = waveformData.heartRate.timestamps.map(t => t.toLocaleTimeString());

            // 更新心率波形
            if (waveformCharts.heartRate && waveformData.heartRate.values.length > 0) {
                waveformCharts.heartRate.data.labels = timeLabels;
                waveformCharts.heartRate.data.datasets[0].data = waveformData.heartRate.values;
                waveformCharts.heartRate.update('none');
                document.getElementById('heartRateWaveValue').textContent = waveformData.heartRate.values[waveformData.heartRate.values.length - 1] + ' bpm';
            }

            // 更新血压波形
            if (waveformCharts.bloodPressure && waveformData.bloodPressure.systolic.length > 0) {
                waveformCharts.bloodPressure.data.labels = timeLabels;
                waveformCharts.bloodPressure.data.datasets[0].data = waveformData.bloodPressure.systolic;
                waveformCharts.bloodPressure.data.datasets[1].data = waveformData.bloodPressure.diastolic;
                waveformCharts.bloodPressure.update('none');
                const lastSystolic = waveformData.bloodPressure.systolic[waveformData.bloodPressure.systolic.length - 1];
                const lastDiastolic = waveformData.bloodPressure.diastolic[waveformData.bloodPressure.diastolic.length - 1];
                document.getElementById('bloodPressureWaveValue').textContent = `${lastSystolic}/${lastDiastolic}`;
            }

            // 更新血氧波形
            if (waveformCharts.spo2 && waveformData.spo2.values.length > 0) {
                waveformCharts.spo2.data.labels = timeLabels;
                waveformCharts.spo2.data.datasets[0].data = waveformData.spo2.values;
                waveformCharts.spo2.update('none');
                document.getElementById('spo2WaveValue').textContent = waveformData.spo2.values[waveformData.spo2.values.length - 1] + '%';
            }

            // 更新体温波形
            if (waveformCharts.temperature && waveformData.temperature.values.length > 0) {
                waveformCharts.temperature.data.labels = timeLabels;
                waveformCharts.temperature.data.datasets[0].data = waveformData.temperature.values;
                waveformCharts.temperature.update('none');
                document.getElementById('temperatureWaveValue').textContent = waveformData.temperature.values[waveformData.temperature.values.length - 1].toFixed(1) + '°C';
            }

            // 更新血糖波形
            if (waveformCharts.bloodGlucose && waveformData.bloodGlucose.values.length > 0) {
                waveformCharts.bloodGlucose.data.labels = timeLabels;
                waveformCharts.bloodGlucose.data.datasets[0].data = waveformData.bloodGlucose.values;
                waveformCharts.bloodGlucose.update('none');
                document.getElementById('bloodGlucoseWaveValue').textContent = waveformData.bloodGlucose.values[waveformData.bloodGlucose.values.length - 1].toFixed(1);
            }

            // 更新呼吸波形
            if (waveformCharts.respiratory && waveformData.respiratory.values.length > 0) {
                waveformCharts.respiratory.data.labels = timeLabels;
                waveformCharts.respiratory.data.datasets[0].data = waveformData.respiratory.values;
                waveformCharts.respiratory.update('none');
                document.getElementById('respiratoryWaveValue').textContent = waveformData.respiratory.values[waveformData.respiratory.values.length - 1] + ' 次/分';
            }
        }

        // 生成ECG数据
        function generateECGData(heartRate) {
            const now = Date.now();
            const rrInterval = 60000 / heartRate;
            
            for (let i = 0; i < 5; i++) {
                const t = (now + i * 100) % rrInterval;
                let value = 0;
                
                // 简化的ECG波形
                if (t < 50) {
                    value = 0.2 * Math.sin(Math.PI * t / 50);
                } else if (t < 150) {
                    value = 0;
                } else if (t < 200) {
                    if (t < 170) {
                        value = -0.3;
                    } else if (t < 185) {
                        value = 1.5;
                    } else {
                        value = -0.5;
                    }
                } else if (t < 400) {
                    value = 0.3 * Math.sin(Math.PI * (t - 200) / 200);
                }
                
                value += (Math.random() - 0.5) * 0.05;
                
                ecgData.values.push(value);
                ecgData.timestamps.push(t);
            }
            
            if (ecgData.values.length > 100) {
                ecgData.values = ecgData.values.slice(-100);
                ecgData.timestamps = ecgData.timestamps.slice(-100);
            }
        }

        // 导出数据
        function exportData() {
            const data = {
                monitoringData: monitoringData,
                ecgData: ecgData,
                settings: {
                    samplingRate: document.getElementById('samplingRate').value,
                    duration: document.getElementById('monitoringDuration').value,
                    alertThreshold: document.getElementById('alertThreshold').value
                },
                statistics: {
                    startTime: startTime,
                    dataPoints: dataPoints,
                    alertCount: alertCount,
                    monitoringDuration: startTime ? Date.now() - startTime : 0
                },
                exportTime: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `long_term_monitoring_${new Date().toISOString().slice(0, 19)}.json`;
            a.click();
            
            addLogEntry('监测数据导出完成');
        }

        // 显示监测总结
        function showMonitoringSummary() {
            const duration = startTime ? Date.now() - startTime : 0;
            const hours = Math.floor(duration / 3600000);
            const minutes = Math.floor((duration % 3600000) / 60000);
            
            const summary = `
                监测总结：
                - 监测时长: ${hours}小时${minutes}分钟
                - 数据点数: ${dataPoints}
                - 异常次数: ${alertCount}
                - 平均心率: ${monitoringData.heartRate.length > 0 ? Math.round(monitoringData.heartRate.reduce((a, b) => a + b, 0) / monitoringData.heartRate.length) : 0} bpm
            `;
            
            alert(summary);
            addLogEntry('监测总结已生成');
        }

        // 日志系统
        function addLogEntry(message) {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span><span>${message}</span>`;
            
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
            
            const entries = logPanel.querySelectorAll('.log-entry');
            if (entries.length > 50) {
                entries[0].remove();
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                goBack();
            } else if (e.key === ' ') {
                e.preventDefault();
                if (!isMonitoring) {
                    startMonitoring();
                } else {
                    pauseMonitoring();
                }
            } else if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                exportData();
            }
        });

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            initializeWaveformCharts();
            addLogEntry('长期监测系统已初始化');
        });

        // 页面关闭前确认
        window.addEventListener('beforeunload', function(e) {
            if (isMonitoring) {
                e.preventDefault();
                e.returnValue = '监测正在进行中，确定要离开吗？';
            }
        });
    </script>
</body>
</html>
