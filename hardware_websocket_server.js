#!/usr/bin/env node

/**
 * 医疗设备硬件数据WebSocket服务器
 * 用于模拟真实硬件设备向Web系统发送生理监测数据
 * 
 * 使用方法:
 * 1. 安装依赖: npm install ws
 * 2. 运行服务器: node hardware_websocket_server.js
 * 3. 在Web界面中点击"WebSocket连接"按钮连接
 */

const WebSocket = require('ws');

// 服务器配置
const SERVER_PORT = 8080;
const DATA_INTERVAL = 2000; // 数据发送间隔(毫秒)

// 创建WebSocket服务器
const wss = new WebSocket.Server({ 
    port: SERVER_PORT,
    host: 'localhost'
});

console.log(`🏥 医疗设备WebSocket服务器启动`);
console.log(`📡 监听地址: ws://localhost:${SERVER_PORT}/medical-data`);
console.log(`⏱️  数据发送间隔: ${DATA_INTERVAL}ms`);

// 生理数据基准值
const baseVitalSigns = {
    heartRate: 75,
    systolicBP: 120,
    diastolicBP: 80,
    spo2: 98,
    temperature: 36.5,
    bloodGlucose: 5.2,
    respiratoryRate: 16,
    qtInterval: 400,
    cvp: 8
};

// 生成真实的生理数据
function generateRealisticVitalSigns() {
    const now = new Date();
    const timeOfDay = now.getHours() + now.getMinutes() / 60;
    
    // 根据时间调整基准值（模拟昼夜节律）
    const circadianFactor = 1 + 0.1 * Math.sin((timeOfDay - 6) * Math.PI / 12);
    
    return {
        heartRate: Math.round(baseVitalSigns.heartRate * circadianFactor + (Math.random() - 0.5) * 4),
        systolicBP: Math.round(baseVitalSigns.systolicBP + (Math.random() - 0.5) * 8),
        diastolicBP: Math.round(baseVitalSigns.diastolicBP + (Math.random() - 0.5) * 6),
        spo2: Math.round(baseVitalSigns.spo2 + (Math.random() - 0.5) * 2),
        temperature: Math.round((baseVitalSigns.temperature + (Math.random() - 0.5) * 0.8) * 10) / 10,
        bloodGlucose: Math.round((baseVitalSigns.bloodGlucose + (Math.random() - 0.5) * 1.0) * 10) / 10,
        respiratoryRate: Math.round(baseVitalSigns.respiratoryRate + (Math.random() - 0.5) * 3),
        qtInterval: Math.round(baseVitalSigns.qtInterval + (Math.random() - 0.5) * 20),
        cvp: Math.round(baseVitalSigns.cvp + (Math.random() - 0.5) * 2),
        timestamp: Date.now(),
        deviceId: 'MEDICAL_DEVICE_001',
        batteryLevel: Math.round(85 + Math.random() * 15), // 85-100%
        signalStrength: Math.round(80 + Math.random() * 20) // 80-100%
    };
}

// 生成自定义协议格式数据
function generateCustomProtocolData() {
    const data = generateRealisticVitalSigns();
    return `HR:${data.heartRate},BP:${data.systolicBP}/${data.diastolicBP},SPO2:${data.spo2},TEMP:${data.temperature},GLUCOSE:${data.bloodGlucose},RR:${data.respiratoryRate},QT:${data.qtInterval},CVP:${data.cvp}`;
}

// 生成二进制数据
function generateBinaryData() {
    const data = generateRealisticVitalSigns();
    const buffer = Buffer.alloc(20);
    
    // 数据包头部
    buffer.writeUInt16LE(0xABCD, 0);
    
    // 生理参数
    buffer.writeUInt16LE(data.heartRate, 2);
    buffer.writeUInt16LE(data.systolicBP, 4);
    buffer.writeUInt16LE(data.diastolicBP, 6);
    buffer.writeUInt8(data.spo2, 8);
    buffer.writeFloatLE(data.temperature, 9);
    buffer.writeFloatLE(data.bloodGlucose, 13);
    
    return buffer;
}

// 客户端连接管理
const clients = new Set();

// 处理WebSocket连接
wss.on('connection', function connection(ws, req) {
    const clientId = `CLIENT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    clients.add(ws);
    
    console.log(`🔗 新客户端连接: ${clientId}`);
    console.log(`👥 当前连接数: ${clients.size}`);
    
    // 发送连接确认
    ws.send(JSON.stringify({
        type: 'connection_established',
        clientId: clientId,
        serverTime: new Date().toISOString(),
        message: '硬件设备连接成功'
    }));
    
    // 处理客户端消息
    ws.on('message', function incoming(message) {
        try {
            const data = JSON.parse(message);
            console.log(`📥 收到客户端消息:`, data);
            
            switch (data.type) {
                case 'heartbeat':
                    // 响应心跳
                    ws.send(JSON.stringify({
                        type: 'heartbeat_response',
                        timestamp: Date.now()
                    }));
                    break;
                    
                case 'command':
                    handleCommand(ws, data.command);
                    break;
                    
                default:
                    console.log(`❓ 未知消息类型: ${data.type}`);
            }
        } catch (error) {
            console.error(`❌ 消息解析错误:`, error);
        }
    });
    
    // 处理连接关闭
    ws.on('close', function close() {
        clients.delete(ws);
        console.log(`🔌 客户端断开连接: ${clientId}`);
        console.log(`👥 当前连接数: ${clients.size}`);
    });
    
    // 处理连接错误
    ws.on('error', function error(err) {
        console.error(`❌ WebSocket错误:`, err);
        clients.delete(ws);
    });
});

// 处理客户端命令
function handleCommand(ws, command) {
    console.log(`📤 处理命令: ${command}`);
    
    switch (command) {
        case 'GET_STATUS':
            ws.send(JSON.stringify({
                type: 'status_response',
                status: 'online',
                deviceInfo: {
                    model: 'PM-2024',
                    version: '1.0.0',
                    serialNumber: 'MD001234567',
                    calibrationDate: '2024-01-15'
                },
                timestamp: Date.now()
            }));
            break;
            
        case 'GET_DATA':
            // 立即发送一次数据
            broadcastVitalSigns();
            break;
            
        case 'CALIBRATE':
            ws.send(JSON.stringify({
                type: 'calibration_response',
                status: 'success',
                message: '设备校准完成',
                timestamp: Date.now()
            }));
            break;
            
        default:
            ws.send(JSON.stringify({
                type: 'command_response',
                command: command,
                status: 'unknown_command',
                timestamp: Date.now()
            }));
    }
}

// 广播生理数据到所有连接的客户端
function broadcastVitalSigns() {
    if (clients.size === 0) return;
    
    // 生成不同格式的数据
    const jsonData = generateRealisticVitalSigns();
    const customData = generateCustomProtocolData();
    const binaryData = generateBinaryData();
    
    // 随机选择数据格式
    const formats = ['json', 'custom', 'binary'];
    const selectedFormat = formats[Math.floor(Math.random() * formats.length)];
    
    let dataToSend;
    switch (selectedFormat) {
        case 'json':
            dataToSend = JSON.stringify(jsonData);
            break;
        case 'custom':
            dataToSend = customData;
            break;
        case 'binary':
            dataToSend = binaryData;
            break;
    }
    
    console.log(`📊 广播生理数据 (${selectedFormat}格式) 到 ${clients.size} 个客户端`);
    console.log(`   心率: ${jsonData.heartRate} 次/分, 血压: ${jsonData.systolicBP}/${jsonData.diastolicBP} mmHg`);
    
    // 发送数据到所有客户端
    clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            try {
                if (selectedFormat === 'binary') {
                    client.send(dataToSend);
                } else {
                    client.send(dataToSend);
                }
            } catch (error) {
                console.error(`❌ 数据发送失败:`, error);
                clients.delete(client);
            }
        }
    });
}

// 定期发送生理数据
setInterval(broadcastVitalSigns, DATA_INTERVAL);

// 服务器错误处理
wss.on('error', function error(err) {
    console.error(`❌ 服务器错误:`, err);
});

// 优雅关闭
process.on('SIGINT', function() {
    console.log('\n🛑 正在关闭服务器...');
    
    // 通知所有客户端服务器即将关闭
    clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
                type: 'server_shutdown',
                message: '服务器即将关闭',
                timestamp: Date.now()
            }));
            client.close();
        }
    });
    
    wss.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

console.log('🚀 服务器运行中...');
console.log('💡 提示: 按 Ctrl+C 停止服务器');
