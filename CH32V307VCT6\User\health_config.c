/*
 * health_config.c
 *
 * 健康监测系统配置管理模块实现
 */

#include "health_config.h"
#include "debug.h"
#include <string.h>

// 全局配置变量定义
HEALTH_CONFIG_T g_health_config;

/*********************************************************************
 * @fn      CONFIG_Init
 *
 * @brief   初始化健康监测系统配置，设置默认值
 *
 * @return  none
 */
void CONFIG_Init(void)
{
    // 清零配置结构体
    memset(&g_health_config, 0, sizeof(HEALTH_CONFIG_T));
    
    // 心率传感器阈值配置 (基于现有main.c中的60-100 BPM)
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_HEART_RATE].un_min_normal = 60;
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_HEART_RATE].un_max_normal = 100;
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_HEART_RATE].un_min_warning = 50;
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_HEART_RATE].un_max_warning = 120;
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_HEART_RATE].un_min_danger = 40;
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_HEART_RATE].un_max_danger = 150;
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_HEART_RATE].ch_enabled = 1;
    
    // 血氧传感器阈值配置 (基于现有main.c中的<95%)
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_SPO2].un_min_normal = 95;
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_SPO2].un_max_normal = 100;
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_SPO2].un_min_warning = 90;
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_SPO2].un_max_warning = 100;
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_SPO2].un_min_danger = 80;
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_SPO2].un_max_danger = 100;
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_SPO2].ch_enabled = 1;
    
    // 体温传感器阈值配置 (异常检测阈值保持不变，基于原始标准)
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_TEMPERATURE].un_min_normal = 360; // 36.0°C * 10
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_TEMPERATURE].un_max_normal = 373; // 37.3°C * 10
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_TEMPERATURE].un_min_warning = 350; // 35.0°C * 10
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_TEMPERATURE].un_max_warning = 380; // 38.0°C * 10
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_TEMPERATURE].un_min_danger = 320; // 32.0°C * 10
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_TEMPERATURE].un_max_danger = 400; // 40.0°C * 10
    g_health_config.ast_sensor_threshold[SENSOR_TYPE_TEMPERATURE].ch_enabled = 1;
    
    // 异常检测参数配置
    for(u8 i = 0; i < SENSOR_TYPE_MAX; i++) {
        g_health_config.ast_anomaly_config[i].un_window_size = 10;          // 10个数据点的滑动窗口
        g_health_config.ast_anomaly_config[i].un_change_rate_threshold = 200; // 20%变化率阈值
        g_health_config.ast_anomaly_config[i].un_std_dev_threshold = 50;    // 标准差阈值
        g_health_config.ast_anomaly_config[i].un_trend_threshold = 30;      // 趋势检测阈值
        g_health_config.ast_anomaly_config[i].un_outlier_factor = 2;        // 2倍标准差离群点检测
        g_health_config.ast_anomaly_config[i].ch_enabled = 1;
    }
    
    // 报警配置
    g_health_config.ast_alarm_config[ALARM_LEVEL_WARNING].un_beep_frequency = 1000;   // 1KHz
    g_health_config.ast_alarm_config[ALARM_LEVEL_WARNING].un_beep_duration = 100;     // 100ms
    g_health_config.ast_alarm_config[ALARM_LEVEL_WARNING].un_beep_interval = 2000;    // 2s间隔
    g_health_config.ast_alarm_config[ALARM_LEVEL_WARNING].un_repeat_count = 1;
    g_health_config.ast_alarm_config[ALARM_LEVEL_WARNING].ch_enabled = 1;
    
    g_health_config.ast_alarm_config[ALARM_LEVEL_ATTENTION].un_beep_frequency = 1500; // 1.5KHz
    g_health_config.ast_alarm_config[ALARM_LEVEL_ATTENTION].un_beep_duration = 200;   // 200ms
    g_health_config.ast_alarm_config[ALARM_LEVEL_ATTENTION].un_beep_interval = 1000;  // 1s间隔
    g_health_config.ast_alarm_config[ALARM_LEVEL_ATTENTION].un_repeat_count = 2;
    g_health_config.ast_alarm_config[ALARM_LEVEL_ATTENTION].ch_enabled = 1;
    
    g_health_config.ast_alarm_config[ALARM_LEVEL_DANGER].un_beep_frequency = 2000;    // 2KHz
    g_health_config.ast_alarm_config[ALARM_LEVEL_DANGER].un_beep_duration = 300;      // 300ms
    g_health_config.ast_alarm_config[ALARM_LEVEL_DANGER].un_beep_interval = 500;      // 0.5s间隔
    g_health_config.ast_alarm_config[ALARM_LEVEL_DANGER].un_repeat_count = 3;
    g_health_config.ast_alarm_config[ALARM_LEVEL_DANGER].ch_enabled = 1;
    
    g_health_config.ast_alarm_config[ALARM_LEVEL_EMERGENCY].un_beep_frequency = 3000; // 3KHz
    g_health_config.ast_alarm_config[ALARM_LEVEL_EMERGENCY].un_beep_duration = 500;   // 500ms
    g_health_config.ast_alarm_config[ALARM_LEVEL_EMERGENCY].un_beep_interval = 200;   // 0.2s间隔
    g_health_config.ast_alarm_config[ALARM_LEVEL_EMERGENCY].un_repeat_count = 5;
    g_health_config.ast_alarm_config[ALARM_LEVEL_EMERGENCY].ch_enabled = 1;
    
    // 系统参数配置 (基于现有main.c中的500缓冲区大小)
    g_health_config.un_data_buffer_size = 500;
    g_health_config.un_sampling_interval = 100;  // 100ms采样间隔
    g_health_config.ch_system_enabled = 1;
    g_health_config.ch_debug_mode = 0;
    
    printf("CONFIG: Health monitoring system initialized\r\n");
}

/*********************************************************************
 * @fn      CONFIG_SetSensorThreshold
 *
 * @brief   设置传感器阈值配置
 *
 * @param   sensor_type - 传感器类型
 *          pst_threshold - 阈值配置指针
 *
 * @return  0-成功, 1-失败
 */
u8 CONFIG_SetSensorThreshold(SENSOR_TYPE_E sensor_type, SENSOR_THRESHOLD_T *pst_threshold)
{
    if(sensor_type >= SENSOR_TYPE_MAX || pst_threshold == NULL) {
        return 1;
    }
    
    memcpy(&g_health_config.ast_sensor_threshold[sensor_type], pst_threshold, sizeof(SENSOR_THRESHOLD_T));
    return 0;
}

/*********************************************************************
 * @fn      CONFIG_GetSensorThreshold
 *
 * @brief   获取传感器阈值配置
 *
 * @param   sensor_type - 传感器类型
 *          pst_threshold - 阈值配置指针
 *
 * @return  0-成功, 1-失败
 */
u8 CONFIG_GetSensorThreshold(SENSOR_TYPE_E sensor_type, SENSOR_THRESHOLD_T *pst_threshold)
{
    if(sensor_type >= SENSOR_TYPE_MAX || pst_threshold == NULL) {
        return 1;
    }

    memcpy(pst_threshold, &g_health_config.ast_sensor_threshold[sensor_type], sizeof(SENSOR_THRESHOLD_T));
    return 0;
}

/*********************************************************************
 * @fn      CONFIG_SetAnomalyConfig
 *
 * @brief   设置异常检测配置
 *
 * @param   sensor_type - 传感器类型
 *          pst_config - 异常检测配置指针
 *
 * @return  0-成功, 1-失败
 */
u8 CONFIG_SetAnomalyConfig(SENSOR_TYPE_E sensor_type, ANOMALY_DETECT_CONFIG_T *pst_config)
{
    if(sensor_type >= SENSOR_TYPE_MAX || pst_config == NULL) {
        return 1;
    }

    memcpy(&g_health_config.ast_anomaly_config[sensor_type], pst_config, sizeof(ANOMALY_DETECT_CONFIG_T));
    return 0;
}

/*********************************************************************
 * @fn      CONFIG_GetAnomalyConfig
 *
 * @brief   获取异常检测配置
 *
 * @param   sensor_type - 传感器类型
 *          pst_config - 异常检测配置指针
 *
 * @return  0-成功, 1-失败
 */
u8 CONFIG_GetAnomalyConfig(SENSOR_TYPE_E sensor_type, ANOMALY_DETECT_CONFIG_T *pst_config)
{
    if(sensor_type >= SENSOR_TYPE_MAX || pst_config == NULL) {
        return 1;
    }

    memcpy(pst_config, &g_health_config.ast_anomaly_config[sensor_type], sizeof(ANOMALY_DETECT_CONFIG_T));
    return 0;
}

/*********************************************************************
 * @fn      CONFIG_SetAlarmConfig
 *
 * @brief   设置报警配置
 *
 * @param   alarm_level - 报警级别
 *          pst_config - 报警配置指针
 *
 * @return  0-成功, 1-失败
 */
u8 CONFIG_SetAlarmConfig(ALARM_LEVEL_E alarm_level, ALARM_CONFIG_T *pst_config)
{
    if(alarm_level >= ALARM_LEVEL_MAX || pst_config == NULL) {
        return 1;
    }

    memcpy(&g_health_config.ast_alarm_config[alarm_level], pst_config, sizeof(ALARM_CONFIG_T));
    return 0;
}

/*********************************************************************
 * @fn      CONFIG_GetAlarmConfig
 *
 * @brief   获取报警配置
 *
 * @param   alarm_level - 报警级别
 *          pst_config - 报警配置指针
 *
 * @return  0-成功, 1-失败
 */
u8 CONFIG_GetAlarmConfig(ALARM_LEVEL_E alarm_level, ALARM_CONFIG_T *pst_config)
{
    if(alarm_level >= ALARM_LEVEL_MAX || pst_config == NULL) {
        return 1;
    }

    memcpy(pst_config, &g_health_config.ast_alarm_config[alarm_level], sizeof(ALARM_CONFIG_T));
    return 0;
}

/*********************************************************************
 * @fn      CONFIG_SetSystemParam
 *
 * @brief   设置系统参数
 *
 * @param   buffer_size - 数据缓冲区大小
 *          sampling_interval - 采样间隔(ms)
 *
 * @return  0-成功, 1-失败
 */
u8 CONFIG_SetSystemParam(u16 buffer_size, u16 sampling_interval)
{
    if(buffer_size == 0 || sampling_interval == 0) {
        return 1;
    }

    g_health_config.un_data_buffer_size = buffer_size;
    g_health_config.un_sampling_interval = sampling_interval;
    return 0;
}

/*********************************************************************
 * @fn      CONFIG_GetSystemParam
 *
 * @brief   获取系统参数
 *
 * @param   pun_buffer_size - 数据缓冲区大小指针
 *          pun_sampling_interval - 采样间隔指针
 *
 * @return  0-成功, 1-失败
 */
u8 CONFIG_GetSystemParam(u16 *pun_buffer_size, u16 *pun_sampling_interval)
{
    if(pun_buffer_size == NULL || pun_sampling_interval == NULL) {
        return 1;
    }

    *pun_buffer_size = g_health_config.un_data_buffer_size;
    *pun_sampling_interval = g_health_config.un_sampling_interval;
    return 0;
}

/*********************************************************************
 * @fn      CONFIG_EnableSystem
 *
 * @brief   系统总开关
 *
 * @param   ch_enable - 1-启用, 0-禁用
 *
 * @return  0-成功
 */
u8 CONFIG_EnableSystem(u8 ch_enable)
{
    g_health_config.ch_system_enabled = ch_enable ? 1 : 0;
    return 0;
}

/*********************************************************************
 * @fn      CONFIG_EnableDebugMode
 *
 * @brief   调试模式开关
 *
 * @param   ch_enable - 1-启用, 0-禁用
 *
 * @return  0-成功
 */
u8 CONFIG_EnableDebugMode(u8 ch_enable)
{
    g_health_config.ch_debug_mode = ch_enable ? 1 : 0;
    return 0;
}

/*********************************************************************
 * @fn      CONFIG_IsSystemEnabled
 *
 * @brief   检查系统是否启用
 *
 * @return  1-启用, 0-禁用
 */
u8 CONFIG_IsSystemEnabled(void)
{
    return g_health_config.ch_system_enabled;
}

/*********************************************************************
 * @fn      CONFIG_IsDebugModeEnabled
 *
 * @brief   检查调试模式是否启用
 *
 * @return  1-启用, 0-禁用
 */
u8 CONFIG_IsDebugModeEnabled(void)
{
    return g_health_config.ch_debug_mode;
}

/*********************************************************************
 * @fn      CONFIG_PrintConfig
 *
 * @brief   打印当前配置信息(调试用)
 *
 * @return  none
 */
void CONFIG_PrintConfig(void)
{
    if(!CONFIG_IsDebugModeEnabled()) {
        return;
    }

    printf("=== Health Monitor Configuration ===\r\n");
    printf("System Enabled: %d\r\n", g_health_config.ch_system_enabled);
    printf("Debug Mode: %d\r\n", g_health_config.ch_debug_mode);
    printf("Buffer Size: %d\r\n", g_health_config.un_data_buffer_size);
    printf("Sampling Interval: %d ms\r\n", g_health_config.un_sampling_interval);

    // 打印传感器阈值配置
    const char* sensor_names[] = {"Heart Rate", "SpO2", "Temperature"};
    for(u8 i = 0; i < SENSOR_TYPE_MAX; i++) {
        printf("--- %s Sensor ---\r\n", sensor_names[i]);
        printf("  Normal: %d - %d\r\n",
               g_health_config.ast_sensor_threshold[i].un_min_normal,
               g_health_config.ast_sensor_threshold[i].un_max_normal);
        printf("  Warning: %d - %d\r\n",
               g_health_config.ast_sensor_threshold[i].un_min_warning,
               g_health_config.ast_sensor_threshold[i].un_max_warning);
        printf("  Danger: %d - %d\r\n",
               g_health_config.ast_sensor_threshold[i].un_min_danger,
               g_health_config.ast_sensor_threshold[i].un_max_danger);
        printf("  Enabled: %d\r\n", g_health_config.ast_sensor_threshold[i].ch_enabled);

        printf("  Anomaly Detection:\r\n");
        printf("    Window Size: %d\r\n", g_health_config.ast_anomaly_config[i].un_window_size);
        printf("    Change Rate Threshold: %d\r\n", g_health_config.ast_anomaly_config[i].un_change_rate_threshold);
        printf("    Enabled: %d\r\n", g_health_config.ast_anomaly_config[i].ch_enabled);
    }

    // 打印报警配置
    const char* alarm_names[] = {"None", "Warning", "Attention", "Danger", "Emergency"};
    for(u8 i = 1; i < ALARM_LEVEL_MAX; i++) {  // 跳过NONE级别
        printf("--- %s Alarm ---\r\n", alarm_names[i]);
        printf("  Frequency: %d Hz\r\n", g_health_config.ast_alarm_config[i].un_beep_frequency);
        printf("  Duration: %d ms\r\n", g_health_config.ast_alarm_config[i].un_beep_duration);
        printf("  Interval: %d ms\r\n", g_health_config.ast_alarm_config[i].un_beep_interval);
        printf("  Repeat: %d times\r\n", g_health_config.ast_alarm_config[i].un_repeat_count);
        printf("  Enabled: %d\r\n", g_health_config.ast_alarm_config[i].ch_enabled);
    }

    printf("=== End Configuration ===\r\n");
}
