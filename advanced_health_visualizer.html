<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级生理数据可视化系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            color: #333;
            font-size: 2.2em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .btn.active {
            background: linear-gradient(45deg, #2e7d32, #4caf50);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .data-panel {
            display: grid;
            grid-template-rows: repeat(3, 1fr);
            gap: 15px;
        }

        .data-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .data-card:hover {
            transform: translateY(-3px);
        }

        .data-card .icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .data-card .value {
            font-size: 2.2em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .data-card .label {
            color: #666;
            font-size: 1em;
            margin-bottom: 8px;
        }

        .data-card .range {
            color: #999;
            font-size: 0.85em;
        }

        .heart-rate .icon { color: #e74c3c; }
        .heart-rate .value { color: #e74c3c; }

        .spo2 .icon { color: #3498db; }
        .spo2 .value { color: #3498db; }

        .temperature .icon { color: #f39c12; }
        .temperature .value { color: #f39c12; }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .chart-container h3 {
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .individual-chart {
            background: white;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .individual-chart h4 {
            margin-bottom: 10px;
            color: #333;
            text-align: center;
            font-size: 1.1em;
        }

        .individual-chart-wrapper {
            position: relative;
            height: 200px;
        }

        .status-bar {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-indicator.active {
            background: #4caf50;
        }

        .status-indicator.inactive {
            background: #ccc;
            animation: none;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pulse-animation {
            animation: heartbeat 1s infinite;
        }

        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .stats-panel {
            background: white;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            text-align: center;
        }

        .stat-item {
            padding: 10px;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 高级生理数据可视化系统</h1>
            <p>实时生成与CH32V307VCT6相同的生理数据，提供专业级可视化分析</p>
        </div>

        <div class="status-bar">
            <div>
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">系统已就绪</span>
            </div>
            <div>
                <span>数据点: <strong id="dataCount">0</strong></span>
                <span style="margin-left: 20px;">运行时间: <strong id="runTime">00:00</strong></span>
            </div>
        </div>

        <div class="control-panel">
            <button class="btn" id="startBtn" onclick="startSimulation()">开始模拟</button>
            <button class="btn" id="stopBtn" onclick="stopSimulation()" disabled>停止模拟</button>
            <button class="btn" onclick="resetData()">重置数据</button>
            <button class="btn" onclick="exportData()">导出数据</button>
            <button class="btn" onclick="toggleFullscreen()">全屏显示</button>
        </div>

        <div class="main-grid">
            <div class="data-panel">
                <div class="data-card heart-rate">
                    <div class="icon">💓</div>
                    <div class="value" id="heartRate">--</div>
                    <div class="label">心率 (次/分)</div>
                    <div class="range">正常范围: 55-105</div>
                </div>
                <div class="data-card spo2">
                    <div class="icon">🫁</div>
                    <div class="value" id="spo2">--</div>
                    <div class="label">血氧饱和度 (%)</div>
                    <div class="range">正常范围: 80-100</div>
                </div>
                <div class="data-card temperature">
                    <div class="icon">🌡️</div>
                    <div class="value" id="temperature">--</div>
                    <div class="label">体温 (°C)</div>
                    <div class="range">正常范围: 35.0-40.0</div>
                </div>
            </div>

            <div class="chart-container">
                <h3>📈 综合实时数据图表</h3>
                <div class="chart-wrapper">
                    <canvas id="mainChart"></canvas>
                </div>
            </div>
        </div>

        <div class="stats-panel">
            <h3 style="text-align: center; margin-bottom: 15px;">📊 数据统计</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="avgHeartRate">--</div>
                    <div class="stat-label">平均心率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="maxHeartRate">--</div>
                    <div class="stat-label">最高心率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="minHeartRate">--</div>
                    <div class="stat-label">最低心率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avgSpO2">--</div>
                    <div class="stat-label">平均血氧</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avgTemp">--</div>
                    <div class="stat-label">平均体温</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="currentMode">正常</div>
                    <div class="stat-label">当前模式</div>
                </div>
            </div>
        </div>

        <div class="charts-grid">
            <div class="individual-chart">
                <h4>💓 心率趋势</h4>
                <div class="individual-chart-wrapper">
                    <canvas id="heartRateChart"></canvas>
                </div>
            </div>
            <div class="individual-chart">
                <h4>🫁 血氧趋势</h4>
                <div class="individual-chart-wrapper">
                    <canvas id="spo2Chart"></canvas>
                </div>
            </div>
            <div class="individual-chart">
                <h4>🌡️ 体温趋势</h4>
                <div class="individual-chart-wrapper">
                    <canvas id="temperatureChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟CH32V307VCT6的数据生成逻辑
        class AdvancedHealthDataSimulator {
            constructor() {
                this.isRunning = false;
                this.interval = null;
                this.startTime = null;
                this.dataCount = 0;
                
                // 心率模拟参数（与CH32V307VCT6完全相同）
                this.HR_BASE_VALUE = 75;
                this.HR_MIN_VALUE = 55;
                this.HR_MAX_VALUE = 105;
                this.HR_CHANGE_PROBABILITY = 95;
                this.HR_MAX_CHANGE = 3;
                this.simulatedHeartRate = this.HR_BASE_VALUE;
                this.randomSeed = 12345;
                
                // 心率影响因子
                this.stressLevel = 0;
                this.activityIntensity = 0;
                this.fatigueLevel = 0;
                this.dailyCycleCounter = 0;
                this.hrSimulationMode = 2;
                this.modeChangeTimer = 0;
                this.changeCounter = 0;
                
                // 数据存储
                this.heartRateData = [];
                this.spo2Data = [];
                this.temperatureData = [];
                this.timeLabels = [];
                this.maxDataPoints = 100;
                
                // 统计数据
                this.stats = {
                    heartRate: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                    spo2: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                    temperature: { sum: 0, count: 0, min: Infinity, max: -Infinity }
                };
                
                this.initCharts();
            }
        }
        
        // 全局变量
        let simulator = new AdvancedHealthDataSimulator();
        let mainChart, heartRateChart, spo2Chart, temperatureChart;
        
            // 简单随机数生成器（与CH32V307VCT6相同）
            simpleRandom() {
                this.randomSeed = (this.randomSeed * ********** + 12345) & 0x7FFFFFFF;
                return this.randomSeed;
            }

            // 获取范围内随机数
            getRandomInRange(min, max) {
                return min + (this.simpleRandom() % (max - min + 1));
            }

            // 应用自然变化
            applyNaturalVariation(value) {
                const variation = this.getRandomInRange(-2, 2);
                let result = value + variation;
                if (result < this.HR_MIN_VALUE) result = this.HR_MIN_VALUE;
                if (result > this.HR_MAX_VALUE) result = this.HR_MAX_VALUE;
                return result;
            }

            // 更新心率影响因子
            updateHeartRateFactors() {
                this.dailyCycleCounter++;
                const hourOfDay = (this.dailyCycleCounter / 60) % 24;

                if (hourOfDay >= 22 || hourOfDay <= 6) {
                    this.stressLevel = Math.max(0, this.stressLevel - 1);
                    this.activityIntensity = 0;
                    this.fatigueLevel = Math.min(10, this.fatigueLevel + 1);
                } else if (hourOfDay >= 7 && hourOfDay <= 9) {
                    this.stressLevel = Math.min(10, this.stressLevel + 2);
                    this.activityIntensity = Math.min(10, this.activityIntensity + 3);
                }

                if (this.getRandomInRange(0, 100) < 20) {
                    this.stressLevel = Math.max(0, Math.min(10, this.stressLevel + this.getRandomInRange(-2, 2)));
                    this.activityIntensity = Math.max(0, Math.min(10, this.activityIntensity + this.getRandomInRange(-1, 1)));
                    this.fatigueLevel = Math.max(0, Math.min(10, this.fatigueLevel + this.getRandomInRange(-1, 1)));
                }
            }

            // 心率模拟
            simulateHeartRate() {
                this.updateHeartRateFactors();

                this.modeChangeTimer++;
                if (this.modeChangeTimer > 80) {
                    this.modeChangeTimer = 0;
                    this.hrSimulationMode = this.getRandomInRange(0, 4);
                }

                this.changeCounter++;
                if (this.changeCounter < 1) return this.simulatedHeartRate;
                this.changeCounter = 0;

                if (this.getRandomInRange(0, 100) > this.HR_CHANGE_PROBABILITY) {
                    return this.simulatedHeartRate;
                }

                let targetHR = this.HR_BASE_VALUE;
                switch (this.hrSimulationMode) {
                    case 0: targetHR = 60; break; // 睡眠
                    case 1: targetHR = 70; break; // 休息
                    case 2: targetHR = 75; break; // 正常
                    case 3: targetHR = 85; break; // 活跃
                    case 4: targetHR = 95; break; // 运动
                }

                targetHR += this.stressLevel * 2;
                targetHR += this.activityIntensity * 1.5;
                targetHR -= this.fatigueLevel * 0.5;

                const diff = targetHR - this.simulatedHeartRate;
                let change = 0;

                if (Math.abs(diff) > 10) {
                    change = diff > 0 ? this.HR_MAX_CHANGE : -this.HR_MAX_CHANGE;
                } else if (Math.abs(diff) > 5) {
                    change = diff > 0 ? 2 : -2;
                } else {
                    change = this.getRandomInRange(-this.HR_MAX_CHANGE, this.HR_MAX_CHANGE);
                }

                let newHR = this.simulatedHeartRate + change;
                if (newHR < this.HR_MIN_VALUE) newHR = this.HR_MIN_VALUE;
                if (newHR > this.HR_MAX_VALUE) newHR = this.HR_MAX_VALUE;

                this.simulatedHeartRate = newHR;
                return this.applyNaturalVariation(this.simulatedHeartRate);
            }

            // 模拟血氧数据
            simulateSpO2() {
                const baseSpO2 = 94;
                const variation = this.getRandomInRange(-4, 6);
                let spo2 = baseSpO2 + variation;
                if (spo2 < 80) spo2 = 80;
                if (spo2 > 100) spo2 = 100;
                return spo2;
            }

            // 模拟体温数据
            simulateTemperature() {
                const baseTemp = 36.5;
                const variation = (this.getRandomInRange(-10, 10) / 10.0);
                let temp = baseTemp + variation;
                if (temp < 35.0) temp = 35.0;
                if (temp > 40.0) temp = 40.0;
                return parseFloat(temp.toFixed(1));
            }

            // 生成完整健康数据
            generateHealthData() {
                const heartRate = this.simulateHeartRate();
                const spo2 = this.simulateSpO2();
                const temperature = this.simulateTemperature();

                this.updateStats(heartRate, spo2, temperature);
                return { heartRate, spo2, temperature };
            }

            // 更新统计数据
            updateStats(heartRate, spo2, temperature) {
                // 心率统计
                this.stats.heartRate.sum += heartRate;
                this.stats.heartRate.count++;
                this.stats.heartRate.min = Math.min(this.stats.heartRate.min, heartRate);
                this.stats.heartRate.max = Math.max(this.stats.heartRate.max, heartRate);

                // 血氧统计
                this.stats.spo2.sum += spo2;
                this.stats.spo2.count++;
                this.stats.spo2.min = Math.min(this.stats.spo2.min, spo2);
                this.stats.spo2.max = Math.max(this.stats.spo2.max, spo2);

                // 体温统计
                this.stats.temperature.sum += temperature;
                this.stats.temperature.count++;
                this.stats.temperature.min = Math.min(this.stats.temperature.min, temperature);
                this.stats.temperature.max = Math.max(this.stats.temperature.max, temperature);
            }

            // 获取当前模式名称
            getCurrentModeName() {
                const modes = ['睡眠', '休息', '正常', '活跃', '运动'];
                return modes[this.hrSimulationMode] || '正常';
            }

            // 初始化图表
            initCharts() {
                // 主图表配置
                const mainConfig = {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '心率 (次/分)',
                            data: [],
                            borderColor: '#e74c3c',
                            backgroundColor: 'rgba(231, 76, 60, 0.1)',
                            tension: 0.4,
                            yAxisID: 'y'
                        }, {
                            label: '血氧 (%)',
                            data: [],
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            tension: 0.4,
                            yAxisID: 'y1'
                        }, {
                            label: '体温 (°C)',
                            data: [],
                            borderColor: '#f39c12',
                            backgroundColor: 'rgba(243, 156, 18, 0.1)',
                            tension: 0.4,
                            yAxisID: 'y2'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: { display: true, title: { display: true, text: '时间' } },
                            y: { type: 'linear', display: true, position: 'left', title: { display: true, text: '心率' }, min: 50, max: 110 },
                            y1: { type: 'linear', display: false, position: 'right', min: 80, max: 100 },
                            y2: { type: 'linear', display: false, position: 'right', min: 35, max: 41 }
                        },
                        plugins: { legend: { display: true, position: 'top' } },
                        animation: { duration: 0 }
                    }
                };

                // 单独图表配置
                const singleChartConfig = (label, color, min, max) => ({
                    type: 'line',
                    data: { labels: [], datasets: [{ label, data: [], borderColor: color, backgroundColor: color + '20', tension: 0.4, fill: true }] },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: { y: { min, max } },
                        plugins: { legend: { display: false } },
                        animation: { duration: 0 }
                    }
                });

                return { mainConfig, singleChartConfig };
            }
        }

        // 初始化所有图表
        function initAllCharts() {
            const ctx1 = document.getElementById('mainChart').getContext('2d');
            const ctx2 = document.getElementById('heartRateChart').getContext('2d');
            const ctx3 = document.getElementById('spo2Chart').getContext('2d');
            const ctx4 = document.getElementById('temperatureChart').getContext('2d');

            const { mainConfig, singleChartConfig } = simulator.initCharts();

            mainChart = new Chart(ctx1, mainConfig);
            heartRateChart = new Chart(ctx2, singleChartConfig('心率', '#e74c3c', 50, 110));
            spo2Chart = new Chart(ctx3, singleChartConfig('血氧', '#3498db', 80, 100));
            temperatureChart = new Chart(ctx4, singleChartConfig('体温', '#f39c12', 35, 41));
        }

        // 开始模拟
        function startSimulation() {
            if (simulator.isRunning) return;

            simulator.isRunning = true;
            simulator.startTime = Date.now();
            simulator.dataCount = 0;

            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('stopBtn').classList.add('active');

            updateStatus('运行中', true);

            simulator.interval = setInterval(() => {
                const data = simulator.generateHealthData();
                simulator.dataCount++;

                updateDisplay(data);
                updateCharts(data);
                updateStats();
                updateDataCount(simulator.dataCount);
                updateRunTime(Date.now() - simulator.startTime);
            }, 1000);
        }

        // 停止模拟
        function stopSimulation() {
            if (!simulator.isRunning) return;

            simulator.isRunning = false;
            clearInterval(simulator.interval);

            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('stopBtn').classList.remove('active');

            updateStatus('已停止', false);
        }

        // 重置数据
        function resetData() {
            stopSimulation();

            simulator.heartRateData = [];
            simulator.spo2Data = [];
            simulator.temperatureData = [];
            simulator.timeLabels = [];
            simulator.dataCount = 0;

            // 重置统计数据
            simulator.stats = {
                heartRate: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                spo2: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                temperature: { sum: 0, count: 0, min: Infinity, max: -Infinity }
            };

            // 重置显示
            document.getElementById('heartRate').textContent = '--';
            document.getElementById('spo2').textContent = '--';
            document.getElementById('temperature').textContent = '--';

            // 重置图表
            [mainChart, heartRateChart, spo2Chart, temperatureChart].forEach(chart => {
                chart.data.labels = [];
                chart.data.datasets.forEach(dataset => dataset.data = []);
                chart.update();
            });

            updateStats();
            updateDataCount(0);
            updateRunTime(0);
        }

        // 更新状态显示
        function updateStatus(text, isActive) {
            document.getElementById('statusText').textContent = text;
            const indicator = document.getElementById('statusIndicator');
            indicator.className = `status-indicator ${isActive ? 'active' : 'inactive'}`;
        }

        // 更新数据显示
        function updateDisplay(data) {
            document.getElementById('heartRate').textContent = data.heartRate;
            document.getElementById('spo2').textContent = data.spo2;
            document.getElementById('temperature').textContent = data.temperature;

            // 添加脉冲动画
            const heartRateElement = document.getElementById('heartRate');
            heartRateElement.parentElement.classList.add('pulse-animation');
            setTimeout(() => heartRateElement.parentElement.classList.remove('pulse-animation'), 1000);
        }

        // 更新图表
        function updateCharts(data) {
            const now = new Date().toLocaleTimeString();

            // 添加新数据
            simulator.timeLabels.push(now);
            simulator.heartRateData.push(data.heartRate);
            simulator.spo2Data.push(data.spo2);
            simulator.temperatureData.push(data.temperature);

            // 限制数据点数量
            if (simulator.timeLabels.length > simulator.maxDataPoints) {
                simulator.timeLabels.shift();
                simulator.heartRateData.shift();
                simulator.spo2Data.shift();
                simulator.temperatureData.shift();
            }

            // 更新主图表
            mainChart.data.labels = simulator.timeLabels;
            mainChart.data.datasets[0].data = simulator.heartRateData;
            mainChart.data.datasets[1].data = simulator.spo2Data;
            mainChart.data.datasets[2].data = simulator.temperatureData;
            mainChart.update('none');

            // 更新单独图表
            heartRateChart.data.labels = simulator.timeLabels;
            heartRateChart.data.datasets[0].data = simulator.heartRateData;
            heartRateChart.update('none');

            spo2Chart.data.labels = simulator.timeLabels;
            spo2Chart.data.datasets[0].data = simulator.spo2Data;
            spo2Chart.update('none');

            temperatureChart.data.labels = simulator.timeLabels;
            temperatureChart.data.datasets[0].data = simulator.temperatureData;
            temperatureChart.update('none');
        }

        // 更新统计显示
        function updateStats() {
            const stats = simulator.stats;

            document.getElementById('avgHeartRate').textContent =
                stats.heartRate.count > 0 ? Math.round(stats.heartRate.sum / stats.heartRate.count) : '--';
            document.getElementById('maxHeartRate').textContent =
                stats.heartRate.max !== -Infinity ? stats.heartRate.max : '--';
            document.getElementById('minHeartRate').textContent =
                stats.heartRate.min !== Infinity ? stats.heartRate.min : '--';
            document.getElementById('avgSpO2').textContent =
                stats.spo2.count > 0 ? Math.round(stats.spo2.sum / stats.spo2.count) : '--';
            document.getElementById('avgTemp').textContent =
                stats.temperature.count > 0 ? (stats.temperature.sum / stats.temperature.count).toFixed(1) : '--';
            document.getElementById('currentMode').textContent = simulator.getCurrentModeName();
        }

        // 更新数据计数
        function updateDataCount(count) {
            document.getElementById('dataCount').textContent = count;
        }

        // 更新运行时间
        function updateRunTime(milliseconds) {
            const seconds = Math.floor(milliseconds / 1000);
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            document.getElementById('runTime').textContent =
                `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        // 导出数据
        function exportData() {
            if (simulator.heartRateData.length === 0) {
                alert('没有数据可导出');
                return;
            }

            let csvContent = "时间,心率(次/分),血氧(%),体温(°C)\n";
            for (let i = 0; i < simulator.timeLabels.length; i++) {
                csvContent += `${simulator.timeLabels[i]},${simulator.heartRateData[i]},${simulator.spo2Data[i]},${simulator.temperatureData[i]}\n`;
            }

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `health_data_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
            link.click();
        }

        // 全屏切换
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initAllCharts();
            updateStatus('系统已就绪', false);
            updateDataCount(0);
            updateRunTime(0);
            updateStats();
        });
    </script>
</body>
</html>
