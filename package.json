{"name": "medical-monitoring-hardware-server", "version": "1.0.0", "description": "医疗监测系统硬件数据WebSocket服务器", "main": "hardware_websocket_server.js", "scripts": {"start": "node hardware_websocket_server.js", "dev": "nodemon hardware_websocket_server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["medical", "monitoring", "websocket", "hardware", "vital-signs"], "author": "Medical System Developer", "license": "MIT", "dependencies": {"ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}