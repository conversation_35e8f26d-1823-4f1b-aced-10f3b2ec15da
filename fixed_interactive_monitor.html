<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版交互式医学监测系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            color: #333;
            font-size: 2.2em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .btn.active {
            background: linear-gradient(45deg, #2e7d32, #4caf50);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        /* 主界面 */
        .main-view {
            display: block;
        }

        .main-view.hidden {
            display: none;
        }

        .vital-signs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .vital-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .vital-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        }

        .vital-card::after {
            content: '点击查看详细波形';
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8em;
            color: #999;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .vital-card:hover::after {
            opacity: 1;
        }

        .vital-card .icon {
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .vital-card .value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .vital-card .label {
            color: #666;
            font-size: 1em;
            margin-bottom: 8px;
        }

        .vital-card .range {
            color: #999;
            font-size: 0.8em;
        }

        /* 生理参数颜色主题 */
        .heart-rate .icon { color: #e74c3c; }
        .heart-rate .value { color: #e74c3c; }

        .blood-pressure .icon { color: #8e44ad; }
        .blood-pressure .value { color: #8e44ad; }

        .spo2 .icon { color: #3498db; }
        .spo2 .value { color: #3498db; }

        .temperature .icon { color: #f39c12; }
        .temperature .value { color: #f39c12; }

        /* 详细视图 */
        .detail-view {
            display: none;
        }

        .detail-view.active {
            display: block;
        }

        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .detail-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .detail-title .icon {
            font-size: 2em;
        }

        .detail-title .info h2 {
            color: #333;
            margin-bottom: 5px;
        }

        .detail-title .info .current-value {
            font-size: 1.5em;
            font-weight: bold;
        }

        .back-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .waveform-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .waveform-container h3 {
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
        }

        .info-panel {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .info-panel h3 {
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .info-item .value {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .info-item .label {
            color: #666;
            font-size: 0.9em;
        }

        .pulse-animation {
            animation: heartbeat 1s infinite;
        }

        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @media (max-width: 768px) {
            .vital-signs-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .detail-header {
                flex-direction: column;
                gap: 15px;
            }
            
            .info-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 修复版交互式医学监测系统</h1>
            <p>点击生理数据卡片查看详细波形图和分析（已修复显示和点击问题）</p>
        </div>

        <div class="control-panel">
            <button class="btn" id="startBtn" onclick="startSimulation()">开始监测</button>
            <button class="btn" id="stopBtn" onclick="stopSimulation()" disabled>停止监测</button>
            <button class="btn" onclick="resetData()">重置数据</button>
            <button class="btn" onclick="testClick()">测试点击功能</button>
        </div>

        <!-- 主界面 -->
        <div class="main-view" id="mainView">
            <div class="vital-signs-grid">
                <div class="vital-card heart-rate" onclick="showDetailView('heartRate')">
                    <div class="icon">💓</div>
                    <div class="value" id="heartRate">72</div>
                    <div class="label">心率 (次/分)</div>
                    <div class="range">正常: 60-100</div>
                </div>
                <div class="vital-card blood-pressure" onclick="showDetailView('bloodPressure')">
                    <div class="icon">🩸</div>
                    <div class="value" id="bloodPressure">120/80</div>
                    <div class="label">血压 (mmHg)</div>
                    <div class="range">正常: 90-140/60-90</div>
                </div>
                <div class="vital-card spo2" onclick="showDetailView('spo2')">
                    <div class="icon">🫁</div>
                    <div class="value" id="spo2">98</div>
                    <div class="label">血氧饱和度 (%)</div>
                    <div class="range">正常: 95-100</div>
                </div>
                <div class="vital-card temperature" onclick="showDetailView('temperature')">
                    <div class="icon">🌡️</div>
                    <div class="value" id="temperature">36.5</div>
                    <div class="label">体温 (°C)</div>
                    <div class="range">正常: 36.1-37.2</div>
                </div>
            </div>
        </div>

        <!-- 详细视图 -->
        <div class="detail-view" id="detailView">
            <div class="detail-header">
                <div class="detail-title">
                    <div class="icon" id="detailIcon">💓</div>
                    <div class="info">
                        <h2 id="detailTitle">心率监测</h2>
                        <div class="current-value" id="detailCurrentValue">72 次/分</div>
                    </div>
                </div>
                <button class="back-btn" onclick="showMainView()">← 返回主界面</button>
            </div>

            <div class="waveform-container">
                <h3 id="waveformTitle">心率实时波形图</h3>
                <div class="chart-wrapper">
                    <canvas id="detailChart"></canvas>
                </div>
            </div>

            <div class="info-panel">
                <h3>实时数据分析</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="value" id="infoCurrentValue">72</div>
                        <div class="label">当前值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="infoNormalRange">60-100</div>
                        <div class="label">正常范围</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="infoAverageValue">--</div>
                        <div class="label">平均值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="infoMaxValue">--</div>
                        <div class="label">最高值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="infoMinValue">--</div>
                        <div class="label">最低值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="infoStatus">正常</div>
                        <div class="label">状态</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简化的医学数据模拟器
        class FixedMedicalSimulator {
            constructor() {
                this.isRunning = false;
                this.interval = null;
                this.currentDetailView = null;
                
                // 基础生理参数
                this.heartRate = 72;
                this.systolicBP = 120;
                this.diastolicBP = 80;
                this.spo2 = 98;
                this.temperature = 36.5;
                
                // 数据历史
                this.timeLabels = [];
                this.dataHistory = {
                    heartRate: [],
                    systolicBP: [],
                    spo2: [],
                    temperature: []
                };
                
                // 统计数据
                this.stats = {
                    heartRate: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                    systolicBP: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                    spo2: { sum: 0, count: 0, min: Infinity, max: -Infinity },
                    temperature: { sum: 0, count: 0, min: Infinity, max: -Infinity }
                };
                
                this.maxDataPoints = 50;
            }
            
            // 生成模拟数据
            generateData() {
                // 心率变化
                this.heartRate += (Math.random() - 0.5) * 4;
                this.heartRate = Math.max(55, Math.min(105, this.heartRate));
                
                // 血压变化
                this.systolicBP += (Math.random() - 0.5) * 6;
                this.systolicBP = Math.max(90, Math.min(150, this.systolicBP));
                this.diastolicBP += (Math.random() - 0.5) * 4;
                this.diastolicBP = Math.max(60, Math.min(100, this.diastolicBP));
                
                // 血氧变化（相对稳定）
                this.spo2 += (Math.random() - 0.5) * 1;
                this.spo2 = Math.max(95, Math.min(100, this.spo2));
                
                // 体温变化（非常稳定）
                this.temperature += (Math.random() - 0.5) * 0.2;
                this.temperature = Math.max(36.0, Math.min(37.5, this.temperature));
                
                const data = {
                    heartRate: Math.round(this.heartRate),
                    systolicBP: Math.round(this.systolicBP),
                    diastolicBP: Math.round(this.diastolicBP),
                    spo2: Math.round(this.spo2),
                    temperature: parseFloat(this.temperature.toFixed(1))
                };
                
                this.updateStats(data);
                return data;
            }
            
            // 更新统计数据
            updateStats(data) {
                Object.keys(data).forEach(key => {
                    const stat = this.stats[key];
                    if (stat) {
                        stat.sum += data[key];
                        stat.count++;
                        stat.min = Math.min(stat.min, data[key]);
                        stat.max = Math.max(stat.max, data[key]);
                    }
                });
            }
        }
        
        // 全局变量
        let simulator = new FixedMedicalSimulator();
        let detailChart = null;
        
        // 显示初始数据
        function displayInitialData() {
            const initialData = {
                heartRate: simulator.heartRate,
                systolicBP: simulator.systolicBP,
                diastolicBP: simulator.diastolicBP,
                spo2: simulator.spo2,
                temperature: simulator.temperature
            };

            updateDisplay(initialData);
            console.log('显示初始数据:', initialData);
        }

        // 开始模拟
        function startSimulation() {
            if (simulator.isRunning) return;

            simulator.isRunning = true;
            document.getElementById('startBtn').disabled = true;
            document.getElementById('startBtn').classList.remove('active');
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('stopBtn').classList.add('active');

            console.log('开始模拟');

            simulator.interval = setInterval(() => {
                const data = simulator.generateData();
                updateDisplay(data);
                updateCharts(data);

                // 如果在详细视图中，更新详细信息
                if (simulator.currentDetailView) {
                    updateDetailView(data);
                }
            }, 1000);
        }

        // 停止模拟
        function stopSimulation() {
            if (!simulator.isRunning) return;

            simulator.isRunning = false;
            clearInterval(simulator.interval);

            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('stopBtn').classList.remove('active');

            console.log('停止模拟');
        }

        // 重置数据
        function resetData() {
            stopSimulation();

            // 重置模拟器
            simulator = new FixedMedicalSimulator();

            // 重置显示
            displayInitialData();

            // 如果在详细视图中，返回主界面
            if (simulator.currentDetailView) {
                showMainView();
            }

            console.log('数据已重置');
        }

        // 测试点击功能
        function testClick() {
            console.log('测试点击功能');
            alert('点击功能测试：请点击任意生理数据卡片查看详细波形图');
        }

        // 更新显示
        function updateDisplay(data) {
            document.getElementById('heartRate').textContent = data.heartRate;
            document.getElementById('bloodPressure').textContent = `${data.systolicBP}/${data.diastolicBP}`;
            document.getElementById('spo2').textContent = data.spo2;
            document.getElementById('temperature').textContent = data.temperature;

            // 添加心跳动画
            const heartRateCard = document.querySelector('.heart-rate');
            if (heartRateCard) {
                heartRateCard.classList.add('pulse-animation');
                setTimeout(() => heartRateCard.classList.remove('pulse-animation'), 1000);
            }
        }

        // 更新图表数据
        function updateCharts(data) {
            const now = new Date().toLocaleTimeString();

            // 添加新数据
            simulator.timeLabels.push(now);
            simulator.dataHistory.heartRate.push(data.heartRate);
            simulator.dataHistory.systolicBP.push(data.systolicBP);
            simulator.dataHistory.spo2.push(data.spo2);
            simulator.dataHistory.temperature.push(data.temperature);

            // 限制数据点数量
            if (simulator.timeLabels.length > simulator.maxDataPoints) {
                simulator.timeLabels.shift();
                Object.keys(simulator.dataHistory).forEach(key => {
                    simulator.dataHistory[key].shift();
                });
            }
        }

        // 显示详细视图
        function showDetailView(parameterType) {
            console.log('点击了参数:', parameterType);
            simulator.currentDetailView = parameterType;

            // 隐藏主界面，显示详细视图
            document.getElementById('mainView').classList.add('hidden');
            document.getElementById('detailView').classList.add('active');

            // 配置详细视图内容
            const parameterConfig = {
                heartRate: {
                    icon: '💓',
                    title: '心率监测',
                    unit: '次/分',
                    color: '#e74c3c',
                    normalRange: '60-100',
                    getValue: () => simulator.heartRate,
                    getDataArray: () => simulator.dataHistory.heartRate
                },
                bloodPressure: {
                    icon: '🩸',
                    title: '血压监测',
                    unit: 'mmHg',
                    color: '#8e44ad',
                    normalRange: '90-140/60-90',
                    getValue: () => `${Math.round(simulator.systolicBP)}/${Math.round(simulator.diastolicBP)}`,
                    getDataArray: () => simulator.dataHistory.systolicBP
                },
                spo2: {
                    icon: '🫁',
                    title: '血氧监测',
                    unit: '%',
                    color: '#3498db',
                    normalRange: '95-100',
                    getValue: () => Math.round(simulator.spo2),
                    getDataArray: () => simulator.dataHistory.spo2
                },
                temperature: {
                    icon: '🌡️',
                    title: '体温监测',
                    unit: '°C',
                    color: '#f39c12',
                    normalRange: '36.1-37.2',
                    getValue: () => simulator.temperature.toFixed(1),
                    getDataArray: () => simulator.dataHistory.temperature
                }
            };

            const config = parameterConfig[parameterType];
            if (!config) {
                console.error('未找到参数配置:', parameterType);
                return;
            }

            console.log('配置详细视图:', config);

            // 更新详细视图标题和图标
            document.getElementById('detailIcon').textContent = config.icon;
            document.getElementById('detailIcon').style.color = config.color;
            document.getElementById('detailTitle').textContent = config.title;
            document.getElementById('detailCurrentValue').textContent = `${config.getValue()} ${config.unit}`;
            document.getElementById('detailCurrentValue').style.color = config.color;
            document.getElementById('waveformTitle').textContent = `${config.title}实时波形图`;

            // 更新信息面板
            updateInfoPanel(parameterType, config);

            // 初始化详细图表
            initDetailChart(parameterType, config);
        }

        // 显示主界面
        function showMainView() {
            console.log('返回主界面');
            document.getElementById('mainView').classList.remove('hidden');
            document.getElementById('detailView').classList.remove('active');
            simulator.currentDetailView = null;

            // 销毁详细图表
            if (detailChart) {
                detailChart.destroy();
                detailChart = null;
            }
        }

        // 初始化详细图表
        function initDetailChart(parameterType, config) {
            if (detailChart) {
                detailChart.destroy();
            }

            const ctx = document.getElementById('detailChart').getContext('2d');
            detailChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: simulator.timeLabels,
                    datasets: [{
                        label: config.title,
                        data: config.getDataArray(),
                        borderColor: config.color,
                        backgroundColor: config.color + '20',
                        tension: 0.4,
                        fill: true,
                        pointRadius: 4,
                        pointHoverRadius: 8,
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                font: {
                                    size: 14
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间',
                                font: {
                                    size: 14
                                }
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: `${config.title} (${config.unit})`,
                                font: {
                                    size: 14
                                }
                            }
                        }
                    },
                    animation: {
                        duration: 300
                    }
                }
            });

            console.log('详细图表已初始化');
        }

        // 更新详细视图
        function updateDetailView(data) {
            if (!simulator.currentDetailView || !detailChart) return;

            const parameterConfig = {
                heartRate: { getValue: () => data.heartRate, unit: '次/分' },
                bloodPressure: { getValue: () => `${data.systolicBP}/${data.diastolicBP}`, unit: 'mmHg' },
                spo2: { getValue: () => data.spo2, unit: '%' },
                temperature: { getValue: () => data.temperature, unit: '°C' }
            };

            const config = parameterConfig[simulator.currentDetailView];
            if (!config) return;

            // 更新当前值显示
            document.getElementById('detailCurrentValue').textContent = `${config.getValue()} ${config.unit}`;

            // 更新详细图表
            detailChart.data.labels = simulator.timeLabels;
            detailChart.data.datasets[0].data = simulator.dataHistory[simulator.currentDetailView] || [];
            detailChart.update('none');

            // 更新信息面板
            updateInfoPanel(simulator.currentDetailView, config);
        }

        // 更新信息面板
        function updateInfoPanel(parameterType, config) {
            const stat = simulator.stats[parameterType];

            document.getElementById('infoCurrentValue').textContent = config.getValue();
            document.getElementById('infoNormalRange').textContent = config.normalRange || '正常范围';

            if (stat && stat.count > 0) {
                document.getElementById('infoAverageValue').textContent = (stat.sum / stat.count).toFixed(1);
                document.getElementById('infoMaxValue').textContent = stat.max;
                document.getElementById('infoMinValue').textContent = stat.min;

                // 简单的状态判断
                const current = parseFloat(config.getValue());
                let status = '正常';
                if (parameterType === 'heartRate' && (current < 60 || current > 100)) status = '异常';
                if (parameterType === 'spo2' && current < 95) status = '异常';
                if (parameterType === 'temperature' && (current < 36.1 || current > 37.2)) status = '异常';

                document.getElementById('infoStatus').textContent = status;
                document.getElementById('infoStatus').style.color = status === '正常' ? '#4caf50' : '#f44336';
            } else {
                document.getElementById('infoAverageValue').textContent = '--';
                document.getElementById('infoMaxValue').textContent = '--';
                document.getElementById('infoMinValue').textContent = '--';
                document.getElementById('infoStatus').textContent = '正常';
                document.getElementById('infoStatus').style.color = '#4caf50';
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            console.log('修复版交互式医学监测系统已加载');
            displayInitialData();
        });
    </script>
</body>
</html>
