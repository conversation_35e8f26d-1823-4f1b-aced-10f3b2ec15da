<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能医疗监测系统 - 启动中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: flex-start; /* 改为flex-start，允许内容从顶部开始 */
            justify-content: center;
            padding: 20px;
            padding-top: 40px; /* 增加顶部间距 */
            padding-bottom: 40px; /* 增加底部间距 */
            position: relative;
            overflow-x: hidden; /* 只隐藏横向滚动条 */
            overflow-y: auto;   /* 允许纵向滚动 */
        }

        /* 确保html也允许滚动 */
        html {
            overflow-x: hidden;
            overflow-y: auto;
            scroll-behavior: smooth; /* 平滑滚动 */
        }

        /* 自定义滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Firefox滚动条样式 */
        * {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
        }

        /* 动态背景粒子效果 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        /* 背景波浪效果 */
        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            clip-path: polygon(0 20px, 100% 0, 100% 100%, 0 100%);
            animation: wave 3s ease-in-out infinite;
        }

        @keyframes wave {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(-10px); }
        }

        /* 主容器 */
        .launcher-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 60px 50px;
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            max-width: 900px;
            width: 100%;
            text-align: center;
            position: relative;
            z-index: 2;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 标题区域 */
        .header {
            margin-bottom: 50px;
        }

        .logo {
            font-size: 4em;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .title {
            font-size: 2.5em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            letter-spacing: -0.5px;
        }

        .subtitle {
            font-size: 1.2em;
            color: #7f8c8d;
            font-weight: 400;
            line-height: 1.6;
        }

        /* 选项卡片 */
        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .option-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 40px 30px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .option-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea, #764ba2);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .option-card:hover {
            transform: translateY(-8px);
            box-shadow: 
                0 20px 40px rgba(102, 126, 234, 0.2),
                0 0 0 1px rgba(102, 126, 234, 0.1);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .option-card:hover::before {
            opacity: 0.05;
        }

        .option-card:hover .option-icon {
            transform: scale(1.1);
            color: #667eea;
        }

        .option-card:hover .option-title {
            color: #667eea;
        }

        .option-card:active {
            transform: translateY(-4px) scale(0.98);
        }

        /* 卡片加载动画 */
        .option-card {
            animation: cardSlideIn 0.6s ease-out forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .option-card:nth-child(1) { animation-delay: 0.1s; }
        .option-card:nth-child(2) { animation-delay: 0.2s; }
        .option-card:nth-child(3) { animation-delay: 0.3s; }

        @keyframes cardSlideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .option-icon {
            font-size: 3.5em;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            color: #34495e;
        }

        .option-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            transition: color 0.3s ease;
        }

        .option-description {
            font-size: 1em;
            color: #7f8c8d;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .option-features {
            list-style: none;
            text-align: left;
            font-size: 0.9em;
            color: #95a5a6;
        }

        .option-features li {
            margin-bottom: 8px;
            position: relative;
            padding-left: 20px;
        }

        .option-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }

        /* 状态指示器 */
        .status-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 12px;
            height: 12px;
            background: #27ae60;
            border-radius: 50%;
            animation: statusPulse 2s ease-in-out infinite;
        }

        @keyframes statusPulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.6;
                transform: scale(1.2);
            }
        }

        /* 点击波纹效果 */
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(102, 126, 234, 0.3);
            transform: scale(0);
            animation: rippleEffect 0.6s linear;
            pointer-events: none;
        }

        @keyframes rippleEffect {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* 底部信息 */
        .footer {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            color: #95a5a6;
            font-size: 0.9em;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .footer-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .system-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.85em;
            color: #27ae60;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #27ae60;
            border-radius: 50%;
            animation: statusPulse 2s ease-in-out infinite;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                align-items: flex-start; /* 确保小屏幕也从顶部开始 */
                padding: 15px;
                padding-top: 20px;
                padding-bottom: 20px;
            }

            .launcher-container {
                padding: 40px 30px;
                margin: 10px 0; /* 只保留上下边距 */
                max-width: none; /* 移除最大宽度限制 */
            }

            .title {
                font-size: 2em;
            }

            .logo {
                font-size: 3em;
            }

            .options-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .option-card {
                padding: 30px 25px;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
                padding-top: 15px;
                padding-bottom: 15px;
            }

            .launcher-container {
                padding: 30px 20px;
                margin: 5px 0;
            }

            .title {
                font-size: 1.8em;
            }

            .subtitle {
                font-size: 1.1em;
            }

            .footer {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .footer-info {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 动态背景效果 -->
    <div class="particles" id="particles"></div>
    <div class="wave"></div>

    <div class="launcher-container">
        <div class="header">
            <div class="logo">🏥</div>
            <h1 class="title">智能医疗监测系统</h1>
            <p class="subtitle">专业级生理参数监测平台<br>为您提供精准、实时的健康数据分析</p>
        </div>

        <div class="options-grid">
            <!-- 短期检测 -->
            <div class="option-card" onclick="navigateToShortTerm(this)">
                <div class="status-indicator"></div>
                <div class="option-icon">⚡</div>
                <h3 class="option-title">短期检测</h3>
                <p class="option-description">快速健康状态评估，适用于临时检查和急诊监测</p>
                <ul class="option-features">
                    <li>实时生理参数监测</li>
                    <li>快速异常检测</li>
                    <li>即时报告生成</li>
                    <li>适合急诊场景</li>
                </ul>
            </div>

            <!-- 长期监测 -->
            <div class="option-card" onclick="navigateToLongTerm(this)">
                <div class="status-indicator"></div>
                <div class="option-icon">📊</div>
                <h3 class="option-title">长期监测</h3>
                <p class="option-description">持续健康追踪，提供详细的趋势分析和预警功能</p>
                <ul class="option-features">
                    <li>多对象集成管理</li>
                    <li>历史数据分析</li>
                    <li>趋势预测预警</li>
                    <li>详细波形图表</li>
                </ul>
            </div>

            <!-- 设备连接 -->
            <div class="option-card" onclick="navigateToDeviceConnection(this)">
                <div class="status-indicator"></div>
                <div class="option-icon">🔗</div>
                <h3 class="option-title">设备连接</h3>
                <p class="option-description">管理和配置医疗设备连接，确保数据传输稳定可靠</p>
                <ul class="option-features">
                    <li>设备状态监控</li>
                    <li>连接配置管理</li>
                    <li>数据同步设置</li>
                    <li>故障诊断工具</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <div class="footer-info">
                <p>© 2024 智能医疗监测系统</p>
                <span>|</span>
                <p>专业医疗级监测解决方案</p>
            </div>
            <div class="system-status">
                <div class="status-dot"></div>
                <span>系统就绪</span>
            </div>
        </div>
    </div>

    <script>
        // 创建动态背景粒子
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                // 随机位置
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                
                // 随机动画延迟
                particle.style.animationDelay = Math.random() * 6 + 's';
                
                // 随机大小
                const size = Math.random() * 3 + 2;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                
                particlesContainer.appendChild(particle);
            }
        }

        // 创建点击波纹效果
        function createRipple(element, event) {
            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;

            const ripple = document.createElement('div');
            ripple.className = 'ripple';
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';

            element.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        }

        // 导航函数
        function navigateToShortTerm(element) {
            console.log('🚀 启动短期检测模式');

            // 添加加载状态
            element.style.opacity = '0.7';
            element.style.transform = 'scale(0.95)';

            // 显示加载提示
            const originalTitle = element.querySelector('.option-title').textContent;
            element.querySelector('.option-title').textContent = '正在启动...';

            setTimeout(() => {
                window.location.href = 'integrated_medical_final.html?mode=short';
            }, 800);
        }

        function navigateToLongTerm(element) {
            console.log('📊 启动长期监测模式');

            element.style.opacity = '0.7';
            element.style.transform = 'scale(0.95)';

            const originalTitle = element.querySelector('.option-title').textContent;
            element.querySelector('.option-title').textContent = '正在启动...';

            setTimeout(() => {
                window.location.href = 'integrated_medical_final.html?mode=long';
            }, 800);
        }

        function navigateToDeviceConnection(element) {
            console.log('🔗 启动设备连接管理');

            element.style.opacity = '0.7';
            element.style.transform = 'scale(0.95)';

            const originalTitle = element.querySelector('.option-title').textContent;
            element.querySelector('.option-title').textContent = '正在启动...';

            setTimeout(() => {
                window.location.href = 'integrated_medical_final.html?mode=device';
            }, 800);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌟 启动中心加载完成');
            createParticles();

            // 检查滚动功能
            checkScrollability();

            // 为所有选项卡片添加点击波纹效果
            const optionCards = document.querySelectorAll('.option-card');
            optionCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    createRipple(this, e);
                });
            });

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.key === '1') navigateToShortTerm(optionCards[0]);
                if (e.key === '2') navigateToLongTerm(optionCards[1]);
                if (e.key === '3') navigateToDeviceConnection(optionCards[2]);
            });

            // 添加滚动事件监听
            window.addEventListener('scroll', function() {
                console.log('📜 页面滚动位置:', window.scrollY);
            });

            console.log('💡 提示: 可使用数字键 1、2、3 快速选择功能');
            console.log('🖱️ 提示: 可使用鼠标滚轮或触摸滑动浏览页面');
            console.log('✨ 启动界面已就绪，请选择功能模块');
        });

        // 检查页面滚动功能
        function checkScrollability() {
            const bodyHeight = document.body.scrollHeight;
            const windowHeight = window.innerHeight;

            console.log('📏 页面高度信息:');
            console.log('  - 内容总高度:', bodyHeight + 'px');
            console.log('  - 视口高度:', windowHeight + 'px');
            console.log('  - 是否需要滚动:', bodyHeight > windowHeight ? '是' : '否');
            console.log('  - body overflow-y:', getComputedStyle(document.body).overflowY);
            console.log('  - html overflow-y:', getComputedStyle(document.documentElement).overflowY);

            if (bodyHeight > windowHeight) {
                console.log('✅ 页面内容超出视口，滚动功能应该可用');
            } else {
                console.log('ℹ️ 页面内容未超出视口，无需滚动');
            }
        }
    </script>
</body>
</html>
