<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集成式生理监测管理系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 15px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 25px;
        }

        .header h1 {
            color: #333;
            font-size: 2.3em;
            margin-bottom: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        /* 管理界面 */
        .management-view {
            display: block;
        }

        .management-view.hidden {
            display: none;
        }

        .status-info {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.1em;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        .subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .subject-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .subject-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .subject-card:hover::before {
            left: 100%;
        }

        .subject-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .subject-card::after {
            content: '点击进入详细监测';
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.85em;
            color: #666;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .subject-card:hover::after {
            opacity: 1;
        }

        .subject-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .subject-id {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 1.1em;
        }

        .subject-status {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }

        .status-normal {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-attention {
            background: #fff3e0;
            color: #f57c00;
        }

        .status-critical {
            background: #ffebee;
            color: #d32f2f;
        }

        .status-inactive {
            background: #f5f5f5;
            color: #757575;
        }

        .subject-info {
            margin-bottom: 20px;
        }

        .subject-info h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .subject-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .detail-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }

        .detail-item .label {
            color: #666;
            font-size: 0.85em;
            margin-bottom: 4px;
        }

        .detail-item .value {
            color: #333;
            font-weight: bold;
            font-size: 1.1em;
        }

        .vital-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
        }

        .vital-item {
            text-align: center;
            flex: 1;
        }

        .vital-item .icon {
            font-size: 1.5em;
            margin-bottom: 5px;
        }

        .vital-item .value {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .vital-item .label {
            font-size: 0.8em;
            color: #666;
        }

        /* 生理参数颜色 */
        .heart-rate .icon { color: #e74c3c; }
        .heart-rate .value { color: #e74c3c; }
        .blood-pressure .icon { color: #8e44ad; }
        .blood-pressure .value { color: #8e44ad; }
        .spo2 .icon { color: #3498db; }
        .spo2 .value { color: #3498db; }
        .temperature .icon { color: #f39c12; }
        .temperature .value { color: #f39c12; }

        /* 详细监测界面（复用之前的样式） */
        .detail-monitoring-view {
            display: none;
        }

        .detail-monitoring-view.active {
            display: block;
        }

        /* 参数详细视图 */
        .parameter-detail-view {
            display: none;
        }

        .parameter-detail-view.active {
            display: block;
        }

        .waveform-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
        }

        .waveform-container h3 {
            margin-bottom: 18px;
            color: #333;
            text-align: center;
            font-size: 1.4em;
        }

        .chart-wrapper {
            position: relative;
            height: 380px;
        }

        .info-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .info-panel h3 {
            margin-bottom: 18px;
            color: #333;
            text-align: center;
            font-size: 1.4em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 18px;
            border-radius: 10px;
            text-align: center;
        }

        .info-item .value {
            font-size: 1.6em;
            font-weight: bold;
            color: #333;
            margin-bottom: 6px;
        }

        .info-item .label {
            color: #666;
            font-size: 0.95em;
        }

        .vitals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .vital-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .vital-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .vital-card:hover::before {
            left: 100%;
        }

        .vital-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .vital-card::after {
            content: '点击查看详细分析';
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.85em;
            color: #666;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .vital-card:hover::after {
            opacity: 1;
        }

        .vital-card .icon {
            font-size: 3.2em;
            margin-bottom: 12px;
            display: block;
        }

        .vital-card .value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .vital-card .label {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 8px;
        }

        .vital-card .range {
            color: #999;
            font-size: 0.9em;
        }

        /* 扩展的生理参数颜色主题 */
        .respiratory-rate .icon { color: #27ae60; }
        .respiratory-rate .value { color: #27ae60; }
        .respiratory-rate:hover { border-left: 4px solid #27ae60; }

        .blood-glucose .icon { color: #e67e22; }
        .blood-glucose .value { color: #e67e22; }
        .blood-glucose:hover { border-left: 4px solid #e67e22; }

        .ecg-rhythm .icon { color: #34495e; }
        .ecg-rhythm .value { color: #34495e; }
        .ecg-rhythm:hover { border-left: 4px solid #34495e; }

        .cvp .icon { color: #9b59b6; }
        .cvp .value { color: #9b59b6; }
        .cvp:hover { border-left: 4px solid #9b59b6; }

        .back-to-management {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1em;
            margin-bottom: 20px;
        }

        .back-to-management:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .pulse-animation {
            animation: heartbeat 1s infinite;
        }

        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @media (max-width: 768px) {
            .subjects-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .subject-details {
                grid-template-columns: 1fr;
            }
            
            .vital-summary {
                flex-direction: column;
                gap: 10px;
            }
            
            .vitals-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            
            .vital-card {
                padding: 20px;
            }
            
            .vital-card .icon {
                font-size: 2.8em;
            }
            
            .vital-card .value {
                font-size: 2.2em;
            }
        }

        @media (max-width: 480px) {
            .vitals-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 集成式生理监测管理系统</h1>
            <p>多对象集成监测，统一管理，精准分析</p>
        </div>

        <!-- 管理界面 -->
        <div class="management-view" id="managementView">
            <div class="status-info" id="managementStatusInfo">
                系统运行正常 - 当前监测12个对象，1个对象数据有效
            </div>

            <div class="subjects-grid" id="subjectsGrid">
                <!-- 检测对象卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 详细监测界面 -->
        <div class="detail-monitoring-view" id="detailMonitoringView">
            <button class="back-to-management" onclick="backToManagement()">← 返回管理界面</button>
            
            <div class="status-info" id="detailStatusInfo">
                监测系统运行正常 - 8项生理参数实时采集中
            </div>

            <div class="vitals-grid" id="vitalsGrid">
                <!-- 生理参数卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 参数详细视图 -->
        <div class="parameter-detail-view" id="parameterDetailView" style="display: none;">
            <div class="detail-header">
                <div class="detail-title">
                    <div class="icon" id="parameterDetailIcon">💓</div>
                    <div class="info">
                        <h2 id="parameterDetailTitle">心率分析</h2>
                        <div class="current-value" id="parameterDetailCurrentValue">72 次/分</div>
                    </div>
                </div>
                <button class="back-to-management" onclick="backToDetailMonitoring()">← 返回监测</button>
            </div>

            <div class="waveform-container">
                <h3 id="parameterWaveformTitle">心率变化趋势</h3>
                <div class="chart-wrapper">
                    <canvas id="parameterDetailChart"></canvas>
                </div>
            </div>

            <div class="info-panel">
                <h3>数据分析</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="value" id="parameterCurrentValueInfo">72</div>
                        <div class="label">当前值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="parameterAverageValueInfo">74</div>
                        <div class="label">平均值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="parameterMaxValueInfo">85</div>
                        <div class="label">最高值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="parameterMinValueInfo">65</div>
                        <div class="label">最低值</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="parameterStatusInfo">正常</div>
                        <div class="label">状态评估</div>
                    </div>
                    <div class="info-item">
                        <div class="value" id="parameterTrendInfo">稳定</div>
                        <div class="label">变化趋势</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSubjectId = null;
        let currentParameterType = null;
        let managementInterval = null;
        let parameterDetailChart = null;
        let parameterDataHistory = {
            heartRate: [],
            systolicBP: [],
            spo2: [],
            temperature: [],
            respiratoryRate: [],
            bloodGlucose: [],
            qtInterval: [],
            cvp: [],
            timeLabels: []
        };
        
        // 模拟检测对象数据
        let subjects = {
            'P001': {
                id: 'P001',
                name: '张某某',
                age: 45,
                gender: '男',
                room: '4F012',
                status: 'normal',
                isActive: true,  // 唯一有效的监测对象
                vitalSigns: {
                    heartRate: 72,
                    systolicBP: 120,
                    diastolicBP: 80,
                    spo2: 98,
                    temperature: 36.5,
                    respiratoryRate: 16,
                    bloodGlucose: 5.2,
                    qtInterval: 400,
                    cvp: 8
                }
            },
            'P002': {
                id: 'P002',
                name: '李某某',
                age: 32,
                gender: '女',
                room: '4F015',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            },
            'P003': {
                id: 'P003',
                name: '王某某',
                age: 58,
                gender: '男',
                room: '4F018',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            },
            'P004': {
                id: 'P004',
                name: '陈某某',
                age: 28,
                gender: '女',
                room: '4F021',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            },
            'P005': {
                id: 'P005',
                name: '刘某某',
                age: 65,
                gender: '男',
                room: '4F024',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            },
            'P006': {
                id: 'P006',
                name: '赵某某',
                age: 41,
                gender: '女',
                room: '4F027',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            },
            'P007': {
                id: 'P007',
                name: '孙某某',
                age: 52,
                gender: '男',
                room: '4F030',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            },
            'P008': {
                id: 'P008',
                name: '周某某',
                age: 36,
                gender: '女',
                room: '4F033',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            },
            'P009': {
                id: 'P009',
                name: '吴某某',
                age: 47,
                gender: '男',
                room: '4F036',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            },
            'P010': {
                id: 'P010',
                name: '郑某某',
                age: 29,
                gender: '女',
                room: '4F039',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            },
            'P011': {
                id: 'P011',
                name: '马某某',
                age: 61,
                gender: '男',
                room: '4F042',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            },
            'P012': {
                id: 'P012',
                name: '朱某某',
                age: 38,
                gender: '女',
                room: '4F045',
                status: 'inactive',
                isActive: false,
                vitalSigns: null
            }
        };
        
        // 初始化管理系统
        function initializeManagementSystem() {
            renderSubjectsGrid();
            updateManagementStatus();
        }

        // 渲染检测对象网格
        function renderSubjectsGrid() {
            console.log('📊 开始渲染检测对象网格');

            const subjectsGrid = document.getElementById('subjectsGrid');
            if (!subjectsGrid) {
                console.error('❌ 未找到subjectsGrid元素');
                throw new Error('subjectsGrid元素不存在');
            }

            subjectsGrid.innerHTML = '';

            const subjectList = Object.values(subjects);
            console.log(`📋 准备渲染 ${subjectList.length} 个检测对象`);

            if (subjectList.length === 0) {
                console.warn('⚠️ 没有检测对象数据');
                subjectsGrid.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">暂无检测对象数据</div>';
                return;
            }

            subjectList.forEach((subject, index) => {
                try {
                    console.log(`🔨 创建对象卡片: ${subject.id} (${index + 1}/${subjectList.length})`);
                    const subjectCard = createSubjectCard(subject);
                    subjectsGrid.appendChild(subjectCard);
                    console.log(`✅ 对象卡片创建成功: ${subject.id}`);
                } catch (error) {
                    console.error(`❌ 创建对象卡片失败 ${subject.id}:`, error);
                }
            });

            console.log(`✅ 检测对象网格渲染完成，共创建 ${subjectsGrid.children.length} 个卡片`);
        }

        // 创建检测对象卡片
        function createSubjectCard(subject) {
            const card = document.createElement('div');
            card.className = 'subject-card';

            // 只有有效对象才能点击进入详细监测
            if (subject.isActive) {
                card.onclick = () => enterDetailMonitoring(subject.id);
            } else {
                card.style.opacity = '0.7';
                card.style.cursor = 'default';
            }

            let statusClass, statusText;
            if (!subject.isActive) {
                statusClass = 'status-inactive';
                statusText = '未连接';
            } else {
                statusClass = subject.status === 'normal' ? 'status-normal' :
                             subject.status === 'attention' ? 'status-attention' : 'status-critical';
                statusText = subject.status === 'normal' ? '正常' :
                            subject.status === 'attention' ? '注意' : '紧急';
            }

            // 生理参数显示内容
            let vitalSummaryContent;
            if (subject.isActive && subject.vitalSigns) {
                vitalSummaryContent = `
                    <div class="vital-item heart-rate">
                        <div class="icon">💓</div>
                        <div class="value">${Math.round(subject.vitalSigns.heartRate)}</div>
                        <div class="label">心率</div>
                    </div>
                    <div class="vital-item blood-pressure">
                        <div class="icon">🩸</div>
                        <div class="value">${Math.round(subject.vitalSigns.systolicBP)}/${Math.round(subject.vitalSigns.diastolicBP)}</div>
                        <div class="label">血压</div>
                    </div>
                    <div class="vital-item spo2">
                        <div class="icon">🫁</div>
                        <div class="value">${Math.round(subject.vitalSigns.spo2)}%</div>
                        <div class="label">血氧</div>
                    </div>
                    <div class="vital-item temperature">
                        <div class="icon">🌡️</div>
                        <div class="value">${subject.vitalSigns.temperature.toFixed(1)}°C</div>
                        <div class="label">体温</div>
                    </div>
                `;
            } else {
                vitalSummaryContent = `
                    <div class="vital-item">
                        <div class="icon">💓</div>
                        <div class="value">--</div>
                        <div class="label">心率</div>
                    </div>
                    <div class="vital-item">
                        <div class="icon">🩸</div>
                        <div class="value">--/--</div>
                        <div class="label">血压</div>
                    </div>
                    <div class="vital-item">
                        <div class="icon">🫁</div>
                        <div class="value">--%</div>
                        <div class="label">血氧</div>
                    </div>
                    <div class="vital-item">
                        <div class="icon">🌡️</div>
                        <div class="value">--°C</div>
                        <div class="label">体温</div>
                    </div>
                `;
            }

            card.innerHTML = `
                <div class="subject-header">
                    <div class="subject-id">${subject.id}</div>
                    <div class="subject-status ${statusClass}">${statusText}</div>
                </div>
                <div class="subject-info">
                    <h3>${subject.name}</h3>
                    <div class="subject-details">
                        <div class="detail-item">
                            <div class="label">年龄</div>
                            <div class="value">${subject.age}岁</div>
                        </div>
                        <div class="detail-item">
                            <div class="label">性别</div>
                            <div class="value">${subject.gender}</div>
                        </div>
                        <div class="detail-item">
                            <div class="label">房间</div>
                            <div class="value">${subject.room}</div>
                        </div>
                        <div class="detail-item">
                            <div class="label">监测时长</div>
                            <div class="value">${subject.isActive ? getMonitoringDuration() + '小时' : '--'}</div>
                        </div>
                    </div>
                </div>
                <div class="vital-summary">
                    ${vitalSummaryContent}
                </div>
            `;

            return card;
        }

        // 获取监测时长（模拟）
        function getMonitoringDuration() {
            return Math.floor(Math.random() * 12) + 1;
        }

        // 更新管理状态
        function updateManagementStatus() {
            const totalSubjects = Object.keys(subjects).length;
            const activeCount = Object.values(subjects).filter(s => s.isActive).length;
            const inactiveCount = totalSubjects - activeCount;
            const normalCount = Object.values(subjects).filter(s => s.isActive && s.status === 'normal').length;
            const attentionCount = Object.values(subjects).filter(s => s.isActive && s.status === 'attention').length;
            const criticalCount = Object.values(subjects).filter(s => s.isActive && s.status === 'critical').length;

            let statusText = `系统运行正常 - 当前监测${totalSubjects}个对象，${activeCount}个对象数据有效`;

            if (activeCount > 0) {
                if (criticalCount > 0) {
                    statusText += `，${criticalCount}个紧急状态`;
                } else if (attentionCount > 0) {
                    statusText += `，${attentionCount}个需要注意`;
                } else {
                    statusText += `，有效对象参数正常`;
                }
            }

            if (inactiveCount > 0) {
                statusText += `，${inactiveCount}个对象未连接`;
            }

            document.getElementById('managementStatusInfo').textContent = statusText;
        }

        // 开始管理更新
        function startManagementUpdate() {
            managementInterval = setInterval(() => {
                updateSubjectsData();
                renderSubjectsGrid();
                updateManagementStatus();
            }, 5000); // 每5秒更新一次
        }

        // 更新检测对象数据
        function updateSubjectsData() {
            Object.keys(subjects).forEach(subjectId => {
                const subject = subjects[subjectId];

                // 只更新有效对象的数据
                if (subject.isActive && subject.vitalSigns) {
                    // 更新生理数据（小幅波动）
                    subject.vitalSigns.heartRate += (Math.random() - 0.5) * 3;
                    subject.vitalSigns.heartRate = Math.max(60, Math.min(100, subject.vitalSigns.heartRate));

                    subject.vitalSigns.systolicBP += (Math.random() - 0.5) * 4;
                    subject.vitalSigns.systolicBP = Math.max(100, Math.min(140, subject.vitalSigns.systolicBP));

                    subject.vitalSigns.diastolicBP += (Math.random() - 0.5) * 3;
                    subject.vitalSigns.diastolicBP = Math.max(60, Math.min(90, subject.vitalSigns.diastolicBP));

                    subject.vitalSigns.spo2 += (Math.random() - 0.5) * 0.5;
                    subject.vitalSigns.spo2 = Math.max(95, Math.min(100, subject.vitalSigns.spo2));

                    subject.vitalSigns.temperature += (Math.random() - 0.5) * 0.1;
                    subject.vitalSigns.temperature = Math.max(36.0, Math.min(37.5, subject.vitalSigns.temperature));

                    subject.vitalSigns.respiratoryRate += (Math.random() - 0.5) * 1;
                    subject.vitalSigns.respiratoryRate = Math.max(12, Math.min(20, subject.vitalSigns.respiratoryRate));

                    subject.vitalSigns.bloodGlucose += (Math.random() - 0.5) * 0.2;
                    subject.vitalSigns.bloodGlucose = Math.max(3.9, Math.min(6.1, subject.vitalSigns.bloodGlucose));

                    // QT间期与心率相关
                    const hrFactor = (subject.vitalSigns.heartRate - 72) * -1.5;
                    subject.vitalSigns.qtInterval = 400 + hrFactor + (Math.random() - 0.5) * 10;
                    subject.vitalSigns.qtInterval = Math.max(380, Math.min(420, subject.vitalSigns.qtInterval));

                    subject.vitalSigns.cvp += (Math.random() - 0.5) * 0.5;
                    subject.vitalSigns.cvp = Math.max(2, Math.min(12, subject.vitalSigns.cvp));

                    // 更新状态
                    updateSubjectStatus(subject);
                }
            });
        }

        // 更新检测对象状态
        function updateSubjectStatus(subject) {
            // 只对有效对象进行状态评估
            if (!subject.isActive || !subject.vitalSigns) {
                subject.status = 'inactive';
                return;
            }

            const vs = subject.vitalSigns;
            let hasAttention = false;
            let hasCritical = false;

            // 检查各项指标是否异常
            if (vs.heartRate < 60 || vs.heartRate > 100) hasAttention = true;
            if (vs.systolicBP < 90 || vs.systolicBP > 140) hasAttention = true;
            if (vs.diastolicBP < 60 || vs.diastolicBP > 90) hasAttention = true;
            if (vs.spo2 < 95) hasAttention = true;
            if (vs.temperature < 36.1 || vs.temperature > 37.2) hasAttention = true;
            if (vs.respiratoryRate < 12 || vs.respiratoryRate > 20) hasAttention = true;
            if (vs.bloodGlucose < 3.9 || vs.bloodGlucose > 6.1) hasAttention = true;
            if (vs.qtInterval < 380 || vs.qtInterval > 420) hasAttention = true;
            if (vs.cvp < 2 || vs.cvp > 12) hasAttention = true;

            // 检查是否有严重异常
            if (vs.heartRate < 50 || vs.heartRate > 120) hasCritical = true;
            if (vs.systolicBP < 80 || vs.systolicBP > 160) hasCritical = true;
            if (vs.spo2 < 90) hasCritical = true;
            if (vs.temperature < 35.5 || vs.temperature > 38.0) hasCritical = true;

            if (hasCritical) {
                subject.status = 'critical';
            } else if (hasAttention) {
                subject.status = 'attention';
            } else {
                subject.status = 'normal';
            }
        }

        // 进入详细监测界面
        function enterDetailMonitoring(subjectId) {
            const subject = subjects[subjectId];

            // 只有有效对象才能进入详细监测
            if (!subject || !subject.isActive || !subject.vitalSigns) {
                alert('该对象当前未连接，无法查看详细监测数据');
                return;
            }

            currentSubjectId = subjectId;

            // 隐藏管理界面，显示详细监测界面
            document.getElementById('managementView').classList.add('hidden');
            document.getElementById('detailMonitoringView').classList.add('active');

            // 停止管理更新
            if (managementInterval) {
                clearInterval(managementInterval);
                managementInterval = null;
            }

            // 更新详细监测界面标题
            document.getElementById('detailStatusInfo').textContent =
                `${subject.name} (${subject.id}) - 8项生理参数实时监测中`;

            // 渲染生理参数网格
            renderVitalsGrid(subject);

            // 开始详细监测更新
            startDetailMonitoringUpdate();
        }

        // 渲染生理参数网格
        function renderVitalsGrid(subject) {
            const vitalsGrid = document.getElementById('vitalsGrid');
            vitalsGrid.innerHTML = '';

            const vitalCards = [
                {
                    id: 'heartRate',
                    icon: '💓',
                    label: '心率 (次/分)',
                    value: Math.round(subject.vitalSigns.heartRate),
                    range: '参考范围: 60-100',
                    class: 'heart-rate'
                },
                {
                    id: 'bloodPressure',
                    icon: '🩸',
                    label: '血压 (mmHg)',
                    value: `${Math.round(subject.vitalSigns.systolicBP)}/${Math.round(subject.vitalSigns.diastolicBP)}`,
                    range: '参考范围: 90-140/60-90',
                    class: 'blood-pressure'
                },
                {
                    id: 'spo2',
                    icon: '🫁',
                    label: '血氧饱和度 (%)',
                    value: Math.round(subject.vitalSigns.spo2),
                    range: '参考范围: 95-100',
                    class: 'spo2'
                },
                {
                    id: 'temperature',
                    icon: '🌡️',
                    label: '体温 (°C)',
                    value: subject.vitalSigns.temperature.toFixed(1),
                    range: '参考范围: 36.1-37.2',
                    class: 'temperature'
                },
                {
                    id: 'respiratoryRate',
                    icon: '🌬️',
                    label: '呼吸频率 (次/分)',
                    value: Math.round(subject.vitalSigns.respiratoryRate),
                    range: '参考范围: 12-20',
                    class: 'respiratory-rate'
                },
                {
                    id: 'bloodGlucose',
                    icon: '🍯',
                    label: '血糖 (mmol/L)',
                    value: subject.vitalSigns.bloodGlucose.toFixed(1),
                    range: '参考范围: 3.9-6.1',
                    class: 'blood-glucose'
                },
                {
                    id: 'ecgRhythm',
                    icon: '📈',
                    label: '心电节律',
                    value: getECGRhythmText(subject.vitalSigns.qtInterval),
                    range: `QT间期: ${Math.round(subject.vitalSigns.qtInterval)}ms`,
                    class: 'ecg-rhythm'
                },
                {
                    id: 'cvp',
                    icon: '🔄',
                    label: '中心静脉压 (mmHg)',
                    value: Math.round(subject.vitalSigns.cvp),
                    range: '参考范围: 2-12',
                    class: 'cvp'
                }
            ];

            vitalCards.forEach(card => {
                const cardElement = document.createElement('div');
                cardElement.className = `vital-card ${card.class}`;
                cardElement.onclick = () => openParameterDetailView(card.id);
                cardElement.innerHTML = `
                    <div class="icon">${card.icon}</div>
                    <div class="value">${card.value}</div>
                    <div class="label">${card.label}</div>
                    <div class="range">${card.range}</div>
                `;
                vitalsGrid.appendChild(cardElement);
            });
        }

        // 获取心电节律文本
        function getECGRhythmText(qtInterval) {
            const qtValue = Math.round(qtInterval);
            if (qtValue > 420) return '窦缓';
            if (qtValue < 380) return '窦速';
            return '窦性';
        }

        // 开始详细监测更新
        function startDetailMonitoringUpdate() {
            const detailInterval = setInterval(() => {
                if (!currentSubjectId) {
                    clearInterval(detailInterval);
                    return;
                }

                const subject = subjects[currentSubjectId];
                if (!subject) {
                    clearInterval(detailInterval);
                    return;
                }

                // 更新当前对象的数据
                updateSingleSubjectData(subject);

                // 重新渲染生理参数网格
                renderVitalsGrid(subject);

                // 添加心跳动画
                const heartCard = document.querySelector('.heart-rate');
                if (heartCard) {
                    heartCard.classList.add('pulse-animation');
                    setTimeout(() => heartCard.classList.remove('pulse-animation'), 1000);
                }
            }, 3000); // 每3秒更新一次
        }

        // 更新单个检测对象数据
        function updateSingleSubjectData(subject) {
            // 心率变化
            subject.vitalSigns.heartRate += (Math.random() - 0.5) * 3;
            subject.vitalSigns.heartRate = Math.max(68, Math.min(82, subject.vitalSigns.heartRate));

            // 血压变化
            subject.vitalSigns.systolicBP += (Math.random() - 0.5) * 4;
            subject.vitalSigns.systolicBP = Math.max(115, Math.min(130, subject.vitalSigns.systolicBP));
            subject.vitalSigns.diastolicBP += (Math.random() - 0.5) * 3;
            subject.vitalSigns.diastolicBP = Math.max(75, Math.min(85, subject.vitalSigns.diastolicBP));

            // 血氧变化（非常稳定）
            subject.vitalSigns.spo2 += (Math.random() - 0.5) * 0.5;
            subject.vitalSigns.spo2 = Math.max(97, Math.min(99, subject.vitalSigns.spo2));

            // 体温变化（极其稳定）
            subject.vitalSigns.temperature += (Math.random() - 0.5) * 0.1;
            subject.vitalSigns.temperature = Math.max(36.3, Math.min(36.7, subject.vitalSigns.temperature));

            // 呼吸频率变化
            subject.vitalSigns.respiratoryRate += (Math.random() - 0.5) * 1;
            subject.vitalSigns.respiratoryRate = Math.max(15, Math.min(17, subject.vitalSigns.respiratoryRate));

            // 血糖变化（相对稳定）
            subject.vitalSigns.bloodGlucose += (Math.random() - 0.5) * 0.2;
            subject.vitalSigns.bloodGlucose = Math.max(4.8, Math.min(5.6, subject.vitalSigns.bloodGlucose));

            // QT间期变化（与心率相关）
            const hrFactor = (subject.vitalSigns.heartRate - 72) * -1.5;
            subject.vitalSigns.qtInterval = 400 + hrFactor + (Math.random() - 0.5) * 10;
            subject.vitalSigns.qtInterval = Math.max(380, Math.min(420, subject.vitalSigns.qtInterval));

            // 中心静脉压变化
            subject.vitalSigns.cvp += (Math.random() - 0.5) * 0.5;
            subject.vitalSigns.cvp = Math.max(7, Math.min(9, subject.vitalSigns.cvp));
        }

        // 返回管理界面
        function backToManagement() {
            // 隐藏详细监测界面，显示管理界面
            document.getElementById('detailMonitoringView').classList.remove('active');
            document.getElementById('managementView').classList.remove('hidden');

            // 清除当前对象ID
            currentSubjectId = null;

            // 重新开始管理更新
            startManagementUpdate();
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (managementInterval) {
                clearInterval(managementInterval);
            }
        });

        // 打开参数详细视图
        function openParameterDetailView(parameterType) {
            console.log('🔍 打开参数详细视图:', parameterType);

            if (!currentSubjectId) {
                console.error('❌ 当前对象ID为空');
                return;
            }

            const subject = subjects[currentSubjectId];
            if (!subject || !subject.isActive || !subject.vitalSigns) {
                console.error('❌ 对象无效或未激活');
                return;
            }

            currentParameterType = parameterType;

            // 隐藏详细监测界面，显示参数详细视图
            document.getElementById('detailMonitoringView').classList.remove('active');
            document.getElementById('parameterDetailView').classList.add('active');

            // 生成参数历史数据
            generateParameterHistoryData(subject, parameterType);

            // 延迟配置参数详细视图，确保界面已显示
            setTimeout(() => {
                setupParameterDetailView(parameterType, subject);
                startParameterDetailUpdate();
            }, 150);

            console.log('✅ 参数详细视图已打开');
        }

        // 生成参数历史数据
        function generateParameterHistoryData(subject, parameterType) {
            console.log('📊 生成参数历史数据:', parameterType);

            const now = new Date();
            parameterDataHistory.timeLabels = [];

            // 确保参数数组存在并清空
            parameterDataHistory[parameterType] = [];

            // 生成过去30分钟的历史数据
            for (let i = 29; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 60000);
                parameterDataHistory.timeLabels.push(time.toLocaleTimeString().slice(0, 5));

                let baseValue, minVal, maxVal;
                switch(parameterType) {
                    case 'heartRate':
                        baseValue = subject.vitalSigns.heartRate + (Math.random() - 0.5) * 8;
                        minVal = 65; maxVal = 85;
                        break;
                    case 'bloodPressure':
                        baseValue = subject.vitalSigns.systolicBP + (Math.random() - 0.5) * 10;
                        minVal = 110; maxVal = 135;
                        break;
                    case 'spo2':
                        baseValue = subject.vitalSigns.spo2 + (Math.random() - 0.5) * 2;
                        minVal = 96; maxVal = 100;
                        break;
                    case 'temperature':
                        baseValue = subject.vitalSigns.temperature + (Math.random() - 0.5) * 0.4;
                        minVal = 36.2; maxVal = 36.8;
                        break;
                    case 'respiratoryRate':
                        baseValue = subject.vitalSigns.respiratoryRate + (Math.random() - 0.5) * 4;
                        minVal = 14; maxVal = 18;
                        break;
                    case 'bloodGlucose':
                        baseValue = subject.vitalSigns.bloodGlucose + (Math.random() - 0.5) * 0.8;
                        minVal = 4.5; maxVal = 5.8;
                        break;
                    case 'ecgRhythm':
                        baseValue = subject.vitalSigns.qtInterval + (Math.random() - 0.5) * 30;
                        minVal = 380; maxVal = 420;
                        break;
                    case 'cvp':
                        baseValue = subject.vitalSigns.cvp + (Math.random() - 0.5) * 4;
                        minVal = 6; maxVal = 10;
                        break;
                    default:
                        console.warn('⚠️ 未知参数类型:', parameterType);
                        baseValue = 50;
                        minVal = 40; maxVal = 60;
                        break;
                }

                const value = Math.max(minVal, Math.min(maxVal, baseValue));
                if (parameterType === 'temperature' || parameterType === 'bloodGlucose') {
                    parameterDataHistory[parameterType].push(parseFloat(value.toFixed(1)));
                } else {
                    parameterDataHistory[parameterType].push(Math.round(value));
                }
            }

            console.log('✅ 历史数据生成完成:', {
                parameterType,
                dataPoints: parameterDataHistory[parameterType].length,
                timeLabels: parameterDataHistory.timeLabels.length,
                sampleData: parameterDataHistory[parameterType].slice(0, 5)
            });
        }

        // 配置参数详细视图
        function setupParameterDetailView(parameterType, subject) {
            const parameterConfigs = {
                heartRate: {
                    icon: '💓',
                    title: '心率分析',
                    unit: '次/分',
                    color: '#e74c3c',
                    getCurrentValue: () => Math.round(subject.vitalSigns.heartRate),
                    getDataArray: () => {
                        const data = parameterDataHistory.heartRate || [];
                        console.log('💓 心率数据获取:', data.length, '个数据点');
                        return data;
                    }
                },
                bloodPressure: {
                    icon: '🩸',
                    title: '血压分析',
                    unit: 'mmHg',
                    color: '#8e44ad',
                    getCurrentValue: () => `${Math.round(subject.vitalSigns.systolicBP)}/${Math.round(subject.vitalSigns.diastolicBP)}`,
                    getDataArray: () => {
                        const data = parameterDataHistory.bloodPressure || parameterDataHistory.systolicBP || [];
                        console.log('🩸 血压数据获取:', data.length, '个数据点');
                        return data;
                    }
                },
                spo2: {
                    icon: '🫁',
                    title: '血氧分析',
                    unit: '%',
                    color: '#3498db',
                    getCurrentValue: () => Math.round(subject.vitalSigns.spo2),
                    getDataArray: () => parameterDataHistory.spo2
                },
                temperature: {
                    icon: '🌡️',
                    title: '体温分析',
                    unit: '°C',
                    color: '#f39c12',
                    getCurrentValue: () => subject.vitalSigns.temperature.toFixed(1),
                    getDataArray: () => parameterDataHistory.temperature
                },
                respiratoryRate: {
                    icon: '🌬️',
                    title: '呼吸分析',
                    unit: '次/分',
                    color: '#27ae60',
                    getCurrentValue: () => Math.round(subject.vitalSigns.respiratoryRate),
                    getDataArray: () => parameterDataHistory.respiratoryRate
                },
                bloodGlucose: {
                    icon: '🍯',
                    title: '血糖分析',
                    unit: 'mmol/L',
                    color: '#e67e22',
                    getCurrentValue: () => subject.vitalSigns.bloodGlucose.toFixed(1),
                    getDataArray: () => parameterDataHistory.bloodGlucose
                },
                ecgRhythm: {
                    icon: '📈',
                    title: '心电分析',
                    unit: 'ms',
                    color: '#34495e',
                    getCurrentValue: () => Math.round(subject.vitalSigns.qtInterval),
                    getDataArray: () => {
                        const data = parameterDataHistory.ecgRhythm || parameterDataHistory.qtInterval || [];
                        console.log('📈 心电数据获取:', data.length, '个数据点');
                        return data;
                    }
                },
                cvp: {
                    icon: '🔄',
                    title: '静脉压分析',
                    unit: 'mmHg',
                    color: '#9b59b6',
                    getCurrentValue: () => Math.round(subject.vitalSigns.cvp),
                    getDataArray: () => parameterDataHistory.cvp
                }
            };

            const config = parameterConfigs[parameterType];
            if (!config) return;

            // 更新参数详细视图标题和图标
            document.getElementById('parameterDetailIcon').textContent = config.icon;
            document.getElementById('parameterDetailIcon').style.color = config.color;
            document.getElementById('parameterDetailTitle').textContent = config.title;
            document.getElementById('parameterDetailCurrentValue').textContent = `${config.getCurrentValue()} ${config.unit}`;
            document.getElementById('parameterDetailCurrentValue').style.color = config.color;
            document.getElementById('parameterWaveformTitle').textContent = `${config.title.replace('分析', '')}变化趋势`;

            // 更新信息面板
            updateParameterDetailInfo(parameterType, config);

            // 初始化参数详细图表
            initializeParameterDetailChart(parameterType, config);
        }

        // 初始化参数详细图表
        function initializeParameterDetailChart(parameterType, config) {
            console.log('📈 初始化参数详细图表:', parameterType);

            if (parameterDetailChart) {
                parameterDetailChart.destroy();
                parameterDetailChart = null;
                console.log('🗑️ 销毁旧图表');
            }

            const canvas = document.getElementById('parameterDetailChart');
            if (!canvas) {
                console.error('❌ 未找到图表画布元素');
                alert('图表画布元素不存在，请刷新页面重试');
                return;
            }

            // 检查画布是否可见
            const rect = canvas.getBoundingClientRect();
            console.log('🎨 画布元素信息:', {
                width: canvas.offsetWidth,
                height: canvas.offsetHeight,
                visible: rect.width > 0 && rect.height > 0,
                rect: rect
            });

            if (rect.width === 0 || rect.height === 0) {
                console.warn('⚠️ 画布不可见，延迟重试');
                setTimeout(() => initializeParameterDetailChart(parameterType, config), 200);
                return;
            }

            const ctx = canvas.getContext('2d');
            const dataArray = config.getDataArray();

            console.log('📊 图表数据:', {
                labels: parameterDataHistory.timeLabels.length,
                data: dataArray ? dataArray.length : 0,
                config: config.title,
                sampleData: dataArray ? dataArray.slice(0, 5) : []
            });

            try {
                parameterDetailChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: parameterDataHistory.timeLabels,
                        datasets: [{
                            label: config.title.replace('分析', ''),
                            data: dataArray || [],
                            borderColor: config.color,
                            backgroundColor: config.color + '15',
                            tension: 0.4,
                            fill: true,
                            pointRadius: 3,
                            pointHoverRadius: 6,
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    font: { size: 14 },
                                    usePointStyle: true
                                }
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: '时间',
                                    font: { size: 14 }
                                },
                                grid: { color: 'rgba(0,0,0,0.1)' }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: `${config.title.replace('分析', '')} (${config.unit})`,
                                    font: { size: 14 }
                                },
                                grid: { color: 'rgba(0,0,0,0.1)' }
                            }
                        },
                        animation: {
                            duration: 800,
                            easing: 'easeInOutQuart'
                        }
                    }
                });

                console.log('✅ 参数详细图表初始化成功');
            } catch (error) {
                console.error('❌ 图表初始化失败:', error);
            }
        }

        // 开始参数详细更新
        function startParameterDetailUpdate() {
            const parameterDetailInterval = setInterval(() => {
                if (!currentSubjectId || !currentParameterType) {
                    clearInterval(parameterDetailInterval);
                    return;
                }

                const subject = subjects[currentSubjectId];
                if (!subject || !subject.isActive || !subject.vitalSigns) {
                    clearInterval(parameterDetailInterval);
                    return;
                }

                // 更新当前对象的数据
                updateSingleSubjectData(subject);

                // 更新参数历史数据
                updateParameterHistoryData(subject, currentParameterType);

                // 更新参数详细视图
                updateParameterDetailView();
            }, 3000);
        }

        // 更新参数历史数据
        function updateParameterHistoryData(subject, parameterType) {
            const now = new Date().toLocaleTimeString().slice(0, 5);

            parameterDataHistory.timeLabels.push(now);

            let newValue;
            let dataKey = parameterType;

            switch(parameterType) {
                case 'heartRate':
                    newValue = Math.round(subject.vitalSigns.heartRate);
                    break;
                case 'bloodPressure':
                    newValue = Math.round(subject.vitalSigns.systolicBP);
                    break;
                case 'spo2':
                    newValue = Math.round(subject.vitalSigns.spo2);
                    break;
                case 'temperature':
                    newValue = parseFloat(subject.vitalSigns.temperature.toFixed(1));
                    break;
                case 'respiratoryRate':
                    newValue = Math.round(subject.vitalSigns.respiratoryRate);
                    break;
                case 'bloodGlucose':
                    newValue = parseFloat(subject.vitalSigns.bloodGlucose.toFixed(1));
                    break;
                case 'ecgRhythm':
                    newValue = Math.round(subject.vitalSigns.qtInterval);
                    break;
                case 'cvp':
                    newValue = Math.round(subject.vitalSigns.cvp);
                    break;
                default:
                    console.warn('⚠️ 未知参数类型:', parameterType);
                    return;
            }

            // 确保数据数组存在
            if (!parameterDataHistory[dataKey]) {
                parameterDataHistory[dataKey] = [];
            }

            parameterDataHistory[dataKey].push(newValue);

            // 限制数据点数量
            const maxPoints = 30;
            if (parameterDataHistory.timeLabels.length > maxPoints) {
                parameterDataHistory.timeLabels.shift();
                if (parameterDataHistory[dataKey].length > maxPoints) {
                    parameterDataHistory[dataKey].shift();
                }
            }

            console.log('📊 更新参数历史数据:', {
                parameterType,
                newValue,
                dataLength: parameterDataHistory[dataKey].length
            });
        }

        // 更新参数详细视图
        function updateParameterDetailView() {
            if (!currentSubjectId || !currentParameterType) return;

            const subject = subjects[currentSubjectId];
            if (!subject || !subject.isActive || !subject.vitalSigns) return;

            // 重新配置视图
            setupParameterDetailView(currentParameterType, subject);

            // 更新图表
            if (parameterDetailChart) {
                parameterDetailChart.data.labels = parameterDataHistory.timeLabels;
                parameterDetailChart.data.datasets[0].data = parameterDataHistory[currentParameterType];
                parameterDetailChart.update('none');
            }
        }

        // 更新参数详细信息
        function updateParameterDetailInfo(parameterType, config) {
            const dataArray = config.getDataArray();

            // 更新当前值
            document.getElementById('parameterCurrentValueInfo').textContent = config.getCurrentValue();

            if (dataArray && dataArray.length > 0) {
                // 计算统计信息
                const sum = dataArray.reduce((a, b) => a + b, 0);
                const avg = sum / dataArray.length;
                const max = Math.max(...dataArray);
                const min = Math.min(...dataArray);

                document.getElementById('parameterAverageValueInfo').textContent = avg.toFixed(1);
                document.getElementById('parameterMaxValueInfo').textContent = max.toFixed(1);
                document.getElementById('parameterMinValueInfo').textContent = min.toFixed(1);

                // 判断状态
                const currentValue = parseFloat(config.getCurrentValue());
                let status = '正常';
                let statusColor = '#4caf50';

                // 根据医学标准判断状态
                switch(parameterType) {
                    case 'heartRate':
                        if (currentValue < 60 || currentValue > 100) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    case 'spo2':
                        if (currentValue < 95) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    case 'temperature':
                        if (currentValue < 36.1 || currentValue > 37.2) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    case 'respiratoryRate':
                        if (currentValue < 12 || currentValue > 20) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    case 'bloodGlucose':
                        if (currentValue < 3.9 || currentValue > 6.1) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    case 'cvp':
                        if (currentValue < 2 || currentValue > 12) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    case 'ecgRhythm':
                        if (currentValue < 380 || currentValue > 420) {
                            status = '注意';
                            statusColor = '#ff9800';
                        }
                        break;
                    default:
                        if (parameterType === 'bloodPressure') {
                            const systolic = Math.round(subjects[currentSubjectId].vitalSigns.systolicBP);
                            if (systolic < 90 || systolic > 140) {
                                status = '注意';
                                statusColor = '#ff9800';
                            }
                        }
                        break;
                }

                document.getElementById('parameterStatusInfo').textContent = status;
                document.getElementById('parameterStatusInfo').style.color = statusColor;

                // 判断趋势
                if (dataArray.length >= 5) {
                    const recent = dataArray.slice(-5);
                    const first = recent[0];
                    const last = recent[recent.length - 1];
                    const diff = last - first;

                    let trend = '稳定';
                    const threshold = parameterType === 'temperature' ? 0.2 :
                                    parameterType === 'bloodGlucose' ? 0.3 : 1;

                    if (Math.abs(diff) > threshold) {
                        trend = diff > 0 ? '上升' : '下降';
                    }

                    document.getElementById('parameterTrendInfo').textContent = trend;
                } else {
                    document.getElementById('parameterTrendInfo').textContent = '稳定';
                }
            } else {
                // 默认值
                document.getElementById('parameterAverageValueInfo').textContent = '--';
                document.getElementById('parameterMaxValueInfo').textContent = '--';
                document.getElementById('parameterMinValueInfo').textContent = '--';
                document.getElementById('parameterStatusInfo').textContent = '正常';
                document.getElementById('parameterStatusInfo').style.color = '#4caf50';
                document.getElementById('parameterTrendInfo').textContent = '稳定';
            }
        }

        // 返回详细监测界面
        function backToDetailMonitoring() {
            // 隐藏参数详细视图，显示详细监测界面
            document.getElementById('parameterDetailView').classList.remove('active');
            document.getElementById('detailMonitoringView').classList.add('active');

            // 清除当前参数类型
            currentParameterType = null;

            // 销毁参数详细图表
            if (parameterDetailChart) {
                parameterDetailChart.destroy();
                parameterDetailChart = null;
            }
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (managementInterval) {
                clearInterval(managementInterval);
            }
            if (parameterDetailChart) {
                parameterDetailChart.destroy();
            }
        });

        // 测试图表功能
        function testChart() {
            console.log('🧪 测试图表功能');

            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('❌ Chart.js未加载');
                alert('Chart.js库未加载，请检查网络连接');
                return;
            }

            console.log('✅ Chart.js已加载，版本:', Chart.version);

            // 检查画布元素
            const canvas = document.getElementById('parameterDetailChart');
            if (!canvas) {
                console.error('❌ 画布元素不存在');
                return;
            }

            console.log('✅ 画布元素存在');

            // 测试简单图表
            const ctx = canvas.getContext('2d');
            const testChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1', '2', '3', '4', '5'],
                    datasets: [{
                        label: '测试数据',
                        data: [10, 20, 15, 25, 30],
                        borderColor: '#e74c3c',
                        backgroundColor: '#e74c3c15'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            console.log('✅ 测试图表创建成功');

            // 5秒后销毁测试图表
            setTimeout(() => {
                testChart.destroy();
                console.log('🗑️ 测试图表已销毁');
            }, 5000);
        }

        // 测试图表功能
        function testParameterChart() {
            console.log('🧪 测试参数图表功能');

            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('❌ Chart.js未加载');
                alert('Chart.js库未加载，请检查网络连接');
                return;
            }

            console.log('✅ Chart.js已加载，版本:', Chart.version);

            // 检查画布元素
            const canvas = document.getElementById('parameterDetailChart');
            if (!canvas) {
                console.error('❌ 参数详细图表画布不存在');
                return;
            }

            console.log('✅ 参数详细图表画布存在');

            // 显示参数详细视图
            document.getElementById('detailMonitoringView').classList.remove('active');
            document.getElementById('parameterDetailView').classList.add('active');

            // 延迟测试图表
            setTimeout(() => {
                try {
                    const ctx = canvas.getContext('2d');
                    const testChart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: ['1', '2', '3', '4', '5'],
                            datasets: [{
                                label: '测试数据',
                                data: [65, 70, 68, 75, 72],
                                borderColor: '#e74c3c',
                                backgroundColor: '#e74c3c20',
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });

                    console.log('✅ 测试图表创建成功');

                    // 5秒后销毁测试图表
                    setTimeout(() => {
                        testChart.destroy();
                        console.log('🗑️ 测试图表已销毁');

                        // 返回详细监测界面
                        document.getElementById('parameterDetailView').classList.remove('active');
                        document.getElementById('detailMonitoringView').classList.add('active');
                    }, 5000);

                } catch (error) {
                    console.error('❌ 测试图表创建失败:', error);
                }
            }, 200);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 系统初始化开始');

            try {
                // 检查关键DOM元素
                const requiredElements = ['subjectsGrid', 'managementStatusInfo', 'managementView'];
                for (const elementId of requiredElements) {
                    const element = document.getElementById(elementId);
                    if (!element) {
                        throw new Error(`关键DOM元素不存在: ${elementId}`);
                    }
                    console.log(`✅ DOM元素检查通过: ${elementId}`);
                }

                // 检查Chart.js是否加载
                if (typeof Chart === 'undefined') {
                    console.error('❌ Chart.js未加载');
                    document.getElementById('managementStatusInfo').textContent = '系统加载失败 - Chart.js库未加载';
                    document.getElementById('managementStatusInfo').style.background = '#ffebee';
                    document.getElementById('managementStatusInfo').style.color = '#d32f2f';
                    return;
                }

                console.log('✅ Chart.js已加载，版本:', Chart.version);

                // 检查数据
                const subjectCount = Object.keys(subjects).length;
                console.log(`📊 检测对象数据: ${subjectCount} 个对象`);

                if (subjectCount === 0) {
                    throw new Error('没有检测对象数据');
                }

                // 初始化系统
                initializeManagementSystem();
                startManagementUpdate();

                console.log('🔧 系统初始化完成，可以使用testParameterChart()函数测试图表');

                // 验证渲染结果
                setTimeout(() => {
                    const subjectsGrid = document.getElementById('subjectsGrid');
                    const cardCount = subjectsGrid.children.length;
                    console.log(`🎯 渲染验证: ${cardCount} 个对象卡片已创建`);

                    if (cardCount === 0) {
                        console.error('❌ 对象卡片渲染失败');
                        document.getElementById('managementStatusInfo').textContent = '系统渲染失败 - 对象卡片未显示';
                        document.getElementById('managementStatusInfo').style.background = '#ffebee';
                        document.getElementById('managementStatusInfo').style.color = '#d32f2f';
                    } else {
                        console.log('✅ 系统渲染成功');
                    }
                }, 1000);

            } catch (error) {
                console.error('💥 系统初始化失败:', error);
                const statusElement = document.getElementById('managementStatusInfo');
                if (statusElement) {
                    statusElement.textContent = `系统初始化失败: ${error.message}`;
                    statusElement.style.background = '#ffebee';
                    statusElement.style.color = '#d32f2f';
                }
                alert(`系统初始化失败: ${error.message}\n请刷新页面重试`);
            }
        });
    </script>
</body>
</html>
