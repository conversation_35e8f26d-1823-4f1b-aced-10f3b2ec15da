/*
 * health_config.h
 *
 * 健康监测系统配置管理模块
 * 管理传感器阈值、检测参数和报警配置
 */

#ifndef __HEALTH_CONFIG_H
#define __HEALTH_CONFIG_H

#include "ch32v30x.h"

#ifdef __cplusplus
extern "C" {
#endif

// 传感器类型定义
typedef enum {
    SENSOR_TYPE_HEART_RATE = 0,    // 心率传感器
    SENSOR_TYPE_SPO2,              // 血氧传感器  
    SENSOR_TYPE_TEMPERATURE,       // 体温传感器
    SENSOR_TYPE_MAX
} SENSOR_TYPE_E;

// 异常检测类型定义
typedef enum {
    ANOMALY_TYPE_THRESHOLD = 0,    // 阈值异常
    ANOMALY_TYPE_FLUCTUATION,      // 波动异常
    ANOMALY_TYPE_TREND,            // 趋势异常
    ANOMALY_TYPE_OUTLIER,          // 离群点异常
    ANOMALY_TYPE_MAX
} ANOMALY_TYPE_E;

// 报警级别定义
typedef enum {
    ALARM_LEVEL_NONE = 0,          // 无报警
    ALARM_LEVEL_WARNING,           // 警告级别
    ALARM_LEVEL_ATTENTION,         // 注意级别
    ALARM_LEVEL_DANGER,            // 危险级别
    ALARM_LEVEL_EMERGENCY,         // 紧急级别
    ALARM_LEVEL_MAX
} ALARM_LEVEL_E;

// 传感器阈值配置结构体
typedef struct {
    u16 un_min_normal;             // 正常范围最小值
    u16 un_max_normal;             // 正常范围最大值
    u16 un_min_warning;            // 警告范围最小值
    u16 un_max_warning;            // 警告范围最大值
    u16 un_min_danger;             // 危险范围最小值
    u16 un_max_danger;             // 危险范围最大值
    u8 ch_enabled;                 // 是否启用检测
} SENSOR_THRESHOLD_T;

// 异常检测参数配置结构体
typedef struct {
    u8 un_window_size;             // 滑动窗口大小
    u16 un_change_rate_threshold;  // 变化率阈值(千分比)
    u16 un_std_dev_threshold;      // 标准差阈值
    u16 un_trend_threshold;        // 趋势检测阈值
    u8 un_outlier_factor;          // 离群点检测因子
    u8 ch_enabled;                 // 是否启用异常检测
} ANOMALY_DETECT_CONFIG_T;

// 报警配置结构体
typedef struct {
    u16 un_beep_frequency;         // 蜂鸣器频率(Hz)
    u16 un_beep_duration;          // 蜂鸣器持续时间(ms)
    u16 un_beep_interval;          // 蜂鸣器间隔时间(ms)
    u8 un_repeat_count;            // 重复次数
    u8 ch_enabled;                 // 是否启用报警
} ALARM_CONFIG_T;

// 系统配置结构体
typedef struct {
    SENSOR_THRESHOLD_T ast_sensor_threshold[SENSOR_TYPE_MAX];      // 传感器阈值配置
    ANOMALY_DETECT_CONFIG_T ast_anomaly_config[SENSOR_TYPE_MAX];   // 异常检测配置
    ALARM_CONFIG_T ast_alarm_config[ALARM_LEVEL_MAX];              // 报警配置
    u16 un_data_buffer_size;       // 数据缓冲区大小
    u16 un_sampling_interval;      // 采样间隔(ms)
    u8 ch_system_enabled;          // 系统总开关
    u8 ch_debug_mode;              // 调试模式开关
} HEALTH_CONFIG_T;

// 全局配置变量声明
extern HEALTH_CONFIG_T g_health_config;

// 配置管理接口函数声明
void CONFIG_Init(void);                                                    // 初始化配置
u8 CONFIG_SetSensorThreshold(SENSOR_TYPE_E sensor_type, 
                             SENSOR_THRESHOLD_T *pst_threshold);           // 设置传感器阈值
u8 CONFIG_GetSensorThreshold(SENSOR_TYPE_E sensor_type, 
                             SENSOR_THRESHOLD_T *pst_threshold);           // 获取传感器阈值
u8 CONFIG_SetAnomalyConfig(SENSOR_TYPE_E sensor_type, 
                          ANOMALY_DETECT_CONFIG_T *pst_config);            // 设置异常检测配置
u8 CONFIG_GetAnomalyConfig(SENSOR_TYPE_E sensor_type, 
                          ANOMALY_DETECT_CONFIG_T *pst_config);            // 获取异常检测配置
u8 CONFIG_SetAlarmConfig(ALARM_LEVEL_E alarm_level, 
                        ALARM_CONFIG_T *pst_config);                       // 设置报警配置
u8 CONFIG_GetAlarmConfig(ALARM_LEVEL_E alarm_level, 
                        ALARM_CONFIG_T *pst_config);                       // 获取报警配置
u8 CONFIG_SetSystemParam(u16 buffer_size, u16 sampling_interval);          // 设置系统参数
u8 CONFIG_GetSystemParam(u16 *pun_buffer_size, u16 *pun_sampling_interval); // 获取系统参数
u8 CONFIG_EnableSystem(u8 ch_enable);                                      // 系统总开关
u8 CONFIG_EnableDebugMode(u8 ch_enable);                                   // 调试模式开关
u8 CONFIG_IsSystemEnabled(void);                                           // 检查系统是否启用
u8 CONFIG_IsDebugModeEnabled(void);                                        // 检查调试模式是否启用
void CONFIG_PrintConfig(void);                                             // 打印当前配置(调试用)

#ifdef __cplusplus
}
#endif

#endif /* __HEALTH_CONFIG_H */
