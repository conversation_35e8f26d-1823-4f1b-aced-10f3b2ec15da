<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版医学监测系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 15px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 25px;
        }

        .header h1 {
            color: #333;
            font-size: 2.3em;
            margin-bottom: 8px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .status-info {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.1em;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        /* 主界面 */
        .main-view {
            display: block;
        }

        .main-view.hidden {
            display: none;
        }

        .vitals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .vital-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .vital-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .vital-card .icon {
            font-size: 3.2em;
            margin-bottom: 12px;
            display: block;
        }

        .vital-card .value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .vital-card .label {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 8px;
        }

        .vital-card .range {
            color: #999;
            font-size: 0.9em;
        }

        /* 生理参数颜色主题 */
        .heart-rate .icon { color: #e74c3c; }
        .heart-rate .value { color: #e74c3c; }
        .blood-pressure .icon { color: #8e44ad; }
        .blood-pressure .value { color: #8e44ad; }
        .spo2 .icon { color: #3498db; }
        .spo2 .value { color: #3498db; }
        .temperature .icon { color: #f39c12; }
        .temperature .value { color: #f39c12; }

        /* 波形视图 */
        .waveform-view {
            display: none;
        }

        .waveform-view.active {
            display: block;
        }

        .waveform-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .waveform-title {
            display: flex;
            align-items: center;
            gap: 18px;
        }

        .waveform-title .icon {
            font-size: 2.8em;
        }

        .waveform-title .info h2 {
            color: #333;
            margin-bottom: 8px;
            font-size: 1.8em;
        }

        .waveform-title .info .current-value {
            font-size: 1.6em;
            font-weight: bold;
        }

        .back-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1em;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
        }

        .chart-container h3 {
            margin-bottom: 18px;
            color: #333;
            text-align: center;
            font-size: 1.4em;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
        }

        .stats-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 18px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-item .value {
            font-size: 1.6em;
            font-weight: bold;
            color: #333;
            margin-bottom: 6px;
        }

        .stat-item .label {
            color: #666;
            font-size: 0.95em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 医学生理监测系统</h1>
            <p>点击生理参数查看详细波形图</p>
        </div>

        <div class="status-info" id="statusInfo">
            系统运行正常 - 生理参数实时监测中
        </div>

        <!-- 主界面 -->
        <div class="main-view" id="mainView">
            <div class="vitals-grid">
                <div class="vital-card heart-rate" onclick="showWaveform('heartRate')">
                    <div class="icon">💓</div>
                    <div class="value" id="heartRateValue">72</div>
                    <div class="label">心率 (次/分)</div>
                    <div class="range">参考范围: 60-100</div>
                </div>
                <div class="vital-card blood-pressure" onclick="showWaveform('bloodPressure')">
                    <div class="icon">🩸</div>
                    <div class="value" id="bloodPressureValue">120/80</div>
                    <div class="label">血压 (mmHg)</div>
                    <div class="range">参考范围: 90-140/60-90</div>
                </div>
                <div class="vital-card spo2" onclick="showWaveform('spo2')">
                    <div class="icon">🫁</div>
                    <div class="value" id="spo2Value">98</div>
                    <div class="label">血氧饱和度 (%)</div>
                    <div class="range">参考范围: 95-100</div>
                </div>
                <div class="vital-card temperature" onclick="showWaveform('temperature')">
                    <div class="icon">🌡️</div>
                    <div class="value" id="temperatureValue">36.5</div>
                    <div class="label">体温 (°C)</div>
                    <div class="range">参考范围: 36.1-37.2</div>
                </div>
            </div>
        </div>

        <!-- 波形视图 -->
        <div class="waveform-view" id="waveformView">
            <div class="waveform-header">
                <div class="waveform-title">
                    <div class="icon" id="waveformIcon">💓</div>
                    <div class="info">
                        <h2 id="waveformTitle">心率分析</h2>
                        <div class="current-value" id="waveformCurrentValue">72 次/分</div>
                    </div>
                </div>
                <button class="back-btn" onclick="backToMain()">← 返回总览</button>
            </div>

            <div class="chart-container">
                <h3 id="chartTitle">心率变化趋势</h3>
                <div class="chart-wrapper">
                    <canvas id="waveformChart"></canvas>
                </div>
            </div>

            <div class="stats-panel">
                <h3>数据统计</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="value" id="currentStat">72</div>
                        <div class="label">当前值</div>
                    </div>
                    <div class="stat-item">
                        <div class="value" id="averageStat">74</div>
                        <div class="label">平均值</div>
                    </div>
                    <div class="stat-item">
                        <div class="value" id="maxStat">85</div>
                        <div class="label">最高值</div>
                    </div>
                    <div class="stat-item">
                        <div class="value" id="minStat">65</div>
                        <div class="label">最低值</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentParameter = null;
        let waveformChart = null;
        let updateInterval = null;
        
        // 生理数据
        let vitalSigns = {
            heartRate: 72,
            systolicBP: 120,
            diastolicBP: 80,
            spo2: 98,
            temperature: 36.5
        };
        
        // 历史数据
        let historyData = {
            heartRate: [],
            bloodPressure: [],
            spo2: [],
            temperature: [],
            timeLabels: []
        };
        
        // 参数配置
        const parameterConfigs = {
            heartRate: {
                icon: '💓',
                title: '心率分析',
                unit: '次/分',
                color: '#e74c3c',
                getValue: () => Math.round(vitalSigns.heartRate),
                getDisplayValue: () => Math.round(vitalSigns.heartRate) + ' 次/分'
            },
            bloodPressure: {
                icon: '🩸',
                title: '血压分析',
                unit: 'mmHg',
                color: '#8e44ad',
                getValue: () => Math.round(vitalSigns.systolicBP),
                getDisplayValue: () => `${Math.round(vitalSigns.systolicBP)}/${Math.round(vitalSigns.diastolicBP)} mmHg`
            },
            spo2: {
                icon: '🫁',
                title: '血氧分析',
                unit: '%',
                color: '#3498db',
                getValue: () => Math.round(vitalSigns.spo2),
                getDisplayValue: () => Math.round(vitalSigns.spo2) + '%'
            },
            temperature: {
                icon: '🌡️',
                title: '体温分析',
                unit: '°C',
                color: '#f39c12',
                getValue: () => vitalSigns.temperature.toFixed(1),
                getDisplayValue: () => vitalSigns.temperature.toFixed(1) + '°C'
            }
        };
        
        // 初始化系统
        function initializeSystem() {
            generateHistoryData();
            updateMainDisplay();
        }

        // 生成历史数据
        function generateHistoryData() {
            console.log('📊 生成历史数据');

            const now = new Date();
            historyData.timeLabels = [];

            // 为每个参数初始化数组
            Object.keys(parameterConfigs).forEach(param => {
                historyData[param] = [];
            });

            // 生成过去30分钟的数据
            for (let i = 29; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 60000);
                historyData.timeLabels.push(time.toLocaleTimeString().slice(0, 5));

                // 心率数据
                const baseHR = 72 + (Math.random() - 0.5) * 8;
                historyData.heartRate.push(Math.round(Math.max(65, Math.min(85, baseHR))));

                // 血压数据（收缩压）
                const baseBP = 120 + (Math.random() - 0.5) * 10;
                historyData.bloodPressure.push(Math.round(Math.max(110, Math.min(135, baseBP))));

                // 血氧数据
                const baseSpo2 = 98 + (Math.random() - 0.5) * 2;
                historyData.spo2.push(Math.round(Math.max(96, Math.min(100, baseSpo2))));

                // 体温数据
                const baseTemp = 36.5 + (Math.random() - 0.5) * 0.4;
                historyData.temperature.push(parseFloat(Math.max(36.2, Math.min(36.8, baseTemp)).toFixed(1)));
            }

            console.log('✅ 历史数据生成完成');
        }

        // 更新主界面显示
        function updateMainDisplay() {
            document.getElementById('heartRateValue').textContent = Math.round(vitalSigns.heartRate);
            document.getElementById('bloodPressureValue').textContent = `${Math.round(vitalSigns.systolicBP)}/${Math.round(vitalSigns.diastolicBP)}`;
            document.getElementById('spo2Value').textContent = Math.round(vitalSigns.spo2);
            document.getElementById('temperatureValue').textContent = vitalSigns.temperature.toFixed(1);
        }

        // 开始数据更新
        function startDataUpdate() {
            updateInterval = setInterval(() => {
                updateVitalSigns();
                updateMainDisplay();
                updateHistoryData();

                // 如果在波形视图中，更新波形
                if (currentParameter && waveformChart) {
                    updateWaveformChart();
                }
            }, 3000);
        }

        // 更新生理数据
        function updateVitalSigns() {
            // 心率变化
            vitalSigns.heartRate += (Math.random() - 0.5) * 3;
            vitalSigns.heartRate = Math.max(68, Math.min(82, vitalSigns.heartRate));

            // 血压变化
            vitalSigns.systolicBP += (Math.random() - 0.5) * 4;
            vitalSigns.systolicBP = Math.max(115, Math.min(130, vitalSigns.systolicBP));
            vitalSigns.diastolicBP += (Math.random() - 0.5) * 3;
            vitalSigns.diastolicBP = Math.max(75, Math.min(85, vitalSigns.diastolicBP));

            // 血氧变化
            vitalSigns.spo2 += (Math.random() - 0.5) * 0.5;
            vitalSigns.spo2 = Math.max(97, Math.min(99, vitalSigns.spo2));

            // 体温变化
            vitalSigns.temperature += (Math.random() - 0.5) * 0.1;
            vitalSigns.temperature = Math.max(36.3, Math.min(36.7, vitalSigns.temperature));
        }

        // 更新历史数据
        function updateHistoryData() {
            const now = new Date().toLocaleTimeString().slice(0, 5);

            historyData.timeLabels.push(now);
            historyData.heartRate.push(Math.round(vitalSigns.heartRate));
            historyData.bloodPressure.push(Math.round(vitalSigns.systolicBP));
            historyData.spo2.push(Math.round(vitalSigns.spo2));
            historyData.temperature.push(parseFloat(vitalSigns.temperature.toFixed(1)));

            // 限制数据点数量
            const maxPoints = 30;
            if (historyData.timeLabels.length > maxPoints) {
                historyData.timeLabels.shift();
                Object.keys(parameterConfigs).forEach(param => {
                    if (historyData[param].length > maxPoints) {
                        historyData[param].shift();
                    }
                });
            }
        }

        // 显示波形图
        function showWaveform(parameterType) {
            console.log('📈 显示波形图:', parameterType);

            currentParameter = parameterType;
            const config = parameterConfigs[parameterType];

            if (!config) {
                console.error('❌ 未找到参数配置:', parameterType);
                return;
            }

            // 切换界面
            document.getElementById('mainView').classList.add('hidden');
            document.getElementById('waveformView').classList.add('active');

            // 更新波形视图标题
            document.getElementById('waveformIcon').textContent = config.icon;
            document.getElementById('waveformIcon').style.color = config.color;
            document.getElementById('waveformTitle').textContent = config.title;
            document.getElementById('waveformCurrentValue').textContent = config.getDisplayValue();
            document.getElementById('waveformCurrentValue').style.color = config.color;
            document.getElementById('chartTitle').textContent = `${config.title.replace('分析', '')}变化趋势`;

            // 延迟初始化图表，确保界面已显示
            setTimeout(() => {
                initializeWaveformChart(parameterType, config);
                updateStatistics(parameterType);
            }, 100);
        }

        // 初始化波形图表
        function initializeWaveformChart(parameterType, config) {
            console.log('🎨 初始化波形图表:', parameterType);

            // 销毁旧图表
            if (waveformChart) {
                waveformChart.destroy();
                waveformChart = null;
            }

            const canvas = document.getElementById('waveformChart');
            if (!canvas) {
                console.error('❌ 未找到图表画布');
                return;
            }

            const ctx = canvas.getContext('2d');
            const data = historyData[parameterType] || [];

            console.log('📊 图表数据:', {
                labels: historyData.timeLabels.length,
                data: data.length,
                sample: data.slice(-5)
            });

            try {
                waveformChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: historyData.timeLabels,
                        datasets: [{
                            label: config.title.replace('分析', ''),
                            data: data,
                            borderColor: config.color,
                            backgroundColor: config.color + '20',
                            tension: 0.4,
                            fill: true,
                            pointRadius: 3,
                            pointHoverRadius: 6,
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    font: { size: 14 },
                                    usePointStyle: true
                                }
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: '时间',
                                    font: { size: 14 }
                                },
                                grid: { color: 'rgba(0,0,0,0.1)' }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: `${config.title.replace('分析', '')} (${config.unit})`,
                                    font: { size: 14 }
                                },
                                grid: { color: 'rgba(0,0,0,0.1)' }
                            }
                        },
                        animation: {
                            duration: 800,
                            easing: 'easeInOutQuart'
                        }
                    }
                });

                console.log('✅ 波形图表创建成功');
            } catch (error) {
                console.error('❌ 图表创建失败:', error);
                alert('波形图创建失败，请刷新页面重试');
            }
        }

        // 更新波形图表
        function updateWaveformChart() {
            if (!waveformChart || !currentParameter) return;

            const config = parameterConfigs[currentParameter];
            const data = historyData[currentParameter] || [];

            // 更新当前值显示
            document.getElementById('waveformCurrentValue').textContent = config.getDisplayValue();

            // 更新图表数据
            waveformChart.data.labels = historyData.timeLabels;
            waveformChart.data.datasets[0].data = data;
            waveformChart.update('none');

            // 更新统计信息
            updateStatistics(currentParameter);
        }

        // 更新统计信息
        function updateStatistics(parameterType) {
            const config = parameterConfigs[parameterType];
            const data = historyData[parameterType] || [];

            if (data.length === 0) return;

            const current = config.getValue();
            const sum = data.reduce((a, b) => a + b, 0);
            const average = sum / data.length;
            const max = Math.max(...data);
            const min = Math.min(...data);

            document.getElementById('currentStat').textContent = current;
            document.getElementById('averageStat').textContent = average.toFixed(1);
            document.getElementById('maxStat').textContent = max.toFixed(1);
            document.getElementById('minStat').textContent = min.toFixed(1);
        }

        // 返回主界面
        function backToMain() {
            console.log('🔙 返回主界面');

            // 切换界面
            document.getElementById('waveformView').classList.remove('active');
            document.getElementById('mainView').classList.remove('hidden');

            // 清理
            currentParameter = null;
            if (waveformChart) {
                waveformChart.destroy();
                waveformChart = null;
            }
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
            if (waveformChart) {
                waveformChart.destroy();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 系统初始化开始');

            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('❌ Chart.js未加载');
                document.getElementById('statusInfo').textContent = '系统加载失败 - Chart.js库未加载';
                document.getElementById('statusInfo').style.background = '#ffebee';
                document.getElementById('statusInfo').style.color = '#d32f2f';
                return;
            }

            console.log('✅ Chart.js已加载，版本:', Chart.version);

            initializeSystem();
            startDataUpdate();
            console.log('✅ 系统初始化完成');
        });
    </script>
</body>
</html>
