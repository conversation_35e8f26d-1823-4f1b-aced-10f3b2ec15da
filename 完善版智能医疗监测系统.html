<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能医疗监测系统 - 完善版</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
            color: #333;
        }

        .container {
            max-width: 1500px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            padding: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.08), 0 10px 25px rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.9);
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 12px;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.2);
            font-weight: 700;
            letter-spacing: -1px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.95;
            position: relative;
            z-index: 1;
        }

        .mode-selector {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .mode-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .mode-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .mode-btn.active {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            transform: scale(1.05);
        }

        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
            padding: 0 10px;
        }

        .control-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08), 0 4px 15px rgba(0,0,0,0.05);
            border: 1px solid rgba(255,255,255,0.8);
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            overflow: hidden;
        }

        .control-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .control-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.12), 0 8px 25px rgba(0,0,0,0.08);
        }

        .control-card:hover::before {
            opacity: 1;
        }

        .control-card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.4em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
            letter-spacing: -0.5px;
        }

        .status-indicator {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: #e74c3c;
            animation: pulse 2s infinite;
            box-shadow: 0 0 10px rgba(231, 76, 60, 0.3);
        }

        .status-indicator.connected {
            background: #27ae60;
            box-shadow: 0 0 10px rgba(39, 174, 96, 0.3);
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.1);
            }
        }

        .btn-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            overflow: hidden;
            text-transform: none;
            letter-spacing: 0.5px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(17, 153, 142, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(252, 70, 107, 0.3);
        }

        .btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn:active {
            transform: translateY(-1px) scale(0.98);
        }

        .info-text {
            font-size: 13px;
            color: #5a6c7d;
            margin-top: 15px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            border-left: 4px solid #667eea;
            line-height: 1.5;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .data-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .data-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
        }

        .data-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .data-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
            display: block;
        }

        .data-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .data-label {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 10px;
        }

        .data-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-normal { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-danger { background: #f8d7da; color: #721c24; }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .chart-container h3 {
            margin-bottom: 20px;
            color: #333;
            text-align: center;
            font-size: 1.5em;
        }

        .chart-wrapper {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        .alert-panel {
            background: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
            animation: slideIn 0.5s ease;
        }

        .alert-panel.show {
            display: block;
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .log-panel {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: #ecf0f1;
            padding: 20px;
            border-radius: 15px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 13px;
            max-height: 220px;
            overflow-y: auto;
            margin-top: 25px;
            box-shadow: 0 8px 25px rgba(44, 62, 80, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 6px 0;
            border-bottom: 1px solid rgba(255,255,255,0.08);
            transition: all 0.2s ease;
            border-radius: 4px;
        }

        .log-entry:hover {
            background: rgba(255, 255, 255, 0.05);
            padding-left: 8px;
        }

        .timestamp {
            color: #74b9ff;
            margin-right: 12px;
            font-weight: 500;
        }

        /* 校准动画样式 */
        .calibration-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .calibration-content {
            text-align: center;
            padding: 30px;
        }

        .calibration-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #e3e3e3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: calibrationSpin 1s linear infinite;
            margin: 0 auto 20px;
        }

        .calibration-text {
            font-size: 16px;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .calibration-progress {
            width: 200px;
            height: 6px;
            background: #e3e3e3;
            border-radius: 3px;
            overflow: hidden;
            margin: 0 auto;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 3px;
        }

        @keyframes calibrationSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 短期检测等待动画样式 */
        .detection-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            margin: 20px 0;
        }

        .loading-container {
            text-align: center;
            padding: 40px;
        }

        .detection-spinner {
            width: 80px;
            height: 80px;
            border: 6px solid #e3e3e3;
            border-top: 6px solid #667eea;
            border-radius: 50%;
            animation: detectionSpin 1.5s linear infinite;
            margin: 0 auto 30px;
        }

        .detection-text {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 30px;
        }

        .detection-steps {
            margin-bottom: 30px;
        }

        .step-item {
            font-size: 14px;
            color: #6c757d;
            margin: 8px 0;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .step-item.active {
            color: #667eea;
            opacity: 1;
            font-weight: 500;
        }

        .progress-container {
            width: 300px;
            height: 8px;
            background: #e3e3e3;
            border-radius: 4px;
            overflow: hidden;
            margin: 0 auto;
        }

        .progress-bar-detection {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        @keyframes detectionSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 25px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-radius: 15px;
            color: #5a6c7d;
            border: 1px solid rgba(102, 126, 234, 0.1);
            font-size: 14px;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 5px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .mode-selector {
                flex-direction: column;
                align-items: center;
            }
            
            .control-panel {
                grid-template-columns: 1fr;
            }
            
            .data-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        .fullscreen-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
            transition: all 0.3s ease;
        }

        .fullscreen-btn:hover {
            background: rgba(0,0,0,0.9);
            transform: scale(1.1);
        }

        /* 病人信息管理样式 */
        .patient-panel, .history-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .patient-info-display {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .patient-avatar {
            text-align: center;
            flex-shrink: 0;
        }

        .avatar-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .avatar-circle:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .patient-details {
            flex: 1;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }

        .info-label {
            font-weight: bold;
            color: #666;
            min-width: 80px;
        }

        .info-value {
            color: #333;
            font-weight: 500;
        }

        .patient-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .patient-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .patient-item:hover {
            background: #f0f8ff;
        }

        .patient-item.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .patient-item-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 1.2em;
        }

        .patient-item-info {
            flex: 1;
        }

        .patient-item-name {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .patient-item-details {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .patient-item-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-monitoring {
            background: #d4edda;
            color: #155724;
        }

        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }

        /* 表单样式 */
        .form-input, .form-select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .query-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }

        .modal-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            color: #999;
            transition: color 0.3s ease;
        }

        .close-btn:hover {
            color: #333;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        /* 导航面包屑 */
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding: 10px 15px;
            background: rgba(255,255,255,0.8);
            border-radius: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: #666;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb-item:hover {
            color: #4facfe;
        }

        .breadcrumb-item.active {
            color: #333;
            font-weight: bold;
        }

        .breadcrumb-separator {
            color: #999;
        }
    </style>
</head>
<body>
    <button class="fullscreen-btn" onclick="toggleFullscreen()" title="全屏切换">⛶</button>
    
    <div class="container">
        <div class="header">
            <h1>🏥 智能医疗监测系统</h1>
            <p>基于物联网的多参数生理监测平台 | 实时监护 · 智能预警 · 数据分析</p>
        </div>

        <!-- 导航面包屑 -->
        <div class="breadcrumb" id="breadcrumb">
            <a href="#" class="breadcrumb-item" onclick="navigateToHome()">🏠 首页</a>
            <span class="breadcrumb-separator">></span>
            <span class="breadcrumb-item active" id="currentPage">短期检测</span>
        </div>

        <div class="mode-selector">
            <button class="mode-btn active" onclick="switchMode('short')">⚡ 短期检测</button>
            <button class="mode-btn" onclick="switchMode('long')">📊 长期监测</button>
            <button class="mode-btn" onclick="switchMode('device')">🔗 设备连接</button>
            <button class="mode-btn" onclick="switchMode('analysis')">📈 数据分析</button>
            <button class="mode-btn" onclick="switchMode('patient')">👤 病人管理</button>
            <button class="mode-btn" onclick="switchMode('history')">📋 历史记录</button>
        </div>

        <!-- 短期检测数据显示面板 -->
        <div class="short-term-panel" id="shortTermPanel" style="display: none;">
            <!-- 检测等待动画 -->
            <div class="detection-loading" id="detectionLoading">
                <div class="loading-container">
                    <div class="detection-spinner"></div>
                    <div class="detection-text">正在进行生理参数检测...</div>
                    <div class="detection-steps">
                        <div class="step-item" id="step1">🔍 扫描传感器</div>
                        <div class="step-item" id="step2">🔗 建立连接</div>
                        <div class="step-item" id="step3">⚙️ 校准设备</div>
                        <div class="step-item" id="step4">📊 采集数据</div>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar-detection" id="detectionProgress"></div>
                    </div>
                </div>
            </div>

            <!-- 检测结果显示 -->
            <div class="detection-results" id="detectionResults" style="display: none;">
                <div class="data-grid">
                    <div class="data-card">
                        <div class="data-icon">❤️</div>
                        <div class="data-value" id="heartRate">75</div>
                        <div class="data-label">心率 (bpm)</div>
                        <div class="data-status status-normal" id="heartRateStatus">正常</div>
                    </div>

                    <div class="data-card">
                        <div class="data-icon">🩸</div>
                        <div class="data-value" id="bloodPressure">120/80</div>
                        <div class="data-label">血压 (mmHg)</div>
                        <div class="data-status status-normal" id="bloodPressureStatus">正常</div>
                    </div>

                    <div class="data-card">
                        <div class="data-icon">🫁</div>
                        <div class="data-value" id="spo2">98</div>
                        <div class="data-label">血氧饱和度 (%)</div>
                        <div class="data-status status-normal" id="spo2Status">正常</div>
                    </div>

                    <div class="data-card">
                        <div class="data-icon">🌡️</div>
                        <div class="data-value" id="temperature">36.5</div>
                        <div class="data-label">体温 (°C)</div>
                        <div class="data-status status-normal" id="temperatureStatus">正常</div>
                    </div>

                    <div class="data-card">
                        <div class="data-icon">🩺</div>
                        <div class="data-value" id="bloodGlucose">5.2</div>
                        <div class="data-label">血糖 (mmol/L)</div>
                        <div class="data-status status-normal" id="bloodGlucoseStatus">正常</div>
                    </div>

                    <div class="data-card">
                        <div class="data-icon">💨</div>
                        <div class="data-value" id="respiratoryRate">16</div>
                        <div class="data-label">呼吸频率 (次/分)</div>
                        <div class="data-status status-normal" id="respiratoryRateStatus">正常</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 病人信息管理面板 -->
        <div class="patient-panel" id="patientPanel" style="display: none;">
            <div class="control-card">
                <h3>👤 当前病人信息</h3>
                <div class="patient-info-display" id="currentPatientInfo">
                    <div class="patient-avatar">
                        <div class="avatar-circle" id="patientAvatar">👤</div>
                        <button class="btn btn-primary" onclick="uploadAvatar()">上传头像</button>
                    </div>
                    <div class="patient-details">
                        <div class="info-row">
                            <span class="info-label">姓名:</span>
                            <span class="info-value" id="patientName">未设置</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">病人编号:</span>
                            <span class="info-value" id="patientId">P001</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">年龄:</span>
                            <span class="info-value" id="patientAge">--</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">性别:</span>
                            <span class="info-value" id="patientGender">--</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">病房:</span>
                            <span class="info-value" id="patientRoom">4F012</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">主治医生:</span>
                            <span class="info-value" id="attendingDoctor">--</span>
                        </div>
                    </div>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="editPatientInfo()">编辑信息</button>
                    <button class="btn btn-success" onclick="addNewPatient()">新增病人</button>
                    <button class="btn btn-warning" onclick="switchPatient()">切换病人</button>
                </div>
            </div>

            <div class="control-card">
                <h3>📋 病人列表</h3>
                <div class="patient-list" id="patientList">
                    <!-- 病人列表将在这里动态生成 -->
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="refreshPatientList()">刷新列表</button>
                    <button class="btn btn-success" onclick="exportPatientData()">导出病人数据</button>
                </div>
            </div>
        </div>





        <div class="control-panel">
            <div class="control-card">
                <h3>
                    <span class="status-indicator" id="connectionStatus"></span>
                    🌐 硬件连接控制
                </h3>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="connectWebSocket()">WebSocket连接</button>
                    <button class="btn btn-success" onclick="connectSerial()">串口连接</button>
                </div>
                <div class="btn-group">
                    <button class="btn btn-warning" onclick="connectBluetooth()">蓝牙连接</button>
                    <button class="btn btn-danger" onclick="disconnect()">断开连接</button>
                </div>
                <div class="info-text">
                    <strong>连接状态：</strong><span id="connectionInfo">未连接 - 模拟模式</span><br>
                    <strong>数据模式：</strong><span id="dataMode">模拟数据</span><br>
                    <strong>最后更新：</strong><span id="lastUpdate">--</span>
                </div>
            </div>

            <div class="control-card">
                <h3>⚙️ 系统控制</h3>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="startMonitoring()">开始监测</button>
                    <button class="btn btn-warning" onclick="pauseMonitoring()">暂停监测</button>
                </div>
                <div class="btn-group">
                    <button class="btn btn-success" onclick="exportData()">导出数据</button>
                    <button class="btn btn-danger" onclick="resetSystem()">重置系统</button>
                </div>
                <div class="info-text">
                    <strong>监测状态：</strong><span id="monitoringStatus">运行中</span><br>
                    <strong>数据点数：</strong><span id="dataPoints">0</span><br>
                    <strong>运行时间：</strong><span id="runTime">00:00:00</span>
                </div>
            </div>

            <div class="control-card">
                <h3>🔧 设备测试</h3>

                <!-- 校准动画容器 -->
                <div id="calibrationAnimation" class="calibration-overlay" style="display: none;">
                    <div class="calibration-content">
                        <div class="calibration-spinner"></div>
                        <div class="calibration-text">正在校准设备...</div>
                        <div class="calibration-progress">
                            <div class="progress-bar" id="calibrationProgress"></div>
                        </div>
                    </div>
                </div>

                <div class="btn-group">
                    <button class="btn btn-primary" onclick="sendTestCommand()">发送测试命令</button>
                    <button class="btn btn-success" onclick="requestDataUpdate()">请求数据更新</button>
                </div>
                <div class="btn-group">
                    <button class="btn btn-warning" onclick="calibrateDevice()">设备校准</button>
                    <button class="btn btn-danger" onclick="resetDevice()">设备重置</button>
                </div>
                <div class="info-text">
                    <strong>设备状态：</strong><span id="deviceStatus">在线</span><br>
                    <strong>信号质量：</strong><span id="signalQuality">良好</span><br>
                    <strong>电池电量：</strong><span id="batteryLevel">85%</span>
                </div>
            </div>
        </div>

        <!-- 历史记录面板 -->
        <div class="history-panel" id="historyPanel" style="display: none;">
            <div class="control-card">
                <h3>📊 数据查询</h3>
                <div class="query-controls">
                    <div class="input-group">
                        <label>开始日期:</label>
                        <input type="datetime-local" id="startDate" class="form-input">
                    </div>
                    <div class="input-group">
                        <label>结束日期:</label>
                        <input type="datetime-local" id="endDate" class="form-input">
                    </div>
                    <div class="input-group">
                        <label>数据类型:</label>
                        <select id="dataType" class="form-select">
                            <option value="all">全部数据</option>
                            <option value="heartRate">心率</option>
                            <option value="bloodPressure">血压</option>
                            <option value="spo2">血氧</option>
                            <option value="temperature">体温</option>
                        </select>
                    </div>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="queryHistoryData()">查询数据</button>
                    <button class="btn btn-success" onclick="generateReport()">生成报告</button>
                    <button class="btn btn-warning" onclick="clearHistory()">清除历史</button>
                </div>
            </div>

            <div class="control-card">
                <h3>📈 历史数据图表</h3>
                <div class="chart-wrapper">
                    <canvas id="historyChart"></canvas>
                </div>
            </div>
        </div>

        <div class="alert-panel" id="alertPanel">
            <strong>⚠️ 异常警报：</strong>
            <span id="alertMessage">检测到生理参数异常，请立即关注！</span>
        </div>







        <div class="log-panel" id="logPanel">
            <div class="log-entry">
                <span class="timestamp">[2024-12-19 10:30:15]</span>
                <span>系统启动完成，开始数据采集...</span>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 智能医疗监测系统 | 基于CH32V307VCT6 + Air780e 4G模块 | 版本 v2.0</p>
            <p>支持WebSocket、串口、蓝牙多种连接方式 | 实时数据传输与云端分析</p>
        </div>
    </div>

    <!-- 病人信息编辑模态框 -->
    <div class="modal" id="patientEditModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">编辑病人信息</h3>
                <button class="close-btn" onclick="closeModal('patientEditModal')">&times;</button>
            </div>
            <form id="patientEditForm">
                <div class="form-grid">
                    <div class="input-group">
                        <label for="editPatientName">姓名 *</label>
                        <input type="text" id="editPatientName" class="form-input" required>
                    </div>
                    <div class="input-group">
                        <label for="editPatientId">病人编号 *</label>
                        <input type="text" id="editPatientId" class="form-input" required>
                    </div>
                    <div class="input-group">
                        <label for="editPatientAge">年龄</label>
                        <input type="number" id="editPatientAge" class="form-input" min="0" max="150">
                    </div>
                    <div class="input-group">
                        <label for="editPatientGender">性别</label>
                        <select id="editPatientGender" class="form-select">
                            <option value="">请选择</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="editPatientRoom">病房</label>
                        <input type="text" id="editPatientRoom" class="form-input">
                    </div>
                    <div class="input-group">
                        <label for="editAttendingDoctor">主治医生</label>
                        <input type="text" id="editAttendingDoctor" class="form-input">
                    </div>
                    <div class="input-group">
                        <label for="editPatientPhone">联系电话</label>
                        <input type="tel" id="editPatientPhone" class="form-input">
                    </div>
                    <div class="input-group">
                        <label for="editEmergencyContact">紧急联系人</label>
                        <input type="text" id="editEmergencyContact" class="form-input">
                    </div>
                    <div class="input-group">
                        <label for="editMedicalHistory">病史</label>
                        <textarea id="editMedicalHistory" class="form-input" rows="3"></textarea>
                    </div>
                    <div class="input-group">
                        <label for="editAllergies">过敏史</label>
                        <textarea id="editAllergies" class="form-input" rows="2"></textarea>
                    </div>
                </div>
                <div class="btn-group" style="margin-top: 20px;">
                    <button type="submit" class="btn btn-success">保存信息</button>
                    <button type="button" class="btn btn-danger" onclick="closeModal('patientEditModal')">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 病人切换模态框 -->
    <div class="modal" id="patientSwitchModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">选择病人</h3>
                <button class="close-btn" onclick="closeModal('patientSwitchModal')">&times;</button>
            </div>
            <div class="patient-search">
                <input type="text" id="patientSearchInput" class="form-input" placeholder="搜索病人姓名或编号...">
            </div>
            <div class="patient-list" id="modalPatientList" style="max-height: 400px;">
                <!-- 病人列表将在这里动态生成 -->
            </div>
            <div class="btn-group" style="margin-top: 20px;">
                <button class="btn btn-primary" onclick="addNewPatient()">新增病人</button>
                <button class="btn btn-danger" onclick="closeModal('patientSwitchModal')">取消</button>
            </div>
        </div>
    </div>

    <!-- 数据报告模态框 -->
    <div class="modal" id="reportModal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title">生理监测报告</h3>
                <button class="close-btn" onclick="closeModal('reportModal')">&times;</button>
            </div>
            <div id="reportContent">
                <!-- 报告内容将在这里动态生成 -->
            </div>
            <div class="btn-group" style="margin-top: 20px;">
                <button class="btn btn-success" onclick="printReport()">打印报告</button>
                <button class="btn btn-primary" onclick="exportReport()">导出PDF</button>
                <button class="btn btn-danger" onclick="closeModal('reportModal')">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentMode = 'short';
        let isMonitoring = true;
        let dataPoints = 0;
        let startTime = Date.now();
        let websocket = null;
        let isConnected = false;
        let vitalSignsChart = null;
        let ecgChart = null;
        let historyChart = null;
        let dataSimulationInterval = null;

        // 病人信息管理
        let currentPatient = {
            id: 'P001',
            name: '张三',
            age: 45,
            gender: '男',
            room: '4F012',
            doctor: '李医生',
            phone: '138****8888',
            emergencyContact: '李女士',
            medicalHistory: '高血压病史3年',
            allergies: '青霉素过敏'
        };

        let patientList = [
            {
                id: 'P001',
                name: '张三',
                age: 45,
                gender: '男',
                room: '4F012',
                doctor: '李医生',
                status: 'offline',
                avatar: '👨'
            },
            {
                id: 'P002',
                name: '王女士',
                age: 38,
                gender: '女',
                room: '4F013',
                doctor: '陈医生',
                status: 'offline',
                avatar: '👩'
            },
            {
                id: 'P003',
                name: '李老先生',
                age: 72,
                gender: '男',
                room: '4F014',
                doctor: '张医生',
                status: 'offline',
                avatar: '👴'
            },
            {
                id: 'P004',
                name: '赵女士',
                age: 55,
                gender: '女',
                room: '4F015',
                doctor: '王医生',
                status: 'offline',
                avatar: '👩'
            },
            {
                id: 'P005',
                name: '刘先生',
                age: 62,
                gender: '男',
                room: '4F016',
                doctor: '李医生',
                status: 'offline',
                avatar: '👨'
            }
        ];

        // 长期监测状态标志
        let isLongTermMonitoring = false;

        // 历史数据存储
        let historyData = {
            patients: {},
            records: []
        };

        // 生理参数数据存储
        let vitalData = {
            heartRate: [],
            bloodPressure: [],
            spo2: [],
            temperature: [],
            timestamps: []
        };

        let ecgData = {
            values: [],
            timestamps: []
        };

        // 初始化系统
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            startDataSimulation();
            updateRunTime();
            updateCurrentPatientInfo();
            updatePatientList();
            addLogEntry('系统初始化完成');
        });

        // 页面导航功能
        function navigateToHome() {
            switchMode('short');
            updateBreadcrumb('短期检测');
        }

        function updateBreadcrumb(pageName) {
            document.getElementById('currentPage').textContent = pageName;
        }

        // 模式切换增强 - 支持页面跳转
        function switchMode(mode) {
            currentMode = mode;
            document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));

            // 找到对应的按钮并激活
            const targetBtn = document.querySelector(`[onclick="switchMode('${mode}')"]`);
            if (targetBtn) {
                targetBtn.classList.add('active');
            }

            // 隐藏所有面板
            document.getElementById('patientPanel').style.display = 'none';
            document.getElementById('historyPanel').style.display = 'none';
            document.getElementById('shortTermPanel').style.display = 'none';

            // 显示对应面板和更新面包屑，或跳转到专门页面
            switch(mode) {
                case 'short':
                    // 激活短期检测模式
                    updateBreadcrumb('短期检测');
                    startShortTermDetection();
                    addLogEntry('切换到短期检测模式 - 血糖偏高，其余正常');
                    break;
                case 'long':
                    // 先更新病人状态，然后跳转到长期监测页面
                    startLongTermMonitoring();
                    addLogEntry('切换到长期监测模式 - 开始监测第一位病人');
                    // 跳转到长期监测专门页面
                    window.location.href = '长期监测页面.html';
                    break;
                case 'device':
                    updateBreadcrumb('设备连接');
                    addLogEntry('切换到设备连接模式 - 多设备管理');
                    break;
                case 'analysis':
                    updateBreadcrumb('数据分析');
                    addLogEntry('切换到数据分析模式 - 历史数据分析');
                    break;
                case 'patient':
                    document.getElementById('patientPanel').style.display = 'grid';
                    updateBreadcrumb('病人管理');
                    updatePatientList();
                    addLogEntry('切换到病人管理模式');
                    break;
                case 'history':
                    document.getElementById('historyPanel').style.display = 'grid';
                    updateBreadcrumb('历史记录');
                    initializeHistoryChart();
                    addLogEntry('切换到历史记录模式');
                    break;
            }
        }

        // 病人信息管理功能
        function updateCurrentPatientInfo() {
            document.getElementById('patientName').textContent = currentPatient.name;
            document.getElementById('patientId').textContent = currentPatient.id;
            document.getElementById('patientAge').textContent = currentPatient.age + '岁';
            document.getElementById('patientGender').textContent = currentPatient.gender;
            document.getElementById('patientRoom').textContent = currentPatient.room;
            document.getElementById('attendingDoctor').textContent = currentPatient.doctor;

            // 更新头像
            const avatar = getPatientAvatar(currentPatient);
            document.getElementById('patientAvatar').textContent = avatar;
        }

        function getPatientAvatar(patient) {
            if (patient.gender === '男') {
                return patient.age > 60 ? '👴' : '👨';
            } else {
                return patient.age > 60 ? '👵' : '👩';
            }
        }

        function updatePatientList() {
            const patientListElement = document.getElementById('patientList');
            const modalPatientListElement = document.getElementById('modalPatientList');

            let listHTML = '';
            patientList.forEach(patient => {
                const isActive = patient.id === currentPatient.id;
                const statusClass = patient.status === 'monitoring' ? 'status-monitoring' : 'status-offline';
                const statusText = patient.status === 'monitoring' ? '监测中' : '离线';

                listHTML += `
                    <div class="patient-item ${isActive ? 'active' : ''}" onclick="selectPatient('${patient.id}')">
                        <div class="patient-item-avatar">${patient.avatar}</div>
                        <div class="patient-item-info">
                            <div class="patient-item-name">${patient.name}</div>
                            <div class="patient-item-details">${patient.id} | ${patient.room} | ${patient.doctor}</div>
                        </div>
                        <div class="patient-item-status ${statusClass}">${statusText}</div>
                    </div>
                `;
            });

            if (patientListElement) patientListElement.innerHTML = listHTML;
            if (modalPatientListElement) modalPatientListElement.innerHTML = listHTML;
        }

        function selectPatient(patientId) {
            const patient = patientList.find(p => p.id === patientId);
            if (patient) {
                currentPatient = {
                    id: patient.id,
                    name: patient.name,
                    age: patient.age,
                    gender: patient.gender,
                    room: patient.room,
                    doctor: patient.doctor,
                    phone: '138****8888',
                    emergencyContact: '家属',
                    medicalHistory: '无特殊病史',
                    allergies: '无已知过敏'
                };

                updateCurrentPatientInfo();
                updatePatientList();
                closeModal('patientSwitchModal');
                addLogEntry(`切换到病人: ${patient.name} (${patient.id})`);
            }
        }

        function editPatientInfo() {
            // 填充表单数据
            document.getElementById('editPatientName').value = currentPatient.name;
            document.getElementById('editPatientId').value = currentPatient.id;
            document.getElementById('editPatientAge').value = currentPatient.age;
            document.getElementById('editPatientGender').value = currentPatient.gender;
            document.getElementById('editPatientRoom').value = currentPatient.room;
            document.getElementById('editAttendingDoctor').value = currentPatient.doctor;
            document.getElementById('editPatientPhone').value = currentPatient.phone;
            document.getElementById('editEmergencyContact').value = currentPatient.emergencyContact;
            document.getElementById('editMedicalHistory').value = currentPatient.medicalHistory;
            document.getElementById('editAllergies').value = currentPatient.allergies;

            showModal('patientEditModal');
        }

        function addNewPatient() {
            // 清空表单
            document.getElementById('patientEditForm').reset();
            document.getElementById('editPatientId').value = 'P' + String(patientList.length + 1).padStart(3, '0');
            showModal('patientEditModal');
        }

        // 短期检测功能
        function startShortTermDetection() {
            // 显示短期检测面板和等待动画
            document.getElementById('shortTermPanel').style.display = 'block';
            document.getElementById('detectionLoading').style.display = 'flex';
            document.getElementById('detectionResults').style.display = 'none';

            addLogEntry('开始短期检测 - 正在扫描传感器...');

            // 模拟检测步骤
            const steps = ['step1', 'step2', 'step3', 'step4'];
            const stepTexts = ['扫描传感器', '建立连接', '校准设备', '采集数据'];
            let currentStep = 0;
            let progress = 0;

            const detectionInterval = setInterval(() => {
                // 更新进度条
                progress += 5;
                document.getElementById('detectionProgress').style.width = progress + '%';

                // 激活当前步骤
                if (progress >= (currentStep + 1) * 25 && currentStep < steps.length) {
                    document.getElementById(steps[currentStep]).classList.add('active');
                    addLogEntry(`检测步骤: ${stepTexts[currentStep]}`);
                    currentStep++;
                }

                // 检测完成
                if (progress >= 100) {
                    clearInterval(detectionInterval);

                    setTimeout(() => {
                        // 隐藏等待动画，显示结果
                        document.getElementById('detectionLoading').style.display = 'none';
                        document.getElementById('detectionResults').style.display = 'block';

                        // 设置检测结果数据
                        const shortTermData = {
                            heartRate: 72,        // 正常心率
                            systolicBP: 118,      // 正常收缩压
                            diastolicBP: 78,      // 正常舒张压
                            spo2: 98,             // 正常血氧
                            temperature: 36.6,    // 正常体温
                            bloodGlucose: 8.5,    // 偏高血糖 (正常范围3.9-6.1)
                            respiratoryRate: 16   // 正常呼吸频率
                        };

                        // 更新显示数据
                        updateVitalSigns(shortTermData);

                        // 停止数据模拟，使用固定数据
                        if (dataSimulationInterval) {
                            clearInterval(dataSimulationInterval);
                            dataSimulationInterval = null;
                        }

                        addLogEntry('短期检测完成 - 血糖偏高(8.5 mmol/L)，其余指标正常');

                        // 重置步骤状态
                        steps.forEach(step => {
                            document.getElementById(step).classList.remove('active');
                        });
                        document.getElementById('detectionProgress').style.width = '0%';

                    }, 500);
                }
            }, 100); // 每100ms更新一次，总共5秒完成
        }

        // 长期监测功能
        function startLongTermMonitoring() {
            // 重置所有病人状态为离线
            patientList.forEach(patient => {
                patient.status = 'offline';
            });

            // 随机选择一位病人开始监测
            const randomIndex = Math.floor(Math.random() * patientList.length);
            patientList[randomIndex].status = 'monitoring';

            // 更新病人列表显示
            updatePatientList();

            addLogEntry(`开始监测病人: ${patientList[randomIndex].name} (${patientList[randomIndex].id})`);
        }

        function switchPatient() {
            showModal('patientSwitchModal');
        }

        function refreshPatientList() {
            updatePatientList();
            addLogEntry('病人列表已刷新');
        }

        function exportPatientData() {
            const data = {
                currentPatient: currentPatient,
                patientList: patientList,
                exportTime: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `patient_data_${new Date().toISOString().slice(0, 10)}.json`;
            a.click();

            addLogEntry('病人数据导出完成');
        }

        function uploadAvatar() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        // 这里可以处理头像上传
                        addLogEntry('头像上传功能待实现');
                    };
                    reader.readAsDataURL(file);
                }
            };
            input.click();
        }

        // 历史记录管理功能
        function queryHistoryData() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const dataType = document.getElementById('dataType').value;

            if (!startDate || !endDate) {
                alert('请选择查询时间范围');
                return;
            }

            // 模拟查询历史数据
            const mockData = generateMockHistoryData(startDate, endDate, dataType);
            updateHistoryChart(mockData);

            addLogEntry(`查询历史数据: ${startDate} 到 ${endDate}, 类型: ${dataType}`);
        }

        function generateMockHistoryData(startDate, endDate, dataType) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const data = {
                labels: [],
                datasets: []
            };

            // 生成时间标签
            const timeInterval = (end - start) / 50; // 50个数据点
            for (let i = 0; i < 50; i++) {
                const time = new Date(start.getTime() + i * timeInterval);
                data.labels.push(time.toLocaleString());
            }

            // 根据数据类型生成数据
            if (dataType === 'all' || dataType === 'heartRate') {
                data.datasets.push({
                    label: '心率 (bpm)',
                    data: Array.from({length: 50}, () => 70 + Math.random() * 30),
                    borderColor: '#ff6b6b',
                    backgroundColor: 'rgba(255, 107, 107, 0.1)'
                });
            }

            if (dataType === 'all' || dataType === 'bloodPressure') {
                data.datasets.push({
                    label: '收缩压 (mmHg)',
                    data: Array.from({length: 50}, () => 110 + Math.random() * 30),
                    borderColor: '#4ecdc4',
                    backgroundColor: 'rgba(78, 205, 196, 0.1)'
                });
            }

            if (dataType === 'all' || dataType === 'spo2') {
                data.datasets.push({
                    label: '血氧 (%)',
                    data: Array.from({length: 50}, () => 95 + Math.random() * 5),
                    borderColor: '#45b7d1',
                    backgroundColor: 'rgba(69, 183, 209, 0.1)'
                });
            }

            if (dataType === 'all' || dataType === 'temperature') {
                data.datasets.push({
                    label: '体温 (°C)',
                    data: Array.from({length: 50}, () => 36 + Math.random() * 2),
                    borderColor: '#f39c12',
                    backgroundColor: 'rgba(243, 156, 18, 0.1)'
                });
            }

            return data;
        }

        function initializeHistoryChart() {
            const ctx = document.getElementById('historyChart');
            if (!ctx) return;

            if (historyChart) {
                historyChart.destroy();
            }

            historyChart = new Chart(ctx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: [],
                    datasets: []
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '数值'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: '历史数据趋势'
                        }
                    }
                }
            });
        }

        function updateHistoryChart(data) {
            if (historyChart) {
                historyChart.data = data;
                historyChart.update();
            }
        }

        function generateReport() {
            const reportContent = `
                <div style="padding: 20px; font-family: Arial, sans-serif;">
                    <h2 style="text-align: center; color: #333; margin-bottom: 30px;">
                        🏥 生理监测报告
                    </h2>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                        <div>
                            <h3 style="color: #4facfe; border-bottom: 2px solid #4facfe; padding-bottom: 5px;">病人信息</h3>
                            <p><strong>姓名:</strong> ${currentPatient.name}</p>
                            <p><strong>编号:</strong> ${currentPatient.id}</p>
                            <p><strong>年龄:</strong> ${currentPatient.age}岁</p>
                            <p><strong>性别:</strong> ${currentPatient.gender}</p>
                            <p><strong>病房:</strong> ${currentPatient.room}</p>
                            <p><strong>主治医生:</strong> ${currentPatient.doctor}</p>
                        </div>
                        <div>
                            <h3 style="color: #4facfe; border-bottom: 2px solid #4facfe; padding-bottom: 5px;">报告信息</h3>
                            <p><strong>生成时间:</strong> ${new Date().toLocaleString()}</p>
                            <p><strong>监测时长:</strong> ${Math.floor((Date.now() - startTime) / 1000 / 60)}分钟</p>
                            <p><strong>数据点数:</strong> ${dataPoints}</p>
                            <p><strong>监测模式:</strong> ${getModeText(currentMode)}</p>
                        </div>
                    </div>

                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #4facfe; border-bottom: 2px solid #4facfe; padding-bottom: 5px;">当前生理参数</h3>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 2em; color: #ff6b6b;">❤️</div>
                                <div style="font-size: 1.5em; font-weight: bold;">${document.getElementById('heartRate').textContent}</div>
                                <div>心率 (bpm)</div>
                            </div>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 2em; color: #4ecdc4;">🩸</div>
                                <div style="font-size: 1.5em; font-weight: bold;">${document.getElementById('bloodPressure').textContent}</div>
                                <div>血压 (mmHg)</div>
                            </div>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                                <div style="font-size: 2em; color: #45b7d1;">🫁</div>
                                <div style="font-size: 1.5em; font-weight: bold;">${document.getElementById('spo2').textContent}%</div>
                                <div>血氧饱和度</div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 30px;">
                        <h3 style="color: #4facfe; border-bottom: 2px solid #4facfe; padding-bottom: 5px;">医学评估</h3>
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                            <p><strong>总体评估:</strong> 各项生理指标在正常范围内</p>
                            <p><strong>建议:</strong> 继续保持当前监测频率，注意观察血压变化趋势</p>
                            <p><strong>下次检查:</strong> 建议24小时后复查</p>
                        </div>
                    </div>

                    <div style="text-align: center; color: #666; font-size: 0.9em; margin-top: 40px;">
                        <p>本报告由智能医疗监测系统自动生成</p>
                        <p>© 2024 智能医疗监测系统 | 版本 v2.0</p>
                    </div>
                </div>
            `;

            document.getElementById('reportContent').innerHTML = reportContent;
            showModal('reportModal');
            addLogEntry('生成医疗报告完成');
        }

        function clearHistory() {
            if (confirm('确定要清除所有历史数据吗？此操作不可恢复。')) {
                historyData = { patients: {}, records: [] };
                if (historyChart) {
                    historyChart.data = { labels: [], datasets: [] };
                    historyChart.update();
                }
                addLogEntry('历史数据已清除');
            }
        }

        // 模态框管理
        function showModal(modalId) {
            document.getElementById(modalId).classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
            document.body.style.overflow = 'auto';
        }

        // 报告功能
        function printReport() {
            const reportContent = document.getElementById('reportContent').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>生理监测报告</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; }
                            @media print { body { margin: 0; } }
                        </style>
                    </head>
                    <body>${reportContent}</body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        function exportReport() {
            const reportContent = document.getElementById('reportContent').innerHTML;
            const blob = new Blob([`
                <html>
                    <head>
                        <meta charset="UTF-8">
                        <title>生理监测报告</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; }
                        </style>
                    </head>
                    <body>${reportContent}</body>
                </html>
            `], { type: 'text/html' });

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `medical_report_${currentPatient.id}_${new Date().toISOString().slice(0, 10)}.html`;
            a.click();

            addLogEntry('医疗报告导出完成');
        }

        // 表单提交处理
        document.getElementById('patientEditForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // 获取表单数据
            const formData = {
                name: document.getElementById('editPatientName').value,
                id: document.getElementById('editPatientId').value,
                age: parseInt(document.getElementById('editPatientAge').value),
                gender: document.getElementById('editPatientGender').value,
                room: document.getElementById('editPatientRoom').value,
                doctor: document.getElementById('editAttendingDoctor').value,
                phone: document.getElementById('editPatientPhone').value,
                emergencyContact: document.getElementById('editEmergencyContact').value,
                medicalHistory: document.getElementById('editMedicalHistory').value,
                allergies: document.getElementById('editAllergies').value
            };

            // 验证必填字段
            if (!formData.name || !formData.id) {
                alert('请填写必填字段：姓名和病人编号');
                return;
            }

            // 更新当前病人信息
            currentPatient = formData;

            // 更新病人列表
            const existingPatientIndex = patientList.findIndex(p => p.id === formData.id);
            if (existingPatientIndex >= 0) {
                patientList[existingPatientIndex] = {
                    ...patientList[existingPatientIndex],
                    ...formData,
                    avatar: getPatientAvatar(formData)
                };
            } else {
                patientList.push({
                    ...formData,
                    status: 'monitoring',
                    avatar: getPatientAvatar(formData)
                });
            }

            updateCurrentPatientInfo();
            updatePatientList();
            closeModal('patientEditModal');

            addLogEntry(`病人信息已保存: ${formData.name} (${formData.id})`);
        });

        // 病人搜索功能
        document.getElementById('patientSearchInput').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const patientItems = document.querySelectorAll('#modalPatientList .patient-item');

            patientItems.forEach(item => {
                const name = item.querySelector('.patient-item-name').textContent.toLowerCase();
                const details = item.querySelector('.patient-item-details').textContent.toLowerCase();

                if (name.includes(searchTerm) || details.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.classList.remove('show');
                document.body.style.overflow = 'auto';
            }
        });

        function getModeText(mode) {
            const modes = {
                'short': '短期检测',
                'long': '长期监测',
                'device': '设备连接',
                'analysis': '数据分析',
                'patient': '病人管理',
                'history': '历史记录'
            };
            return modes[mode] || '未知';
        }

        // WebSocket连接
        function connectWebSocket() {
            // 显示设备选择对话框
            const devices = [
                'Device-CH32V307-001',
                'Device-ESP32-A4B2',
                'Device-STM32-F103',
                'Device-Arduino-UNO3'
            ];

            let deviceList = 'Available WebSocket Devices:\n\n';
            devices.forEach((device, index) => {
                deviceList += `${index + 1}. ${device}\n`;
            });

            const choice = prompt(deviceList + '\nSelect device number (1-4):');

            if (choice && choice >= 1 && choice <= 4) {
                const selectedDevice = devices[choice - 1];
                addLogEntry(`正在连接到 ${selectedDevice}...`);

                // 模拟连接过程
                setTimeout(() => {
                    isConnected = true;
                    updateConnectionStatus(`WebSocket已连接 - ${selectedDevice}`, 'connected');
                    addLogEntry(`WebSocket连接成功: ${selectedDevice}`);
                }, 1500);
            } else {
                addLogEntry('WebSocket连接已取消');
            }
        }

        // 串口连接
        function connectSerial() {
            // 显示串口设备选择对话框
            const serialPorts = [
                'COM3 - USB Serial Device (VID_1A86&PID_7523)',
                'COM5 - Silicon Labs CP210x (VID_10C4&PID_EA60)',
                'COM7 - FTDI USB Serial Port (VID_0403&PID_6001)',
                'COM9 - Prolific USB-to-Serial (VID_067B&PID_2303)',
                'COM11 - CH340 USB Bridge (VID_1A86&PID_7523)'
            ];

            let portList = 'Detected Serial Ports:\n\n';
            serialPorts.forEach((port, index) => {
                portList += `${index + 1}. ${port}\n`;
            });

            const choice = prompt(portList + '\nSelect serial port (1-5):');

            if (choice && choice >= 1 && choice <= 5) {
                const selectedPort = serialPorts[choice - 1];
                addLogEntry(`正在连接到 ${selectedPort}...`);

                // 模拟串口连接过程
                setTimeout(() => {
                    isConnected = true;
                    updateConnectionStatus(`串口已连接 - ${selectedPort.split(' - ')[0]}`, 'connected');
                    addLogEntry(`串口连接成功: ${selectedPort.split(' - ')[0]}`);
                }, 2000);
            } else {
                addLogEntry('串口连接已取消');
            }
        }

        // 蓝牙连接
        function connectBluetooth() {
            // 模拟连接成功
            setTimeout(() => {
                isConnected = true;
                updateConnectionStatus('蓝牙已连接', 'connected');
                addLogEntry('蓝牙连接成功: MedicalDevice-001');
            }, 2000); // 2秒后连接成功
        }

        // 断开连接
        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
            
            isConnected = false;
            updateConnectionStatus('未连接 - 模拟模式', 'disconnected');
            addLogEntry('所有连接已断开，切换到模拟模式');
        }

        // 更新连接状态
        function updateConnectionStatus(message, status) {
            document.getElementById('connectionInfo').textContent = message;
            document.getElementById('dataMode').textContent = isConnected ? '硬件数据' : '模拟数据';
            
            const indicator = document.getElementById('connectionStatus');
            if (status === 'connected') {
                indicator.classList.add('connected');
            } else {
                indicator.classList.remove('connected');
            }
        }

        // 系统控制函数
        function startMonitoring() {
            isMonitoring = true;
            document.getElementById('monitoringStatus').textContent = '运行中';
            addLogEntry('开始监测');
        }

        function pauseMonitoring() {
            isMonitoring = false;
            document.getElementById('monitoringStatus').textContent = '已暂停';
            addLogEntry('监测已暂停');
        }



        function exportData() {
            const data = {
                vitalData: vitalData,
                ecgData: ecgData,
                exportTime: new Date().toISOString(),
                mode: currentMode
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `medical_data_${new Date().toISOString().slice(0, 19)}.json`;
            a.click();
            
            addLogEntry('数据导出完成');
        }

        function resetSystem() {
            if (confirm('确定要重置系统吗？这将清除所有数据。')) {
                vitalData = {
                    heartRate: [],
                    bloodPressure: [],
                    spo2: [],
                    temperature: [],
                    timestamps: []
                };
                ecgData = { values: [], timestamps: [] };
                dataPoints = 0;
                startTime = Date.now();
                
                // 重新初始化图表
                if (vitalSignsChart) vitalSignsChart.destroy();
                if (ecgChart) ecgChart.destroy();
                initializeCharts();
                
                addLogEntry('系统已重置');
            }
        }

        // 设备测试函数
        function sendTestCommand() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const command = {
                    type: 'command',
                    command: 'GET_STATUS',
                    timestamp: Date.now()
                };
                websocket.send(JSON.stringify(command));
                addLogEntry('发送测试命令: GET_STATUS');
            } else {
                addLogEntry('无可用连接，无法发送命令');
            }
        }

        function requestDataUpdate() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const command = {
                    type: 'command',
                    command: 'GET_DATA',
                    timestamp: Date.now()
                };
                websocket.send(JSON.stringify(command));
                addLogEntry('请求数据更新');
            } else {
                // 模拟模式下生成新数据
                generateSimulatedData();
                addLogEntry('模拟模式：生成新数据');
            }
        }

        function calibrateDevice() {
            // 显示校准动画
            const calibrationOverlay = document.getElementById('calibrationAnimation');
            const progressBar = document.getElementById('calibrationProgress');

            calibrationOverlay.style.display = 'flex';

            // 开始校准，更新界面状态
            document.getElementById('deviceStatus').textContent = '正在校准设备';
            document.getElementById('signalQuality').textContent = '校准中';
            addLogEntry('开始设备校准...');

            // 模拟进度条动画
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += 2;
                progressBar.style.width = progress + '%';

                if (progress >= 100) {
                    clearInterval(progressInterval);
                }
            }, 60); // 每60ms增加2%，总共3秒完成

            // 模拟校准过程
            setTimeout(() => {
                // 隐藏动画
                calibrationOverlay.style.display = 'none';

                // 重置进度条
                progressBar.style.width = '0%';

                // 更新状态
                document.getElementById('deviceStatus').textContent = '设备正常';
                document.getElementById('signalQuality').textContent = '优秀';
                addLogEntry('设备校准完成 - 设备状态正常');
            }, 3000); // 3秒校准过程
        }

        function resetDevice() {
            addLogEntry('正在重置设备...');
            document.getElementById('deviceStatus').textContent = '重置中';

            setTimeout(() => {
                addLogEntry('设备重置完成');
                document.getElementById('deviceStatus').textContent = '在线';
                document.getElementById('signalQuality').textContent = '良好';
                document.getElementById('batteryLevel').textContent = '100%';
            }, 3000);
        }

        // 数据更新函数
        function updateVitalSigns(data) {
            if (!isMonitoring) return;
            
            // 更新显示值
            document.getElementById('heartRate').textContent = Math.round(data.heartRate || 75);
            document.getElementById('bloodPressure').textContent = `${Math.round(data.systolicBP || 120)}/${Math.round(data.diastolicBP || 80)}`;
            document.getElementById('spo2').textContent = Math.round(data.spo2 || 98);
            document.getElementById('temperature').textContent = (data.temperature || 36.5).toFixed(1);
            document.getElementById('bloodGlucose').textContent = (data.bloodGlucose || 5.2).toFixed(1);
            document.getElementById('respiratoryRate').textContent = Math.round(data.respiratoryRate || 16);
            
            // 更新状态
            updateVitalStatus('heartRate', data.heartRate || 75, 60, 100);
            updateVitalStatus('bloodPressure', data.systolicBP || 120, 90, 140);
            updateVitalStatus('spo2', data.spo2 || 98, 95, 100);
            updateVitalStatus('temperature', data.temperature || 36.5, 36.0, 37.5);
            updateVitalStatus('bloodGlucose', data.bloodGlucose || 5.2, 3.9, 6.1);
            updateVitalStatus('respiratoryRate', data.respiratoryRate || 16, 12, 20);
            
            // 存储数据用于图表
            const now = new Date();
            vitalData.timestamps.push(now);
            vitalData.heartRate.push(data.heartRate || 75);
            vitalData.spo2.push(data.spo2 || 98);
            vitalData.temperature.push(data.temperature || 36.5);
            
            // 限制数据点数量
            if (vitalData.timestamps.length > 50) {
                vitalData.timestamps.shift();
                vitalData.heartRate.shift();
                vitalData.spo2.shift();
                vitalData.temperature.shift();
            }
            
            // 更新图表
            updateCharts();
            
            // 更新统计信息
            dataPoints++;
            document.getElementById('dataPoints').textContent = dataPoints;
            document.getElementById('lastUpdate').textContent = now.toLocaleTimeString();
            
            // 检查异常
            checkAbnormalValues(data);
        }

        function updateVitalStatus(param, value, min, max) {
            const statusElement = document.getElementById(param + 'Status');
            if (value < min || value > max) {
                statusElement.textContent = '异常';
                statusElement.className = 'data-status status-danger';
            } else if (value < min * 1.1 || value > max * 0.9) {
                statusElement.textContent = '注意';
                statusElement.className = 'data-status status-warning';
            } else {
                statusElement.textContent = '正常';
                statusElement.className = 'data-status status-normal';
            }
        }

        function checkAbnormalValues(data) {
            let alerts = [];

            if (data.heartRate && (data.heartRate < 60 || data.heartRate > 100)) {
                alerts.push(`心率异常: ${data.heartRate} bpm`);
            }
            if (data.systolicBP && (data.systolicBP < 90 || data.systolicBP > 140)) {
                alerts.push(`收缩压异常: ${data.systolicBP} mmHg`);
            }
            // 血氧异常检测：调整阈值，血氧稳定在正常范围
            if (data.spo2 && data.spo2 < 96) {
                alerts.push(`血氧饱和度偏低: ${data.spo2}%`);
            }
            // 体温异常检测：调整正常范围
            if (data.temperature && (data.temperature < 36.0 || data.temperature > 37.2)) {
                alerts.push(`体温异常: ${data.temperature}°C`);
            }
            // 血糖异常检测：允许在正常偏高范围内波动
            if (data.bloodGlucose && (data.bloodGlucose < 3.9 || data.bloodGlucose > 7.0)) {
                alerts.push(`血糖异常: ${data.bloodGlucose} mmol/L`);
            }

            if (alerts.length > 0) {
                showAlert(alerts.join(', '));
            }
        }

        function showAlert(message) {
            const alertPanel = document.getElementById('alertPanel');
            const alertMessage = document.getElementById('alertMessage');
            
            alertMessage.textContent = message;
            alertPanel.classList.add('show');
            
            setTimeout(() => {
                alertPanel.classList.remove('show');
            }, 5000);
            
            addLogEntry('⚠️ 异常警报: ' + message);
        }

        // 图表初始化
        function initializeCharts() {
            // 生理参数趋势图
            const ctx1 = document.getElementById('vitalSignsChart').getContext('2d');
            vitalSignsChart = new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '心率 (bpm)',
                        data: [],
                        borderColor: '#ff6b6b',
                        backgroundColor: 'rgba(255, 107, 107, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '血氧 (%)',
                        data: [],
                        borderColor: '#4ecdc4',
                        backgroundColor: 'rgba(78, 205, 196, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }, {
                        label: '体温 (°C)',
                        data: [],
                        borderColor: '#45b7d1',
                        backgroundColor: 'rgba(69, 183, 209, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y2'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '心率 (bpm)'
                            },
                            min: 50,
                            max: 120
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '血氧 (%)'
                            },
                            min: 90,
                            max: 100,
                            grid: {
                                drawOnChartArea: false,
                            },
                        },
                        y2: {
                            type: 'linear',
                            display: false,
                            min: 35,
                            max: 39
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: '实时生理参数监测'
                        }
                    }
                }
            });

            // ECG波形图
            const ctx2 = document.getElementById('ecgChart').getContext('2d');
            ecgChart = new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'ECG信号 (mV)',
                        data: [],
                        borderColor: '#2ecc71',
                        backgroundColor: 'rgba(46, 204, 113, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        tension: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: false,
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间 (ms)'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '电压 (mV)'
                            },
                            min: -2,
                            max: 2
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: '心电图实时波形'
                        }
                    }
                }
            });
        }

        function updateCharts() {
            if (vitalSignsChart && vitalData.timestamps.length > 0) {
                vitalSignsChart.data.labels = vitalData.timestamps.map(t => t.toLocaleTimeString());
                vitalSignsChart.data.datasets[0].data = vitalData.heartRate;
                vitalSignsChart.data.datasets[1].data = vitalData.spo2;
                vitalSignsChart.data.datasets[2].data = vitalData.temperature;
                vitalSignsChart.update('none');
            }

            if (ecgChart && ecgData.values.length > 0) {
                ecgChart.data.labels = ecgData.timestamps;
                ecgChart.data.datasets[0].data = ecgData.values;
                ecgChart.update('none');
            }
        }

        // 数据模拟
        function startDataSimulation() {
            if (dataSimulationInterval) {
                clearInterval(dataSimulationInterval);
            }

            dataSimulationInterval = setInterval(() => {
                if (!isConnected && isMonitoring) {
                    generateSimulatedData();
                }
                generateECGData();
            }, 2000);
        }

        function generateSimulatedData() {
            const time = Date.now();

            const data = {
                // 心率：正常范围波动 (65-85 bpm)
                heartRate: 75 + Math.sin(time / 12000) * 8 + (Math.random() - 0.5) * 4,

                // 血压：正常范围内小幅波动
                systolicBP: 120 + Math.sin(time / 18000) * 8 + (Math.random() - 0.5) * 6,
                diastolicBP: 80 + Math.sin(time / 15000) * 5 + (Math.random() - 0.5) * 4,

                // 血氧：基本保持不变，极小波动 (97-99%)
                spo2: 98 + Math.sin(time / 60000) * 0.5 + (Math.random() - 0.5) * 0.3,

                // 体温：变化频率低，幅度小 (36.4-36.8°C)
                temperature: 36.6 + Math.sin(time / 120000) * 0.15 + (Math.random() - 0.5) * 0.1,

                // 血糖：在正常与偏高之间波动 (5.5-7.8 mmol/L)
                bloodGlucose: 6.5 + Math.sin(time / 25000) * 1.0 + (Math.random() - 0.5) * 0.6,

                // 呼吸频率：变化缓慢 (14-18 次/分)
                respiratoryRate: 16 + Math.sin(time / 45000) * 1.5 + (Math.random() - 0.5) * 0.8,

                timestamp: time
            };

            updateVitalSigns(data);
        }

        function generateECGData() {
            const now = Date.now();
            const heartRate = parseInt(document.getElementById('heartRate').textContent) || 75;
            const rrInterval = 60000 / heartRate; // RR间期(ms)
            
            // 生成ECG波形数据点
            for (let i = 0; i < 10; i++) {
                const t = (now + i * 50) % rrInterval;
                let value = 0;
                
                // 简化的ECG波形生成
                if (t < 50) {
                    // P波
                    value = 0.2 * Math.sin(Math.PI * t / 50);
                } else if (t < 150) {
                    // PR段
                    value = 0;
                } else if (t < 200) {
                    // QRS波群
                    if (t < 170) {
                        value = -0.3; // Q波
                    } else if (t < 185) {
                        value = 1.5; // R波
                    } else {
                        value = -0.5; // S波
                    }
                } else if (t < 400) {
                    // ST段和T波
                    value = 0.3 * Math.sin(Math.PI * (t - 200) / 200);
                }
                
                // 添加噪声
                value += (Math.random() - 0.5) * 0.05;
                
                ecgData.values.push(value);
                ecgData.timestamps.push(t);
            }
            
            // 限制数据点数量
            if (ecgData.values.length > 200) {
                ecgData.values = ecgData.values.slice(-200);
                ecgData.timestamps = ecgData.timestamps.slice(-200);
            }
        }

        // 运行时间更新
        function updateRunTime() {
            setInterval(() => {
                const elapsed = Date.now() - startTime;
                const hours = Math.floor(elapsed / 3600000);
                const minutes = Math.floor((elapsed % 3600000) / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                
                document.getElementById('runTime').textContent = 
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        // 日志系统
        function addLogEntry(message) {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span><span>${message}</span>`;
            
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
            
            // 限制日志条目数量
            const entries = logPanel.querySelectorAll('.log-entry');
            if (entries.length > 100) {
                entries[0].remove();
            }
        }

        // 全屏功能
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
                document.body.classList.add('fullscreen');
            } else {
                document.exitFullscreen();
                document.body.classList.remove('fullscreen');
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        document.querySelector('[onclick="switchMode(\'short\')"]').click();
                        break;
                    case '2':
                        e.preventDefault();
                        document.querySelector('[onclick="switchMode(\'long\')"]').click();
                        break;
                    case '3':
                        e.preventDefault();
                        document.querySelector('[onclick="switchMode(\'device\')"]').click();
                        break;
                    case '4':
                        e.preventDefault();
                        document.querySelector('[onclick="switchMode(\'analysis\')"]').click();
                        break;
                    case '5':
                        e.preventDefault();
                        document.querySelector('[onclick="switchMode(\'patient\')"]').click();
                        break;
                    case '6':
                        e.preventDefault();
                        document.querySelector('[onclick="switchMode(\'history\')"]').click();
                        break;
                    case 's':
                        e.preventDefault();
                        exportData();
                        break;
                    case 'r':
                        e.preventDefault();
                        resetSystem();
                        break;
                    case 'e':
                        e.preventDefault();
                        editPatientInfo();
                        break;
                    case 'p':
                        e.preventDefault();
                        generateReport();
                        break;
                }
            }

            if (e.key === 'F11') {
                e.preventDefault();
                toggleFullscreen();
            }

            if (e.key === 'Escape') {
                // 关闭所有模态框
                document.querySelectorAll('.modal.show').forEach(modal => {
                    modal.classList.remove('show');
                });
                document.body.style.overflow = 'auto';
            }
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                addLogEntry('页面进入后台模式');
            } else {
                addLogEntry('页面恢复前台模式');
            }
        });

        // 窗口关闭前确认
        window.addEventListener('beforeunload', function(e) {
            if (isMonitoring && dataPoints > 0) {
                e.preventDefault();
                e.returnValue = '监测正在进行中，确定要离开吗？';
            }
        });
    </script>
</body>
</html>
