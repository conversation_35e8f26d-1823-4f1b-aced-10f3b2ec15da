<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医学生理参数测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            margin: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        .control-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn.active {
            background: #28a745;
        }

        .parameters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .parameter-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            position: relative;
        }

        .parameter-card .name {
            font-size: 1.1em;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .parameter-card .value {
            font-size: 2.2em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .parameter-card .range {
            font-size: 0.9em;
            opacity: 0.9;
            margin-bottom: 5px;
        }

        .parameter-card .status {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4caf50;
        }

        .parameter-card.abnormal {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .parameter-card.abnormal .status {
            background: #fff;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .output-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .output {
            background: #2d3436;
            color: #00b894;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }

        .stats-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
        }

        .time-info {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            background: #e3f2fd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 医学生理参数测试系统</h1>
            <p>测试8种医学生理参数的真实波动特征</p>
        </div>

        <div class="time-info">
            <strong>模拟时间:</strong> <span id="simulatedTime">12:00</span> | 
            <strong>生理状态:</strong> <span id="physiologicalState">正常</span> | 
            <strong>数据点:</strong> <span id="dataCount">0</span>
        </div>

        <div class="control-section">
            <button class="btn" id="startBtn" onclick="startTest()">开始测试</button>
            <button class="btn" id="stopBtn" onclick="stopTest()">停止测试</button>
            <button class="btn" onclick="resetTest()">重置数据</button>
            <button class="btn" onclick="exportData()">导出数据</button>
            <button class="btn" onclick="simulateStress()">模拟压力事件</button>
        </div>

        <div class="parameters-grid">
            <div class="parameter-card" id="heartRateCard">
                <div class="status"></div>
                <div class="name">💓 心率</div>
                <div class="value" id="heartRate">--</div>
                <div class="range">正常: 60-100 次/分</div>
            </div>
            <div class="parameter-card" id="bloodPressureCard">
                <div class="status"></div>
                <div class="name">🩸 血压</div>
                <div class="value" id="bloodPressure">--/--</div>
                <div class="range">正常: 90-140/60-90 mmHg</div>
            </div>
            <div class="parameter-card" id="spo2Card">
                <div class="status"></div>
                <div class="name">🫁 血氧</div>
                <div class="value" id="spo2">--%</div>
                <div class="range">正常: 95-100%</div>
            </div>
            <div class="parameter-card" id="temperatureCard">
                <div class="status"></div>
                <div class="name">🌡️ 体温</div>
                <div class="value" id="temperature">--°C</div>
                <div class="range">正常: 36.1-37.2°C</div>
            </div>
            <div class="parameter-card" id="respiratoryCard">
                <div class="status"></div>
                <div class="name">🌬️ 呼吸</div>
                <div class="value" id="respiratoryRate">--</div>
                <div class="range">正常: 12-20 次/分</div>
            </div>
            <div class="parameter-card" id="glucoseCard">
                <div class="status"></div>
                <div class="name">🍯 血糖</div>
                <div class="value" id="glucose">--</div>
                <div class="range">正常: 3.9-6.1 mmol/L</div>
            </div>
            <div class="parameter-card" id="qtCard">
                <div class="status"></div>
                <div class="name">📈 QT间期</div>
                <div class="value" id="qtInterval">--ms</div>
                <div class="range">正常: 350-450ms</div>
            </div>
            <div class="parameter-card" id="stressCard">
                <div class="status"></div>
                <div class="name">🧠 压力指数</div>
                <div class="value" id="stressIndex">--</div>
                <div class="range">正常: 0-30</div>
            </div>
        </div>

        <div class="stats-section">
            <h3>📊 统计信息</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="avgHeartRate">--</div>
                    <div class="stat-label">平均心率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avgBloodPressure">--</div>
                    <div class="stat-label">平均血压</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avgTemperature">--</div>
                    <div class="stat-label">平均体温</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="abnormalCount">0</div>
                    <div class="stat-label">异常参数数量</div>
                </div>
            </div>
        </div>

        <div class="output-section">
            <h3>📋 数据日志</h3>
            <div class="output" id="output"></div>
        </div>
    </div>

    <script>
        // 简化版医学参数模拟器
        class MedicalParameterSimulator {
            constructor() {
                this.isRunning = false;
                this.interval = null;
                this.dataCount = 0;
                
                // 基础生理参数
                this.heartRate = 72;
                this.systolicBP = 120;
                this.diastolicBP = 80;
                this.spo2 = 98;
                this.temperature = 36.5;
                this.respiratoryRate = 16;
                this.glucose = 5.0;
                this.qtInterval = 400;
                this.stressIndex = 15;
                
                // 生理状态
                this.timeOfDay = 12;
                this.stressLevel = 0;
                this.physiologicalState = 'normal';
                
                // 统计数据
                this.stats = this.initStats();
                
                // 随机数种子
                this.randomSeed = 12345;
            }
            
            initStats() {
                return {
                    heartRate: { sum: 0, count: 0 },
                    systolicBP: { sum: 0, count: 0 },
                    diastolicBP: { sum: 0, count: 0 },
                    temperature: { sum: 0, count: 0 }
                };
            }
            
            // 简单随机数生成器
            simpleRandom() {
                this.randomSeed = (this.randomSeed * 1103515245 + 12345) & 0x7FFFFFFF;
                return this.randomSeed;
            }
            
            // 正态分布随机数
            getNormalRandom(mean, stdDev) {
                const u = Math.random();
                const v = Math.random();
                const z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
                return z * stdDev + mean;
            }
            
            // 更新生理状态
            updatePhysiologicalState() {
                this.timeOfDay = (this.timeOfDay + 0.0167) % 24;
                
                if (this.timeOfDay >= 22 || this.timeOfDay <= 6) {
                    this.physiologicalState = 'sleep';
                } else if (this.timeOfDay >= 7 && this.timeOfDay <= 9) {
                    this.physiologicalState = 'active';
                } else {
                    this.physiologicalState = 'normal';
                }
                
                // 压力水平自然衰减
                this.stressLevel = Math.max(0, this.stressLevel - 0.1);
            }
            
            // 模拟所有生理参数
            simulateAllParameters() {
                this.updatePhysiologicalState();
                
                // 心率模拟
                let baseHR = 72;
                switch (this.physiologicalState) {
                    case 'sleep': baseHR = 55; break;
                    case 'active': baseHR = 85; break;
                    case 'normal': baseHR = 72; break;
                }
                baseHR += this.stressLevel * 2;
                this.heartRate = Math.round(Math.max(45, Math.min(150, baseHR + this.getNormalRandom(0, 2))));
                
                // 血压模拟
                let baseSystolic = 120, baseDiastolic = 80;
                const circadianFactor = Math.sin((this.timeOfDay - 6) * Math.PI / 12) * 5;
                baseSystolic += circadianFactor;
                baseDiastolic += circadianFactor * 0.6;
                
                if (this.physiologicalState === 'sleep') {
                    baseSystolic -= 10; baseDiastolic -= 5;
                } else if (this.stressLevel > 0) {
                    baseSystolic += 15; baseDiastolic += 10;
                }
                
                this.systolicBP = Math.round(Math.max(90, Math.min(180, baseSystolic + this.getNormalRandom(0, 3))));
                this.diastolicBP = Math.round(Math.max(50, Math.min(110, baseDiastolic + this.getNormalRandom(0, 2))));
                
                // 血氧模拟（高度稳定）
                let baseSpo2 = 98;
                if (this.physiologicalState === 'sleep') baseSpo2 = 97;
                this.spo2 = Math.round(Math.max(94, Math.min(100, baseSpo2 + this.getNormalRandom(0, 0.5))));
                
                // 体温模拟（极其稳定）
                let baseTemp = 36.5;
                const tempCircadian = Math.sin((this.timeOfDay - 6) * Math.PI / 12) * 0.4;
                baseTemp += tempCircadian;
                if (this.physiologicalState === 'sleep') baseTemp -= 0.2;
                
                const targetTemp = baseTemp;
                this.temperature += (targetTemp - this.temperature) * 0.1;
                this.temperature += this.getNormalRandom(0, 0.05);
                this.temperature = Math.max(35.5, Math.min(38.5, this.temperature));
                
                // 呼吸频率
                let baseRR = 16;
                if (this.physiologicalState === 'sleep') baseRR = 12;
                else if (this.stressLevel > 0) baseRR = 22;
                this.respiratoryRate = Math.round(Math.max(8, Math.min(30, baseRR + this.getNormalRandom(0, 1.5))));
                
                // 血糖（餐后波动）
                let baseGlucose = 5.0;
                const mealTimes = [7, 12, 18];
                for (const mealTime of mealTimes) {
                    const timeSinceMeal = Math.abs(this.timeOfDay - mealTime);
                    if (timeSinceMeal < 2) {
                        baseGlucose += (2 - timeSinceMeal) * 1.5;
                    }
                }
                const targetGlucose = baseGlucose;
                this.glucose += (targetGlucose - this.glucose) * 0.05;
                this.glucose += this.getNormalRandom(0, 0.1);
                this.glucose = Math.max(3.5, Math.min(8.0, this.glucose));
                
                // QT间期（与心率相关）
                this.qtInterval = Math.round(Math.max(320, Math.min(480, 400 - (this.heartRate - 72) * 1.5 + this.getNormalRandom(0, 10))));
                
                // 压力指数
                const hrStress = Math.max(0, (this.heartRate - 80) * 0.5);
                const bpStress = Math.max(0, (this.systolicBP - 130) * 0.3);
                const rrStress = Math.max(0, (this.respiratoryRate - 18) * 0.8);
                this.stressIndex = Math.round(Math.max(0, Math.min(100, hrStress + bpStress + rrStress + this.stressLevel * 2 + this.getNormalRandom(0, 2))));
                
                return this.getCurrentData();
            }
            
            getCurrentData() {
                return {
                    heartRate: this.heartRate,
                    systolicBP: this.systolicBP,
                    diastolicBP: this.diastolicBP,
                    spo2: this.spo2,
                    temperature: parseFloat(this.temperature.toFixed(1)),
                    respiratoryRate: this.respiratoryRate,
                    glucose: parseFloat(this.glucose.toFixed(1)),
                    qtInterval: this.qtInterval,
                    stressIndex: this.stressIndex,
                    timeOfDay: this.timeOfDay,
                    physiologicalState: this.physiologicalState
                };
            }
            
            updateStats(data) {
                this.stats.heartRate.sum += data.heartRate;
                this.stats.heartRate.count++;
                this.stats.systolicBP.sum += data.systolicBP;
                this.stats.systolicBP.count++;
                this.stats.diastolicBP.sum += data.diastolicBP;
                this.stats.diastolicBP.count++;
                this.stats.temperature.sum += data.temperature;
                this.stats.temperature.count++;
            }
            
            checkAbnormal(data) {
                const abnormal = [];
                if (data.heartRate < 60 || data.heartRate > 100) abnormal.push('heartRate');
                if (data.systolicBP < 90 || data.systolicBP > 140) abnormal.push('systolicBP');
                if (data.diastolicBP < 60 || data.diastolicBP > 90) abnormal.push('diastolicBP');
                if (data.spo2 < 95) abnormal.push('spo2');
                if (data.temperature < 36.1 || data.temperature > 37.2) abnormal.push('temperature');
                if (data.respiratoryRate < 12 || data.respiratoryRate > 20) abnormal.push('respiratoryRate');
                if (data.glucose < 3.9 || data.glucose > 6.1) abnormal.push('glucose');
                if (data.qtInterval < 350 || data.qtInterval > 450) abnormal.push('qtInterval');
                if (data.stressIndex > 30) abnormal.push('stressIndex');
                return abnormal;
            }
            
            simulateStressEvent() {
                this.stressLevel = Math.min(10, this.stressLevel + Math.random() * 5);
            }
        }
        
        // 全局变量
        let simulator = new MedicalParameterSimulator();
        let testInterval = null;
        
        // 开始测试
        function startTest() {
            if (testInterval) return;

            simulator.isRunning = true;
            document.getElementById('startBtn').classList.add('active');
            document.getElementById('stopBtn').classList.remove('active');

            addOutput('开始医学生理参数测试');

            testInterval = setInterval(() => {
                const data = simulator.simulateAllParameters();
                simulator.dataCount++;
                simulator.updateStats(data);

                updateDisplay(data);
                updateTimeInfo();
                updateStats();

                const abnormal = simulator.checkAbnormal(data);
                updateAbnormalStatus(abnormal);

                // 输出数据日志
                const logMessage = `心率=${data.heartRate}, 血压=${data.systolicBP}/${data.diastolicBP}, 血氧=${data.spo2}%, 体温=${data.temperature}°C, 呼吸=${data.respiratoryRate}, 血糖=${data.glucose}, QT=${data.qtInterval}ms, 压力=${data.stressIndex}`;
                addOutput(logMessage);
            }, 1000);
        }

        // 停止测试
        function stopTest() {
            if (testInterval) {
                clearInterval(testInterval);
                testInterval = null;
                simulator.isRunning = false;

                document.getElementById('startBtn').classList.remove('active');
                document.getElementById('stopBtn').classList.add('active');

                addOutput('停止医学生理参数测试');
            }
        }

        // 重置测试
        function resetTest() {
            stopTest();
            simulator = new MedicalParameterSimulator();

            // 重置显示
            const elements = ['heartRate', 'bloodPressure', 'spo2', 'temperature', 'respiratoryRate', 'glucose', 'qtInterval', 'stressIndex'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    if (id === 'bloodPressure') {
                        element.textContent = '--/--';
                    } else if (id === 'spo2') {
                        element.textContent = '--%';
                    } else if (id === 'temperature') {
                        element.textContent = '--°C';
                    } else if (id === 'qtInterval') {
                        element.textContent = '--ms';
                    } else {
                        element.textContent = '--';
                    }
                }
            });

            // 重置卡片状态
            const cards = document.querySelectorAll('.parameter-card');
            cards.forEach(card => card.classList.remove('abnormal'));

            // 清空输出
            document.getElementById('output').innerHTML = '';

            updateTimeInfo();
            updateStats();
            addOutput('系统已重置');
        }

        // 更新显示
        function updateDisplay(data) {
            document.getElementById('heartRate').textContent = data.heartRate;
            document.getElementById('bloodPressure').textContent = `${data.systolicBP}/${data.diastolicBP}`;
            document.getElementById('spo2').textContent = `${data.spo2}%`;
            document.getElementById('temperature').textContent = `${data.temperature}°C`;
            document.getElementById('respiratoryRate').textContent = data.respiratoryRate;
            document.getElementById('glucose').textContent = data.glucose;
            document.getElementById('qtInterval').textContent = `${data.qtInterval}ms`;
            document.getElementById('stressIndex').textContent = data.stressIndex;
        }

        // 更新时间信息
        function updateTimeInfo() {
            const hours = Math.floor(simulator.timeOfDay);
            const minutes = Math.floor((simulator.timeOfDay - hours) * 60);
            document.getElementById('simulatedTime').textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

            const stateMap = {
                'normal': '正常',
                'sleep': '睡眠',
                'active': '活跃',
                'stress': '压力'
            };
            document.getElementById('physiologicalState').textContent = stateMap[simulator.physiologicalState] || '正常';
            document.getElementById('dataCount').textContent = simulator.dataCount;
        }

        // 更新统计信息
        function updateStats() {
            const stats = simulator.stats;

            if (stats.heartRate.count > 0) {
                document.getElementById('avgHeartRate').textContent = Math.round(stats.heartRate.sum / stats.heartRate.count);
            }

            if (stats.systolicBP.count > 0 && stats.diastolicBP.count > 0) {
                const avgSys = Math.round(stats.systolicBP.sum / stats.systolicBP.count);
                const avgDia = Math.round(stats.diastolicBP.sum / stats.diastolicBP.count);
                document.getElementById('avgBloodPressure').textContent = `${avgSys}/${avgDia}`;
            }

            if (stats.temperature.count > 0) {
                document.getElementById('avgTemperature').textContent = (stats.temperature.sum / stats.temperature.count).toFixed(1);
            }
        }

        // 更新异常状态
        function updateAbnormalStatus(abnormalList) {
            document.getElementById('abnormalCount').textContent = abnormalList.length;

            // 重置所有卡片
            const cards = document.querySelectorAll('.parameter-card');
            cards.forEach(card => card.classList.remove('abnormal'));

            // 标记异常卡片
            const cardMapping = {
                'heartRate': 'heartRateCard',
                'systolicBP': 'bloodPressureCard',
                'diastolicBP': 'bloodPressureCard',
                'spo2': 'spo2Card',
                'temperature': 'temperatureCard',
                'respiratoryRate': 'respiratoryCard',
                'glucose': 'glucoseCard',
                'qtInterval': 'qtCard',
                'stressIndex': 'stressCard'
            };

            abnormalList.forEach(param => {
                const cardId = cardMapping[param];
                if (cardId) {
                    const card = document.getElementById(cardId);
                    if (card) card.classList.add('abnormal');
                }
            });
        }

        // 添加输出日志
        function addOutput(text) {
            const output = document.getElementById('output');
            const line = document.createElement('div');
            line.textContent = `[${new Date().toLocaleTimeString()}] ${text}`;
            output.appendChild(line);
            output.scrollTop = output.scrollHeight;

            // 限制日志条目数量
            const lines = output.children;
            if (lines.length > 100) {
                output.removeChild(lines[0]);
            }
        }

        // 模拟压力事件
        function simulateStress() {
            simulator.simulateStressEvent();
            addOutput('模拟压力事件触发，压力水平增加');
        }

        // 导出数据
        function exportData() {
            if (simulator.dataCount === 0) {
                alert('没有数据可导出');
                return;
            }

            // 创建简单的数据摘要
            const stats = simulator.stats;
            let csvContent = "参数,平均值,数据点数\n";

            if (stats.heartRate.count > 0) {
                csvContent += `心率,${(stats.heartRate.sum / stats.heartRate.count).toFixed(1)},${stats.heartRate.count}\n`;
            }
            if (stats.systolicBP.count > 0) {
                csvContent += `收缩压,${(stats.systolicBP.sum / stats.systolicBP.count).toFixed(1)},${stats.systolicBP.count}\n`;
            }
            if (stats.diastolicBP.count > 0) {
                csvContent += `舒张压,${(stats.diastolicBP.sum / stats.diastolicBP.count).toFixed(1)},${stats.diastolicBP.count}\n`;
            }
            if (stats.temperature.count > 0) {
                csvContent += `体温,${(stats.temperature.sum / stats.temperature.count).toFixed(2)},${stats.temperature.count}\n`;
            }

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `medical_test_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
            link.click();
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            addOutput('医学生理参数测试系统已启动');
            addOutput('系统将模拟8种医学生理参数的真实波动特征');
            addOutput('特点：体温极稳定，血氧高稳定，血压中等稳定，心率有自然变异');
            updateTimeInfo();
            updateStats();
        });
    </script>
</body>
</html>
