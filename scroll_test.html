<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            overflow-x: hidden;
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            padding: 20px;
            padding-top: 40px;
            padding-bottom: 40px;
            position: relative;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 60px 50px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            max-width: 900px;
            width: 100%;
            text-align: center;
        }

        .section {
            margin-bottom: 50px;
            padding: 30px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            border: 2px solid rgba(102, 126, 234, 0.2);
        }

        h1 {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        h2 {
            font-size: 1.8em;
            color: #34495e;
            margin-bottom: 15px;
        }

        p {
            font-size: 1.1em;
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .scroll-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            font-size: 0.9em;
            color: #2c3e50;
            z-index: 1000;
        }

        .test-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        /* 自定义滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        @media (max-width: 768px) {
            body {
                padding: 15px;
                padding-top: 20px;
                padding-bottom: 20px;
            }
            
            .container {
                padding: 40px 30px;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="scroll-indicator" id="scrollIndicator">
        滚动位置: <span id="scrollPosition">0</span>px
    </div>

    <div class="container">
        <h1>🖱️ 滚动功能测试页面</h1>
        
        <div class="section">
            <h2>📏 页面信息</h2>
            <p>页面总高度: <span id="pageHeight">计算中...</span></p>
            <p>视口高度: <span id="viewportHeight">计算中...</span></p>
            <p>是否可滚动: <span id="scrollable">检测中...</span></p>
        </div>

        <div class="section">
            <h2>🧪 滚动测试</h2>
            <p>请尝试使用以下方式滚动页面：</p>
            <ul style="text-align: left; margin: 20px 0;">
                <li>🖱️ 鼠标滚轮上下滚动</li>
                <li>📱 触摸屏上下滑动</li>
                <li>⌨️ 键盘上下箭头键</li>
                <li>⌨️ Page Up / Page Down 键</li>
                <li>⌨️ Home / End 键</li>
            </ul>
            <button class="test-button" onclick="scrollToTop()">回到顶部</button>
            <button class="test-button" onclick="scrollToBottom()">滚动到底部</button>
        </div>

        <div class="section">
            <h2>🎯 测试区域 1</h2>
            <p>这是第一个测试区域。如果您能看到这个区域，说明页面正在正常显示。</p>
            <p>请继续向下滚动查看更多内容。</p>
        </div>

        <div class="section">
            <h2>🎯 测试区域 2</h2>
            <p>这是第二个测试区域。如果您通过滚动看到了这里，说明滚动功能正常工作。</p>
            <p>页面应该能够平滑滚动，滚动条应该在右侧显示。</p>
        </div>

        <div class="section">
            <h2>🎯 测试区域 3</h2>
            <p>这是第三个测试区域。继续测试滚动功能。</p>
            <p>注意观察右上角的滚动位置指示器，它应该实时更新。</p>
        </div>

        <div class="section">
            <h2>🎯 测试区域 4</h2>
            <p>这是第四个测试区域。如果您能滚动到这里，说明滚动功能完全正常。</p>
            <p>您可以尝试快速滚动和慢速滚动，测试不同的滚动速度。</p>
        </div>

        <div class="section">
            <h2>🎯 测试区域 5</h2>
            <p>这是第五个测试区域。这里是页面的中间部分。</p>
            <p>请继续向下滚动，测试页面底部的滚动行为。</p>
        </div>

        <div class="section">
            <h2>🎯 测试区域 6</h2>
            <p>这是第六个测试区域。您已经滚动了很长的距离。</p>
            <p>滚动条应该显示您当前的位置，接近页面底部。</p>
        </div>

        <div class="section">
            <h2>🎯 测试区域 7 (最后)</h2>
            <p>这是最后一个测试区域。如果您能滚动到这里，说明：</p>
            <ul style="text-align: left; margin: 20px 0;">
                <li>✅ 鼠标滚轮功能正常</li>
                <li>✅ 页面滚动功能正常</li>
                <li>✅ 滚动条显示正常</li>
                <li>✅ 响应式布局正常</li>
            </ul>
            <button class="test-button" onclick="scrollToTop()">🔝 返回顶部</button>
        </div>
    </div>

    <script>
        // 更新滚动位置指示器
        function updateScrollIndicator() {
            const scrollPosition = document.getElementById('scrollPosition');
            if (scrollPosition) {
                scrollPosition.textContent = Math.round(window.scrollY);
            }
        }

        // 滚动到顶部
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 滚动到底部
        function scrollToBottom() {
            window.scrollTo({
                top: document.body.scrollHeight,
                behavior: 'smooth'
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 更新页面信息
            const pageHeight = document.getElementById('pageHeight');
            const viewportHeight = document.getElementById('viewportHeight');
            const scrollable = document.getElementById('scrollable');

            if (pageHeight) pageHeight.textContent = document.body.scrollHeight + 'px';
            if (viewportHeight) viewportHeight.textContent = window.innerHeight + 'px';
            if (scrollable) {
                scrollable.textContent = document.body.scrollHeight > window.innerHeight ? '是' : '否';
                scrollable.style.color = document.body.scrollHeight > window.innerHeight ? '#27ae60' : '#e74c3c';
            }

            // 添加滚动事件监听
            window.addEventListener('scroll', updateScrollIndicator);
            
            // 初始化滚动位置
            updateScrollIndicator();

            console.log('📜 滚动测试页面加载完成');
            console.log('页面高度:', document.body.scrollHeight);
            console.log('视口高度:', window.innerHeight);
            console.log('可滚动:', document.body.scrollHeight > window.innerHeight);
        });
    </script>
</body>
</html>
