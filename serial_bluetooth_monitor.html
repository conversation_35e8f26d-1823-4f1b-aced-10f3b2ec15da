<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>串口蓝牙健康监测</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2d3436;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .connection-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            background: #ddd;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 10px 0;
        }

        .status.disconnected { background: #ffebee; color: #c62828; }
        .status.connected { background: #e8f5e8; color: #2e7d32; }

        .data-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .data-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .data-item:hover {
            transform: scale(1.05);
        }

        .data-item .icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .data-item .value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .data-item .label {
            font-size: 1em;
            opacity: 0.9;
        }

        .terminal {
            background: #2d3436;
            color: #00b894;
            border-radius: 10px;
            padding: 20px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .terminal-line {
            margin-bottom: 5px;
            word-wrap: break-word;
        }

        .terminal-line.error {
            color: #ff7675;
        }

        .terminal-line.alarm {
            color: #fd79a8;
            font-weight: bold;
        }

        .input-section {
            margin-top: 20px;
        }

        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .input-group input {
            flex: 1;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
        }

        .fingerprint-indicator {
            background: #f1f2f6;
            border: 2px solid #3742fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .fingerprint-indicator.active {
            background: #00d2d3;
            color: white;
            border-color: #00a085;
        }

        .pulse {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 串口蓝牙健康监测</h1>
            <p>通过Web Serial API连接蓝牙串口模块</p>
        </div>

        <div class="connection-section">
            <div class="status disconnected" id="status">未连接</div>
            <br>
            <button class="btn" id="connectBtn" onclick="connectSerial()">连接串口</button>
            <button class="btn" id="disconnectBtn" onclick="disconnectSerial()" disabled>断开连接</button>
            <button class="btn" onclick="clearTerminal()">清空终端</button>
        </div>

        <div class="fingerprint-indicator" id="fingerprintStatus">
            <h3>👆 指纹检测状态</h3>
            <p id="fingerprintText">等待连接...</p>
        </div>

        <div class="data-display">
            <div class="data-item">
                <div class="icon">💓</div>
                <div class="value" id="heartRate">--</div>
                <div class="label">心率 (次/分)</div>
            </div>
            <div class="data-item">
                <div class="icon">🫁</div>
                <div class="value" id="spo2">--</div>
                <div class="label">血氧 (%)</div>
            </div>
            <div class="data-item">
                <div class="icon">🌡️</div>
                <div class="value" id="temperature">--</div>
                <div class="label">体温 (°C)</div>
            </div>
        </div>

        <div class="input-section">
            <h3>📡 串口终端</h3>
            <div class="terminal" id="terminal"></div>
            <div class="input-group">
                <input type="text" id="commandInput" placeholder="输入命令..." onkeypress="handleKeyPress(event)">
                <button class="btn" onclick="sendCommand()">发送</button>
            </div>
        </div>
    </div>

    <script>
        let port;
        let reader;
        let isConnected = false;

        // 更新连接状态
        function updateStatus(text, className) {
            const status = document.getElementById('status');
            status.textContent = text;
            status.className = `status ${className}`;
        }

        // 添加终端输出
        function addToTerminal(text, className = '') {
            const terminal = document.getElementById('terminal');
            const line = document.createElement('div');
            line.className = `terminal-line ${className}`;
            line.textContent = `[${new Date().toLocaleTimeString()}] ${text}`;
            terminal.appendChild(line);
            terminal.scrollTop = terminal.scrollHeight;
        }

        // 连接串口
        async function connectSerial() {
            try {
                // 请求串口访问
                port = await navigator.serial.requestPort();
                
                // 配置串口参数
                await port.open({ 
                    baudRate: 9600,  // 与蓝牙模块波特率匹配
                    dataBits: 8,
                    stopBits: 1,
                    parity: 'none'
                });

                updateStatus('已连接', 'connected');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;

                addToTerminal('串口连接成功');
                
                // 开始读取数据
                startReading();

            } catch (error) {
                console.error('连接失败:', error);
                updateStatus('连接失败', 'disconnected');
                addToTerminal(`连接失败: ${error.message}`, 'error');
            }
        }

        // 断开串口连接
        async function disconnectSerial() {
            try {
                if (reader) {
                    await reader.cancel();
                }
                if (port) {
                    await port.close();
                }
                
                isConnected = false;
                updateStatus('未连接', 'disconnected');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                
                addToTerminal('串口连接已断开');
                
            } catch (error) {
                console.error('断开连接失败:', error);
                addToTerminal(`断开连接失败: ${error.message}`, 'error');
            }
        }

        // 开始读取数据
        async function startReading() {
            try {
                const textDecoder = new TextDecoder();
                reader = port.readable.getReader();
                isConnected = true;

                while (isConnected) {
                    const { value, done } = await reader.read();
                    if (done) break;

                    const text = textDecoder.decode(value);
                    processReceivedData(text);
                }

            } catch (error) {
                console.error('读取数据错误:', error);
                addToTerminal(`读取错误: ${error.message}`, 'error');
            } finally {
                if (reader) {
                    reader.releaseLock();
                }
            }
        }

        // 处理接收到的数据
        function processReceivedData(data) {
            addToTerminal(`接收: ${data}`);

            // 解析健康数据
            if (data.includes('健康数据:')) {
                parseHealthData(data);
            } 
            // 检测指纹状态
            else if (data.includes('手指检测到') || data.includes('Finger detected')) {
                updateFingerprintStatus(true);
            } 
            else if (data.includes('手指移开') || data.includes('Finger removed')) {
                updateFingerprintStatus(false);
            }
            // 检测报警信息
            else if (data.includes('病人编号001') || data.includes('异常反应')) {
                addToTerminal(`🚨 报警: ${data}`, 'alarm');
                showAlarmNotification();
            }
        }

        // 解析健康数据
        function parseHealthData(data) {
            try {
                // 解析心率
                const hrMatch = data.match(/心率=(\d+)/);
                if (hrMatch) {
                    updateDataDisplay('heartRate', hrMatch[1]);
                }

                // 解析血氧
                const spo2Match = data.match(/血氧=(\d+)/);
                if (spo2Match) {
                    updateDataDisplay('spo2', spo2Match[1]);
                }

                // 解析体温
                const tempMatch = data.match(/体温=(\d+\.?\d*)/);
                if (tempMatch) {
                    updateDataDisplay('temperature', tempMatch[1]);
                }

            } catch (error) {
                addToTerminal(`数据解析错误: ${error.message}`, 'error');
            }
        }

        // 更新数据显示
        function updateDataDisplay(elementId, value) {
            const element = document.getElementById(elementId);
            element.textContent = value;
            element.parentElement.classList.add('pulse');
            setTimeout(() => {
                element.parentElement.classList.remove('pulse');
            }, 1000);
        }

        // 更新指纹状态
        function updateFingerprintStatus(detected) {
            const indicator = document.getElementById('fingerprintStatus');
            const text = document.getElementById('fingerprintText');
            
            if (detected) {
                indicator.classList.add('active');
                text.textContent = '✅ 手指已检测到，正在监测...';
            } else {
                indicator.classList.remove('active');
                text.textContent = '⏳ 等待手指接触...';
            }
        }

        // 显示报警通知
        function showAlarmNotification() {
            // 页面闪烁效果
            document.body.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
            setTimeout(() => {
                document.body.style.background = 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)';
            }, 2000);

            // 浏览器通知
            if (Notification.permission === 'granted') {
                new Notification('健康监测报警', {
                    body: '检测到异常生理数据，请立即查看！',
                    icon: '🚨'
                });
            }
        }

        // 发送命令
        async function sendCommand() {
            const input = document.getElementById('commandInput');
            const command = input.value.trim();
            
            if (!command || !isConnected) return;

            try {
                const encoder = new TextEncoder();
                const writer = port.writable.getWriter();
                await writer.write(encoder.encode(command + '\r\n'));
                writer.releaseLock();
                
                addToTerminal(`发送: ${command}`);
                input.value = '';
                
            } catch (error) {
                addToTerminal(`发送失败: ${error.message}`, 'error');
            }
        }

        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendCommand();
            }
        }

        // 清空终端
        function clearTerminal() {
            document.getElementById('terminal').innerHTML = '';
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            // 检查浏览器支持
            if (!('serial' in navigator)) {
                addToTerminal('此浏览器不支持Web Serial API', 'error');
                addToTerminal('请使用Chrome 89+或Edge 89+浏览器', 'error');
                document.getElementById('connectBtn').disabled = true;
                return;
            }

            // 请求通知权限
            if (Notification.permission === 'default') {
                Notification.requestPermission();
            }

            addToTerminal('串口蓝牙监测系统已启动');
            addToTerminal('点击"连接串口"开始连接蓝牙模块');
        });
    </script>
</body>
</html>
