<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蓝牙健康监测系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 100%;
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .connection-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 10px;
        }

        .status.disconnected {
            background: #ffebee;
            color: #c62828;
        }

        .status.connecting {
            background: #fff3e0;
            color: #ef6c00;
        }

        .status.connected {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .data-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .data-card:hover {
            transform: translateY(-5px);
        }

        .data-card .icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .data-card .value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .data-card .label {
            color: #666;
            font-size: 1.1em;
        }

        .heart-rate .icon { color: #e74c3c; }
        .heart-rate .value { color: #e74c3c; }

        .spo2 .icon { color: #3498db; }
        .spo2 .value { color: #3498db; }

        .temperature .icon { color: #f39c12; }
        .temperature .value { color: #f39c12; }

        .log-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-panel h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .log-entry {
            padding: 8px 12px;
            margin-bottom: 5px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .log-entry.info {
            background: #e3f2fd;
            color: #1976d2;
        }

        .log-entry.data {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .log-entry.alarm {
            background: #ffebee;
            color: #c62828;
            font-weight: bold;
        }

        .log-entry.error {
            background: #fce4ec;
            color: #ad1457;
        }

        .pulse {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .fingerprint-status {
            background: #f0f8ff;
            border: 2px solid #4169e1;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .fingerprint-status.active {
            background: #e8f5e8;
            border-color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 蓝牙健康监测系统</h1>
            <p>实时监测心率、血氧、体温数据</p>
        </div>

        <div class="connection-panel">
            <div class="status disconnected" id="connectionStatus">未连接</div>
            <br>
            <button class="btn" id="connectBtn" onclick="connectBluetooth()">连接蓝牙设备</button>
            <button class="btn" id="disconnectBtn" onclick="disconnectBluetooth()" disabled>断开连接</button>
        </div>

        <div class="fingerprint-status" id="fingerprintStatus">
            <h3>👆 指纹检测状态</h3>
            <p id="fingerprintText">等待手指接触...</p>
        </div>

        <div class="data-grid">
            <div class="data-card heart-rate">
                <div class="icon">💓</div>
                <div class="value" id="heartRate">--</div>
                <div class="label">心率 (次/分)</div>
            </div>
            <div class="data-card spo2">
                <div class="icon">🫁</div>
                <div class="value" id="spo2">--</div>
                <div class="label">血氧饱和度 (%)</div>
            </div>
            <div class="data-card temperature">
                <div class="icon">🌡️</div>
                <div class="value" id="temperature">--</div>
                <div class="label">体温 (°C)</div>
            </div>
        </div>

        <div class="log-panel">
            <h3>📋 数据日志</h3>
            <div id="logContainer"></div>
        </div>
    </div>

    <script>
        let bluetoothDevice;
        let characteristic;
        let isConnected = false;

        // 蓝牙服务和特征UUID (使用标准串口服务)
        const SERVICE_UUID = '6e400001-b5a3-f393-e0a9-e50e24dcca9e';
        const CHARACTERISTIC_UUID = '6e400003-b5a3-f393-e0a9-e50e24dcca9e';

        // 更新连接状态
        function updateConnectionStatus(status, className) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.textContent = status;
            statusElement.className = `status ${className}`;
        }

        // 添加日志条目
        function addLogEntry(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 连接蓝牙设备
        async function connectBluetooth() {
            try {
                updateConnectionStatus('连接中...', 'connecting');
                addLogEntry('开始搜索蓝牙设备...', 'info');

                // 请求蓝牙设备
                bluetoothDevice = await navigator.bluetooth.requestDevice({
                    filters: [
                        { name: 'HC-05' },
                        { name: 'HC-06' },
                        { namePrefix: 'ESP32' },
                        { namePrefix: 'Arduino' }
                    ],
                    optionalServices: [SERVICE_UUID, '0000ffe0-0000-1000-8000-00805f9b34fb']
                });

                addLogEntry(`找到设备: ${bluetoothDevice.name}`, 'info');

                // 连接到GATT服务器
                const server = await bluetoothDevice.gatt.connect();
                addLogEntry('连接到GATT服务器', 'info');

                // 获取服务
                let service;
                try {
                    service = await server.getPrimaryService(SERVICE_UUID);
                } catch (e) {
                    // 尝试使用备用服务UUID
                    service = await server.getPrimaryService('0000ffe0-0000-1000-8000-00805f9b34fb');
                }

                // 获取特征
                try {
                    characteristic = await service.getCharacteristic(CHARACTERISTIC_UUID);
                } catch (e) {
                    // 尝试使用备用特征UUID
                    characteristic = await service.getCharacteristic('0000ffe1-0000-1000-8000-00805f9b34fb');
                }

                // 启用通知
                await characteristic.startNotifications();
                characteristic.addEventListener('characteristicvaluechanged', handleDataReceived);

                // 监听断开连接事件
                bluetoothDevice.addEventListener('gattserverdisconnected', onDisconnected);

                isConnected = true;
                updateConnectionStatus('已连接', 'connected');
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;

                addLogEntry(`成功连接到 ${bluetoothDevice.name}`, 'info');

            } catch (error) {
                console.error('连接失败:', error);
                updateConnectionStatus('连接失败', 'disconnected');
                addLogEntry(`连接失败: ${error.message}`, 'error');
            }
        }

        // 断开蓝牙连接
        function disconnectBluetooth() {
            if (bluetoothDevice && bluetoothDevice.gatt.connected) {
                bluetoothDevice.gatt.disconnect();
            }
        }

        // 处理断开连接事件
        function onDisconnected() {
            isConnected = false;
            updateConnectionStatus('未连接', 'disconnected');
            document.getElementById('connectBtn').disabled = false;
            document.getElementById('disconnectBtn').disabled = true;
            addLogEntry('蓝牙连接已断开', 'info');
        }

        // 处理接收到的数据
        function handleDataReceived(event) {
            const value = new TextDecoder().decode(event.target.value);
            console.log('接收到数据:', value);

            // 解析不同类型的数据
            if (value.includes('健康数据:')) {
                parseHealthData(value);
            } else if (value.includes('手指检测到')) {
                updateFingerprintStatus(true);
                addLogEntry('检测到手指，开始监测', 'info');
            } else if (value.includes('手指移开')) {
                updateFingerprintStatus(false);
                addLogEntry('手指移开，停止监测', 'info');
            } else if (value.includes('病人编号001')) {
                addLogEntry(`🚨 报警: ${value}`, 'alarm');
                showAlarmNotification(value);
            } else {
                addLogEntry(`接收: ${value}`, 'data');
            }
        }

        // 解析健康数据
        function parseHealthData(data) {
            try {
                // 解析格式: "健康数据:心率=75次/分,血氧=94%,体温=36.5°C"
                const heartRateMatch = data.match(/心率=(\d+)次\/分/);
                const spo2Match = data.match(/血氧=(\d+)%/);
                const tempMatch = data.match(/体温=(\d+\.?\d*)°C/);

                if (heartRateMatch) {
                    updateHeartRate(parseInt(heartRateMatch[1]));
                }
                if (spo2Match) {
                    updateSpO2(parseInt(spo2Match[1]));
                }
                if (tempMatch) {
                    updateTemperature(parseFloat(tempMatch[1]));
                }

                addLogEntry(`数据更新: 心率=${heartRateMatch?.[1] || '--'}, 血氧=${spo2Match?.[1] || '--'}, 体温=${tempMatch?.[1] || '--'}`, 'data');

            } catch (error) {
                console.error('数据解析错误:', error);
                addLogEntry(`数据解析错误: ${error.message}`, 'error');
            }
        }

        // 更新心率显示
        function updateHeartRate(value) {
            const element = document.getElementById('heartRate');
            element.textContent = value;
            element.parentElement.classList.add('pulse');
            setTimeout(() => element.parentElement.classList.remove('pulse'), 1000);
        }

        // 更新血氧显示
        function updateSpO2(value) {
            const element = document.getElementById('spo2');
            element.textContent = value;
            element.parentElement.classList.add('pulse');
            setTimeout(() => element.parentElement.classList.remove('pulse'), 1000);
        }

        // 更新体温显示
        function updateTemperature(value) {
            const element = document.getElementById('temperature');
            element.textContent = value.toFixed(1);
            element.parentElement.classList.add('pulse');
            setTimeout(() => element.parentElement.classList.remove('pulse'), 1000);
        }

        // 更新指纹检测状态
        function updateFingerprintStatus(detected) {
            const statusElement = document.getElementById('fingerprintStatus');
            const textElement = document.getElementById('fingerprintText');
            
            if (detected) {
                statusElement.classList.add('active');
                textElement.textContent = '✅ 手指已检测到，正在监测...';
            } else {
                statusElement.classList.remove('active');
                textElement.textContent = '⏳ 等待手指接触...';
            }
        }

        // 显示报警通知
        function showAlarmNotification(message) {
            // 浏览器通知
            if (Notification.permission === 'granted') {
                new Notification('健康监测报警', {
                    body: message,
                    icon: '🚨'
                });
            }

            // 页面闪烁效果
            document.body.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
            setTimeout(() => {
                document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            }, 2000);
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            addLogEntry('蓝牙健康监测系统已启动', 'info');
            
            // 检查浏览器是否支持Web Bluetooth
            if (!navigator.bluetooth) {
                addLogEntry('此浏览器不支持Web Bluetooth API', 'error');
                document.getElementById('connectBtn').disabled = true;
                return;
            }

            // 请求通知权限
            if (Notification.permission === 'default') {
                Notification.requestPermission();
            }

            addLogEntry('系统就绪，可以连接蓝牙设备', 'info');
        });
    </script>
</body>
</html>
