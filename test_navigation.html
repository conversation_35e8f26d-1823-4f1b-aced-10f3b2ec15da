<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .view {
            display: none;
            padding: 20px;
            background: white;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .view.active {
            display: block;
        }
        
        .view.hidden {
            display: none;
        }
        
        .management-view:not(.hidden) {
            display: block;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .status {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>导航功能测试</h1>
    
    <div class="status" id="status">状态：等待操作</div>
    
    <!-- 管理界面 -->
    <div class="view management-view" id="managementView">
        <h2>管理界面</h2>
        <p>这是主管理界面</p>
        <button class="btn" onclick="enterDetail()">进入详细监测</button>
    </div>
    
    <!-- 详细监测界面 -->
    <div class="view detail-view" id="detailView">
        <h2>详细监测界面</h2>
        <p>这是详细监测界面</p>
        <button class="btn" onclick="backToManagement()">← 返回管理界面</button>
        <button class="btn" onclick="debugState()">🔍 调试状态</button>
    </div>

    <script>
        let currentSubjectId = null;
        
        function updateStatus(message) {
            document.getElementById('status').textContent = '状态：' + message;
            console.log('📊 ' + message);
        }
        
        function enterDetail() {
            console.log('🔍 进入详细监测');
            updateStatus('进入详细监测');
            
            const managementView = document.getElementById('managementView');
            const detailView = document.getElementById('detailView');
            
            console.log('切换前状态:', {
                managementClasses: managementView.className,
                detailClasses: detailView.className
            });
            
            managementView.classList.add('hidden');
            detailView.classList.add('active');
            currentSubjectId = 'P001';
            
            console.log('切换后状态:', {
                managementClasses: managementView.className,
                detailClasses: detailView.className
            });
        }
        
        function backToManagement() {
            console.log('🔙 返回管理界面');
            updateStatus('返回管理界面');
            
            const detailView = document.getElementById('detailView');
            const managementView = document.getElementById('managementView');
            
            console.log('返回前状态:', {
                managementClasses: managementView.className,
                detailClasses: detailView.className
            });
            
            detailView.classList.remove('active');
            detailView.style.display = 'none';
            
            managementView.classList.remove('hidden');
            managementView.style.display = 'block';
            
            currentSubjectId = null;
            
            console.log('返回后状态:', {
                managementClasses: managementView.className,
                detailClasses: detailView.className
            });
        }
        
        function debugState() {
            const managementView = document.getElementById('managementView');
            const detailView = document.getElementById('detailView');
            
            const state = {
                managementView: {
                    exists: !!managementView,
                    classes: managementView?.className,
                    display: managementView?.style.display,
                    visible: managementView && !managementView.classList.contains('hidden')
                },
                detailView: {
                    exists: !!detailView,
                    classes: detailView?.className,
                    display: detailView?.style.display,
                    active: detailView && detailView.classList.contains('active')
                },
                currentSubjectId: currentSubjectId
            };
            
            console.log('🔍 当前状态:', state);
            updateStatus('状态已输出到控制台');
            alert('状态信息已输出到浏览器控制台，请按F12查看');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌟 测试页面加载完成');
            updateStatus('页面加载完成，可以开始测试');
        });
    </script>
</body>
</html>
