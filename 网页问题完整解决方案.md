# 智能医疗监测系统 - 网页问题完整解决方案

## 🚨 问题现象分析

根据您提供的截图，网页显示"未找到文件"错误，具体表现为：

```
浏览器地址栏: C:/Users/<USER>/Desktop/智能门锁/integrated_medical_final.html/mode=long
错误信息: 未找到文件 - 它可能已被移动、编辑或删除
```

## 🔍 根本原因分析

### 1. **URL路径错误**
- ❌ 错误路径包含了额外的 `/mode=long` 参数
- ❌ 直接使用文件系统路径访问HTML文件
- ❌ 缺少Web服务器环境

### 2. **文件访问方式不当**
- HTML文件包含JavaScript模块和外部资源
- 直接双击打开会触发浏览器安全限制
- 需要通过HTTP协议访问

### 3. **依赖资源加载失败**
- Chart.js等外部库需要网络环境
- 相对路径资源无法正确加载
- CORS策略限制本地文件访问

## ✅ 完整解决方案

### 方案一：使用Node.js服务器（推荐）

**步骤1：安装Node.js**
```bash
# 下载并安装Node.js (https://nodejs.org)
# 验证安装
node --version
npm --version
```

**步骤2：启动Web服务器**
```bash
# 进入项目目录
cd "C:\Users\<USER>\Desktop\智能门锁\CH32V307VCT6蓝牙"

# 安装http-server
npm install -g http-server

# 启动服务器
http-server -p 8080 -c-1
```

**步骤3：访问网页**
```
浏览器访问: http://localhost:8080/integrated_medical_final.html
```

### 方案二：使用Python服务器

**Python 3.x:**
```bash
cd "C:\Users\<USER>\Desktop\智能门锁\CH32V307VCT6蓝牙"
python -m http.server 8080
```

**Python 2.x:**
```bash
cd "C:\Users\<USER>\Desktop\智能门锁\CH32V307VCT6蓝牙"
python -m SimpleHTTPServer 8080
```

**访问地址:**
```
http://localhost:8080/integrated_medical_final.html
```

### 方案三：使用VS Code Live Server

**步骤1：安装VS Code**
- 下载安装Visual Studio Code

**步骤2：安装Live Server扩展**
- 在VS Code中搜索"Live Server"扩展并安装

**步骤3：启动服务**
- 用VS Code打开项目文件夹
- 右键点击HTML文件
- 选择"Open with Live Server"

## 🔧 硬件连接配置

### WebSocket服务器启动

**创建服务器文件 (server.js):**
```javascript
const WebSocket = require('ws');
const http = require('http');
const fs = require('fs');
const path = require('path');

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    let filePath = path.join(__dirname, req.url === '/' ? 'integrated_medical_final.html' : req.url);
    let extname = path.extname(filePath);
    let contentType = 'text/html';
    
    switch(extname) {
        case '.js': contentType = 'text/javascript'; break;
        case '.css': contentType = 'text/css'; break;
        case '.json': contentType = 'application/json'; break;
    }
    
    fs.readFile(filePath, (error, content) => {
        if (error) {
            res.writeHead(404);
            res.end('File not found');
        } else {
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(content, 'utf-8');
        }
    });
});

// 创建WebSocket服务器
const wss = new WebSocket.Server({ server });

wss.on('connection', (ws) => {
    console.log('设备已连接');
    
    // 模拟医疗数据
    const sendData = () => {
        const data = {
            heartRate: 70 + Math.random() * 30,
            systolicBP: 110 + Math.random() * 20,
            diastolicBP: 70 + Math.random() * 15,
            spo2: 95 + Math.random() * 5,
            temperature: 36.0 + Math.random() * 1.5,
            timestamp: Date.now()
        };
        ws.send(JSON.stringify(data));
    };
    
    const interval = setInterval(sendData, 2000);
    
    ws.on('close', () => {
        clearInterval(interval);
        console.log('设备已断开');
    });
});

server.listen(8080, () => {
    console.log('服务器运行在 http://localhost:8080');
});
```

**启动服务器:**
```bash
npm install ws
node server.js
```

### Air780e 4G模块配置

**AT命令配置:**
```bash
AT                              # 测试通信
AT+CGDCONT=1,"IP","CMNET"      # 设置APN
AT+NETOPEN=1,"TCP","localhost",8080  # 连接服务器
AT+ENTM                        # 进入透传模式
```

**数据发送格式:**
```json
{
  "heartRate": 75,
  "systolicBP": 120,
  "diastolicBP": 80,
  "spo2": 98,
  "temperature": 36.5,
  "timestamp": 1703123456789
}
```

## 🛠️ 调试和测试

### 浏览器开发者工具

**打开方式:**
- Chrome: F12 或 Ctrl+Shift+I
- Firefox: F12 或 Ctrl+Shift+I
- Edge: F12 或 Ctrl+Shift+I

**检查项目:**
1. **Console标签**: 查看JavaScript错误
2. **Network标签**: 检查资源加载状态
3. **Application标签**: 查看本地存储数据

### 常见错误解决

**1. CORS错误:**
```
Access to fetch at 'file://...' from origin 'null' has been blocked by CORS policy
```
**解决**: 使用Web服务器而非直接打开文件

**2. 模块加载错误:**
```
Failed to load module script: Expected a JavaScript module script
```
**解决**: 确保服务器正确设置Content-Type

**3. WebSocket连接失败:**
```
WebSocket connection to 'ws://localhost:8080' failed
```
**解决**: 确保WebSocket服务器正在运行

## 📱 移动端适配

### 响应式设计检查

**在浏览器中测试:**
1. 按F12打开开发者工具
2. 点击设备模拟器图标
3. 选择不同设备尺寸测试

**支持的设备:**
- 📱 手机: iPhone、Android
- 📟 平板: iPad、Android平板
- 💻 桌面: Windows、macOS、Linux

## 🔒 安全配置

### HTTPS配置（生产环境）

**生成SSL证书:**
```bash
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes
```

**HTTPS服务器:**
```javascript
const https = require('https');
const fs = require('fs');

const options = {
    key: fs.readFileSync('key.pem'),
    cert: fs.readFileSync('cert.pem')
};

https.createServer(options, app).listen(443);
```

## 📊 性能监控

### 实时监控指标

**关键指标:**
- 页面加载时间: <3秒
- WebSocket延迟: <100ms
- 内存使用: <100MB
- CPU使用率: <10%

**监控工具:**
- Chrome DevTools Performance
- Lighthouse性能评估
- WebSocket连接状态监控

## 🎯 最佳实践

### 开发环境配置

**推荐工具链:**
```bash
# 项目初始化
npm init -y

# 安装开发依赖
npm install --save-dev live-server
npm install --save-dev nodemon

# package.json scripts
{
  "scripts": {
    "start": "live-server --port=8080",
    "dev": "nodemon server.js"
  }
}
```

### 生产部署

**部署检查清单:**
- ✅ 使用HTTPS协议
- ✅ 启用Gzip压缩
- ✅ 设置缓存策略
- ✅ 配置CDN加速
- ✅ 监控系统状态

## 📞 技术支持

**如果问题仍然存在，请检查:**

1. **文件完整性**: 确保HTML文件没有损坏
2. **网络连接**: 检查外部资源是否可访问
3. **浏览器版本**: 使用最新版本的现代浏览器
4. **防火墙设置**: 确保端口8080未被阻止

**联系方式:**
- 查看控制台错误信息
- 检查网络连接状态
- 参考项目文档说明

---

**© 2024 智能医疗监测系统 | 完整解决方案**
