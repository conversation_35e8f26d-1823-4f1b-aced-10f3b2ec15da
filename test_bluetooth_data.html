<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蓝牙数据测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            margin: 0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }

        .btn:hover {
            background: #0056b3;
        }

        .output {
            background: #2d3436;
            color: #00b894;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }

        .data-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .data-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .data-item .value {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .data-item .label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .pulse {
            animation: pulse 0.5s;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 蓝牙数据测试页面</h1>
            <p>模拟CH32V307VCT6发送的数据格式</p>
        </div>

        <div class="test-section">
            <h3>📡 数据格式测试</h3>
            <button class="btn" onclick="testHealthData()">测试健康数据</button>
            <button class="btn" onclick="testFingerDetected()">测试手指检测</button>
            <button class="btn" onclick="testFingerRemoved()">测试手指移开</button>
            <button class="btn" onclick="testAlarmData()">测试报警数据</button>
            <button class="btn" onclick="testRandomData()">随机数据</button>
            <button class="btn" onclick="clearOutput()">清空输出</button>
        </div>

        <div class="test-section">
            <h3>📊 实时数据预览</h3>
            <div class="data-preview">
                <div class="data-item">
                    <div class="value" id="heartRate">--</div>
                    <div class="label">心率 (次/分)</div>
                </div>
                <div class="data-item">
                    <div class="value" id="spo2">--</div>
                    <div class="label">血氧 (%)</div>
                </div>
                <div class="data-item">
                    <div class="value" id="temperature">--</div>
                    <div class="label">体温 (°C)</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 输出日志</h3>
            <div class="output" id="output"></div>
        </div>

        <div class="test-section">
            <h3>📖 数据格式说明</h3>
            <ul>
                <li><strong>健康数据：</strong>健康数据:心率=75次/分,血氧=94%,体温=36.5°C</li>
                <li><strong>手指检测：</strong>手指检测到 - 开始生理数据监测</li>
                <li><strong>手指移开：</strong>手指移开 - 停止生理数据监测</li>
                <li><strong>报警信息：</strong>病人编号001，身体情况出现异常反应，请立即前往4F012查看</li>
            </ul>
        </div>
    </div>

    <script>
        // 添加输出日志
        function addOutput(text, className = '') {
            const output = document.getElementById('output');
            const line = document.createElement('div');
            line.style.color = className === 'alarm' ? '#ff7675' : '#00b894';
            line.style.fontWeight = className === 'alarm' ? 'bold' : 'normal';
            line.textContent = `[${new Date().toLocaleTimeString()}] ${text}`;
            output.appendChild(line);
            output.scrollTop = output.scrollHeight;
        }

        // 更新数据显示
        function updateDataDisplay(elementId, value) {
            const element = document.getElementById(elementId);
            element.textContent = value;
            element.parentElement.classList.add('pulse');
            setTimeout(() => {
                element.parentElement.classList.remove('pulse');
            }, 500);
        }

        // 解析健康数据
        function parseHealthData(data) {
            const hrMatch = data.match(/心率=(\d+)/);
            const spo2Match = data.match(/血氧=(\d+)/);
            const tempMatch = data.match(/体温=(\d+\.?\d*)/);

            if (hrMatch) updateDataDisplay('heartRate', hrMatch[1]);
            if (spo2Match) updateDataDisplay('spo2', spo2Match[1]);
            if (tempMatch) updateDataDisplay('temperature', tempMatch[1]);
        }

        // 测试健康数据
        function testHealthData() {
            const heartRate = Math.floor(Math.random() * 40) + 60; // 60-100
            const spo2 = Math.floor(Math.random() * 10) + 90; // 90-100
            const temperature = (Math.random() * 2 + 36).toFixed(1); // 36.0-38.0

            const data = `健康数据:心率=${heartRate}次/分,血氧=${spo2}%,体温=${temperature}°C`;
            addOutput(data);
            parseHealthData(data);
        }

        // 测试手指检测
        function testFingerDetected() {
            const data = "手指检测到 - 开始生理数据监测";
            addOutput(data);
            addOutput("Finger detected - Health monitoring enabled");
        }

        // 测试手指移开
        function testFingerRemoved() {
            const data = "手指移开 - 停止生理数据监测";
            addOutput(data);
            addOutput("Finger removed - Health monitoring disabled");
            
            // 清空数据显示
            updateDataDisplay('heartRate', '--');
            updateDataDisplay('spo2', '--');
            updateDataDisplay('temperature', '--');
        }

        // 测试报警数据
        function testAlarmData() {
            const alarmMessages = [
                "病人编号001，身体情况出现异常反应，请立即前往4F012查看",
                "🔴🚨心率异常报警🚨🔴 当前心率:130次/分 > 正常值:125次/分 💓🔴🔴",
                "🔴🚨体温异常报警🚨🔴 当前体温:40.5°C > 正常值:40.0°C 🌡️🔴🔴"
            ];
            
            const randomAlarm = alarmMessages[Math.floor(Math.random() * alarmMessages.length)];
            addOutput(randomAlarm, 'alarm');
        }

        // 随机数据测试
        function testRandomData() {
            const testFunctions = [testHealthData, testFingerDetected, testFingerRemoved];
            const randomTest = testFunctions[Math.floor(Math.random() * testFunctions.length)];
            randomTest();
        }

        // 清空输出
        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }

        // 自动演示模式
        function startAutoDemo() {
            let step = 0;
            const steps = [
                () => {
                    addOutput("=== 开始自动演示 ===");
                    testFingerDetected();
                },
                () => {
                    testHealthData();
                },
                () => {
                    testHealthData();
                },
                () => {
                    testAlarmData();
                },
                () => {
                    testFingerRemoved();
                    addOutput("=== 演示结束 ===");
                }
            ];

            const interval = setInterval(() => {
                if (step < steps.length) {
                    steps[step]();
                    step++;
                } else {
                    clearInterval(interval);
                    setTimeout(startAutoDemo, 5000); // 5秒后重新开始
                }
            }, 2000); // 每2秒执行一步
        }

        // 页面加载完成后开始演示
        window.addEventListener('load', () => {
            addOutput("蓝牙数据测试页面已加载");
            addOutput("点击按钮测试不同的数据格式");
            
            // 3秒后开始自动演示
            setTimeout(() => {
                addOutput("开始自动演示模式...");
                startAutoDemo();
            }, 3000);
        });

        // 键盘快捷键
        document.addEventListener('keydown', (event) => {
            switch(event.key) {
                case '1':
                    testHealthData();
                    break;
                case '2':
                    testFingerDetected();
                    break;
                case '3':
                    testFingerRemoved();
                    break;
                case '4':
                    testAlarmData();
                    break;
                case ' ':
                    event.preventDefault();
                    testRandomData();
                    break;
                case 'c':
                case 'C':
                    clearOutput();
                    break;
            }
        });
    </script>
</body>
</html>
